import { ComponentProps } from '../../../types';

export interface SelectorAndTextInputValue {
  selected?: string;
  value?: string;
}

export interface SelectorAndTextInputOption {
  title: string;
  value: string;
  disabled?: boolean;
  placeholder?: string;
}

export interface SelectorAndTextInputProps extends ComponentProps<SelectorAndTextInputValue> {
  className?: string;
  placeholder?: string;
  options: SelectorAndTextInputOption[];
}
