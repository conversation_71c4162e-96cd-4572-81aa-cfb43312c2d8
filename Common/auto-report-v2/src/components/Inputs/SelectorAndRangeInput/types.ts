import { ComponentProps } from '../../../types';

export interface SelectorAndRangeInputValue {
  selected?: string;
  from?: number;
  to?: number;
}

export interface SelectorAndRangeInputOption {
  title: string;
  value: string;
  disabled?: boolean;
}

export interface SelectorAndRangeInputProps extends ComponentProps<SelectorAndRangeInputValue> {
  className?: string;
  fromPlaceholder?: string;
  toPlaceholder?: string;
  options: SelectorAndRangeInputOption[];
  noLabelSelect?: boolean;
}
