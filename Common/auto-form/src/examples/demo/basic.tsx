/**
 * 基本用法
 * @title 基本示例
 * @order 1
 */

import React from 'react';
import { createAutoForm } from '../..';
import { PageCard, Button, Collapse, Input } from '@mtfe/sjst-antdx';
import { Refresh } from '../../templates/refresh';
import { submitData, getData, queryOptions, queryCity, FormValues, gender, DataPool, Option } from './../api';
import { Context } from '../../type';

enum DateType {
  TIME,
  DATE,
  WEEK,
}

const handle = createAutoForm<DataPool, FormValues>({
  formProps: {
    initialValues: {
      name: 'test name'
    }
  },
  onInit: async c => {
    const [r, r2, r3] = await Promise.all([getData(), queryOptions(), queryCity()]);
    // 物料中需要异步拉取的值
    c.dataPool.options = r2 as Option[];
    c.dataPool.cities = r3 as DataPool['cities'];
    c.dataPool.dateOptions = [
      {
        name: '小时',
        value: DateType.TIME,
      },
      {
        name: '天',
        value: DateType.DATE,
      },
      {
        name: '周',
        value: DateType.WEEK,
      },
    ];
    // 返回表单回填值
    return r as FormValues;
  },
  properties: {
    name: {
      label: '姓名',
      required: true,
      extra: '补充说明文案',
      checkers: (v, cb) => {
        if (v?.length > 2) {
          cb('最长2个字');
          return;
        }
        cb();
      },
      material: {
        name: 'input',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    description: {
      label: '描述',
      help: '使用自定义物料及插槽',
      material: () => <Input />,
      slot: (dom: JSX.Element) => (
        <div style={{ textAlign: 'center' }}>
          前面自定义插入内容
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {dom}
            <div>右边插入内容</div>
          </div>
          后面自定义插入内容
        </div>
      ),
    },
    dateType: (ctx: Context<DataPool>) => ({
      label: '时间类型',
      required: true,
      material: {
        name: 'radio',
        dataSource: ctx.dataPool.dateOptions,
      },
    }),
    timeRange: {
      label: '时间范围',
      required: true,
      ifUpdate: ['dateType'],
      material: {
        name: 'time-range-picker',
      },
      wrapper: (i, dom) => {
        return i.getFieldValue('dateType') === DateType.TIME ? dom : null;
      },
    },
    date: {
      label: '日期',
      required: true,
      material: {
        name: 'date-picker',
      },
      wrapper: (i, dom) => {
        return i.getFieldValue('dateType') === DateType.DATE ? dom : null;
      },
    },
    dateRange: {
      label: '日期范围',
      required: true,
      material: {
        name: 'date-range-picker',
      },
    },
    week: {
      label: '周选择器',
      required: true,
      material: {
        name: 'week-picker',
      },
    },
    gender: {
      label: '性别',
      required: true,
      material: {
        name: 'radio',
        dataSource: gender,
      },
    },
    cities: ctx => ({
      label: '城市',
      required: true,
      material: {
        name: 'cascader',
        dataSource: ctx.dataPool.cities,
      },
    }),
    status: {
      label: '状态',
      required: true,
      material: {
        name: 'switch',
      },
    },
    group: ctx => ({
      label: '分组',
      material: {
        name: 'checkbox',
        dataSource: ctx.dataPool.options,
      },
    }),
  },
  onSubmit: async v => {
    await submitData();
  },
  // 这里template返回的自定义内容在form内侧
  template: (domMap, options) => {
    const { cities, status, ...restDom } = domMap;
    return (
      <>
        {...Object.values(restDom)}
        <Refresh ifUpdate={['name']}>{({ getFieldValue }) => `您输入的名称为：${getFieldValue('name')}`}</Refresh>
        <Collapse bordered={false}>
          <Collapse.Panel header={<a style={{ width: 200 }}>更多设置</a>} key="poi-area">
            {cities}
            {status}
          </Collapse.Panel>
        </Collapse>
      </>
    );
  },
  container: (form, options) => {
    // 这里包裹的div在Form组件外侧
    return (
      <PageCard
        bottom={
          <Button type="primary" onClick={options.submit}>
            提交
          </Button>
        }
      >
        {form}
      </PageCard>
    );
  },
});


export default handle.AutoForm;

