import { IRangePickerXProps } from '@mtfe/sjst-antdx-next';
import { ComponentProps } from '../../../types';

export type TimeValue = {
  /** 选择的起始时间 */
  start: number;
  /** 选择的结束时间 */
  end: number;
};

export type DatePickerQueryConfigType = {
  startKey?: string;
  endKey?: string;
};

export type DatePickerProps = IRangePickerXProps & ComponentProps<TimeValue> & {
  /**
   * Query 转换配置
   */
  queryConfig?: DatePickerQueryConfigType;
}
