import React from 'react';
import {
  Input,
} from 'antd';
import { Selector, Table } from '@mtfe/next-biz/src/components/Table';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  StaffSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: StaffSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as StaffSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    if (this.context.userService?.isHeadOffice()) {
      return <Input placeholder="请输入桌台查询" />;
    }
    return (
      <Selector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(table?: Table | Table[] | string) {
    if (typeof table === 'string') return table;
    return toArray(table)?.map(o => o.name).join('，') || '全部';
  }

  getValue(table: Table | Table[] | string | undefined) {
    if (typeof table === 'string') return table;
    return toArray(table)?.map(o => o.id);
  }
}
