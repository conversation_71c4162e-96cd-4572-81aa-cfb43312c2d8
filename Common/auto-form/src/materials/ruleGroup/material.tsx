import React from 'react';
import { CheckboxMaterial } from '../checkbox';
import { InputMaterial } from '../input';
import { RadioMaterial, } from '../radio';
import { SelectMaterial, } from '../select';
import { SwitchMaterial, } from '../switch';
import { CascaderMaterial, } from '../cascader';
import { TimePickerMaterial } from '../timepicker';
import { TimeRangePickerMaterial, } from '../timepicker/range';
import { DatePickerMaterial } from '../datepicker';
import { RangePickerMaterial } from '../datepicker/range';
import { WeekPickerMaterial } from '../datepicker/week';
import { MonthPickerMaterial } from '../datepicker/month';
import { PoiTreeMaterial } from '../poiTree';
import { BaseMaterials } from '..';

export const renderMaterialForRuleGroup = (props: BaseMaterials) => {
  const materialMap = {
    checkbox: CheckboxMaterial,
    input: InputMaterial,
    radio: RadioMaterial,
    select: SelectMaterial,
    switch: SwitchMaterial,
    cascader: CascaderMaterial,
    'time-picker': TimePickerMaterial,
    'time-range-picker': TimeRangePickerMaterial,
    'date-picker': DatePickerMaterial,
    'date-range-picker': RangePickerMaterial,
    'week-picker': WeekPickerMaterial,
    'month-picker': MonthPickerMaterial,
    'poi-tree-selector': PoiTreeMaterial,
  };
  // eslint-disable-next-line
  const Material: any = materialMap[props.name];
  return <Material {...props} />;
};

