import React from 'react';
import {
  DatePicker,
} from 'antd';
import { DatePickerProps } from 'antd/es/date-picker/interface';
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type DatePickerMaterialProps = {
  name: 'date-picker',
  props?: DatePickerProps,
};

type Props = DatePickerMaterialProps & ValueControl<number | undefined>;

export const DatePickerMaterial = (p: Props) => {
  const {
    value, onChange, name, props, ...otherProps
  } = p;
  return (
    <DatePicker
      {...otherProps}
      {...props}
      value={number2moment(value)}
      onChange={(e) => {
        onChange?.(moment2number(e));
      }}
    />
  );
};
