/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
  Event,
} from '@antv/g2';
import type Element from '@antv/g2/lib/geometry/element';
import Empty from './Empty';
import { PieChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasicChart, { Data } from './useBasicChart';
import formatter from '../../../common/format';
import { LinearGradientSet } from '../../../common/chartConst';
import useEllipsisText from '../../../common/useEllipsisText';
import './Pie.less';

const fontSizeMaps = {
  default: {
    label: 10,
  },
  small: {
    label: 10,
  },
  large: {
    label: 12,
  },
};

export default function PieChart(props: Props<PieChartProps>) {
  const {
    height,
    width,
    dim,
    value,
    size = 'default',
    customData,
    useAbs,
  } = props;
  const [timmer, setTimmer] = React.useState<NodeJS.Timeout>();
  const ellipsisText = useEllipsisText();
  const updateAnnotation = React.useCallback((chart: Chart, activedData: Record<string, string | number> | null) => {
    chart.annotation().clear(true);
    if (!activedData) return;
    const fill = 'rgb(219, 219, 224)';
    // const fill = 'transparent';
    const textAlign = 'center';
    const annotation = chart.annotation();
    const title = activedData[dim.field];
    // if (typeof title === 'string' && title.length > 10) {
    //   title = `${title.slice(0, 10)}……`;
    // }
    if (value.format === 'percent') {
      annotation
        .text({
          position: ['50%', '50%'],
          content: activedData[value.field],
          style: {
            fontSize: 36, fill, textAlign, lineWidth: 0, fontFamily: 'MTfin',
          },
          offsetY: -10,
          offsetX: -8,
        })
        .text({
          position: ['50%', '50%'],
          content: '%',
          style: {
            fontSize: 16, fill, textAlign, lineWidth: 0, fontFamily: 'MTfin',
          },
          offsetX: Math.max(String(activedData[value.field] || '').split('').reduce((w, c) => {
            if (c === '.') return w + 5;
            return w + 10;
          }, 0), 10),
          offsetY: -6,
        });
    } else {
      annotation
        .text({
          position: ['50%', '50%'],
          content: formatter(value.format, activedData[value.field]) || '',
          style: {
            fontSize: 36, fill, textAlign, lineWidth: 0, fontFamily: 'MTfin',
          },
          offsetY: -10,
        });
    }
    annotation.text({
      position: ['50%', '50%'],
      content: ellipsisText(String(title), 16, 80),
      style: {
        fontSize: 16,
        fill,
        textAlign,
        lineWidth: 0,
      },
      offsetY: 26,
    });
    chart.render();
  }, [value, dim]);

  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    const fontSizes = fontSizeMaps[size];
    chart.coordinate('theta', {
      radius: 0.8,
      innerRadius: 0.8,
    });
    chart.axis(false);
    chart.interaction('element-single-selected');
    chart
      .interval()
      .adjust('stack')
      .position(`${value.field}_abs`)
      .animate({
        appear: false,
      })
      .color(dim.field, LinearGradientSet);
    chart.tooltip(false);

    chart.legend({
      flipPage: true,
      maxWidth: 100,
      maxHeight: 160,
      position: 'right',
      marker: {
        symbol: 'square',
        spacing: 4,
      },
      itemName: {
        style: {
          fill: '#FFFFFF',
          fillOpacity: 0.65,
          fontSize: fontSizes.label,
        },
        // 空格占位，主要是让 legend 保持一致
        formatter: v => `${v}                                                                            `,
      },
    });

    chart.interaction('element-single-selected');
    chart.on('element:statechange', (ev: Event) => {
      const { state, stateStatus, element } = ev.gEvent.originalEvent as unknown as {
        state: string,
        stateStatus: boolean,
        element: Element,
      };
      if (state === 'selected') {
        const data = element.getData();
        if (stateStatus) {
          // 更新 Annotation
          updateAnnotation(chart, data);
        }
      }
    });
    return chart;
  }, [dim, value, size, updateAnnotation]);

  /** 自动刷新饼图 */
  const startAutoRefreshChart = React.useCallback((chart: Chart, data: Data) => {
    let activedIndex = 0;
    const refreshChart = () => {
      const interval = chart.geometries.find(g => g.type === 'interval');
      if (!interval) return;
      if (activedIndex >= interval.elements.length) activedIndex = 0;
      interval.elements.forEach((element, i) => {
        element.setState('selected', i === activedIndex);
      });
      // 自动切换分页
      // const legend = chart.getController('legend')?.getComponents()?.find(i => i.type === 'legend');
      // const { currentPageIndex, totalPagesCnt, onNavigationBack, onNavigationAfter } = legend?.component || {};
      // if (currentPageIndex && totalPagesCnt && totalPagesCnt > 1 && onNavigationBack && onNavigationAfter) {
      //   if (currentPageIndex < totalPagesCnt) {
      //     onNavigationAfter?.();
      //   } else {
      //     console.log('重置分页')
      //   }
      // }
      activedIndex++;
    };

    setTimmer((oldTimmer) => {
      if (oldTimmer) {
        clearInterval(oldTimmer);
      } else {
        timmer ?? clearInterval(oldTimmer);
      }
      setTimeout(refreshChart, 0);
      return setInterval(refreshChart, 3000);
    });
  }, [setTimmer, dim, value]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    data.forEach(item => {
      // 存一个绝对值
      const absField = `${value.field}_abs`;
      item[absField] = useAbs ? Math.abs(item[value.field] as number) : item[value.field];
    })
    chart.data(data);
    startAutoRefreshChart(chart, data);
  }, [value, startAutoRefreshChart, useAbs]);

  const dims = React.useMemo(() => [dim.field], [dim]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
    customData,
    empty: <Empty />,
    className: 'auto-report-pie-chart',
  });

  return dom;
}

registorComponent('PieChart', PieChart);
