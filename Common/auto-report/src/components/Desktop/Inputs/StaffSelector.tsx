import React from 'react';
import ReportStaffSelector, { SimplifiedStaff } from '@mtfe/next-biz/src/components/Staff/ReportStaffSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  StaffSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: StaffSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as StaffSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportStaffSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(staff?: SimplifiedStaff | SimplifiedStaff[]) {
    return toArray(staff)?.map(o => o.name).join('，') || '全部';
  }

  getValue(staff: SimplifiedStaff | SimplifiedStaff[] | undefined) {
    if (this.config.mode === 'staff') {
      return toArray(staff)?.map(o => o.id);
    } else {
      return toArray(staff)?.map(o => o.acctId);
    }
  }
}
