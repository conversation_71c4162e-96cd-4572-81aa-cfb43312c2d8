/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { PoiTreeSelectorV2 } from '@mtfe/next-biz/src/components/Org/Poi';
import { Org, OrgType, DepartType } from '@mtfe/next-biz/src/services/org/types';
import {
  Filter,
  PoiFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';
import { serialize, deserialize } from './utils';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: PoiFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as PoiFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  getDefaultValue() {
    if ('defaultValue' in this.config) {
      return this.config.defaultValue;
    }
    return [];
  }

  serialize(org: Org) {
    return serialize([org.id, org.poiId || 0, org.name]);
  }

  deserialize(str: string) {
    const [id, poiId, name] = deserialize(str);
    return { id, poiId, name };
  }

  orgFilter = (org: Org) => {
    if (org.parentOrgType === OrgType.Poi) return false;
    if (org.type === OrgType.Poi) return true;
    if (
      org.type === OrgType.Department
      && (org.parentOrgType === OrgType.HeadOffice || org.parentOrgType === OrgType.Branch)
      && org.departType?.includes(DepartType.Sales)
    ) return true;
    return false;
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <PoiTreeSelectorV2
        size={props.size}
        useEntity
        useMultiplePopSearch
        multiple={!!multiple}
        placeholder="全部"
        options={{
          forceAuthControl: true,
          orgTypes: [OrgType.Poi, OrgType.Department],
          orgFilter: this.orgFilter,
        }}
        {...otherProps}
      />
    );
  }

  getDisplayValue(org?: Org | Org[]) {
    return toArray(org)?.map(o => o.name).join('，') || '全部';
  }

  getQuery(org: Org | Org[] | undefined, query: any) {
    const { userService } = this.context;
    let empty = false;
    if (Array.isArray(org) && !org.length) {
      empty = true;
    } else if (!org) {
      empty = true;
    }
    if (empty && userService?.isHeadOffice()) {
      query.poiIds = userService.account.managedPoiIds;
    }
    query.poiIds = toArray(org)?.map(o => o.poiId || 0).filter(Boolean);
    query.orgIds = toArray(org)?.map(o => o.id || 0).filter(Boolean);
    return query;
  }
}
