import React from 'react';
import { BrandSelector, Brand } from '@mtfe/next-biz/src/components/Brand';
import {
  Filter,
  BrandSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: BrandSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as BrandSelectorFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <BrandSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
      />
    );
  }

  getDisplayValue(brand?: Brand | Brand[]) {
    return toArray(brand)?.map(o => o.name).join('，') || '全部';
  }

  getValue(brand: Brand | Brand[] | undefined) {
    return toArray(brand)?.map(o => o.id);
  }
}
