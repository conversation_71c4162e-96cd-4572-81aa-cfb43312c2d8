/* eslint-disable @typescript-eslint/no-explicit-any */
import { uniq, clone, isPlainObject } from 'lodash';
import React from 'react';
import deepEqual from 'deep-equal';
import memoize from 'memoize-one';
import { Store } from '@mtfe/auto-base';
import {
  ChildrenComponent,
  ComponentStatus,
  ConditionType,
  Config,
  ObjectRefType,
  ObjectRefValueType,
  ReportQuery,
  ReportValueType,
  RuleCompiler,
} from '../../../types';
import { EnvType } from '../../../contexts';
import { isBasicType, isReactNode } from '../../../helper/utils';

export const queryKeyEqual = (a: string[], b: string[]) =>
  a.length === b.length &&
  a.every((v) => b.includes(v)) &&
  b.every((v) => a.includes(v));

// 规则处理函数
function ruleProcessor<StateType>(
  config: Config | any,
  conditionValue: ConditionType<StateType>
): Config | any {
  if (isBasicType(config) || isReactNode(config)) {
    return config;
  }
  if (Array.isArray(config)) {
    return config.map((v) => ruleProcessor(v, conditionValue)).filter(Boolean);
  }
  if (isPlainObject(config)) {
    // customAutoRules 是极端情况下提供的接口，一般不建议使用
    // eslint-disable-next-line
    let { autoRules, customAutoRules, ...otherConfig } = config || {};
    autoRules?.forEach((rule: RuleCompiler<StateType>) => {
      const _query = clone(conditionValue.query || {});
      Object.keys(rule?.conditions?.query || {}).forEach((key) => {
        const v = rule?.conditions?.query?.[key];
        if (v !== undefined) {
          if (typeof v === 'function') {
            _query[key] = v(_query[key], conditionValue)
              ? _query[key]
              : '@___$$$$___@'; // 这里这个字符串用来判定不相等就行
          } else {
            _query[key] = v;
          }
        }
      });

      const conditions = {
        ...conditionValue,
        ...rule?.conditions,
        query: _query,
      };
      if (deepEqual(conditions, conditionValue, { strict: true })) {
        if (rule.merges) {
          otherConfig = { ...otherConfig, ...rule.merges };
        }
        if (rule.excludes?.length) {
          rule.excludes.forEach((v) => {
            otherConfig[v] = undefined;
          });
        }
        if (rule.delete) {
          otherConfig = undefined;
        }
      }
    });

    if (customAutoRules && typeof customAutoRules === 'function') {
      otherConfig = customAutoRules(conditionValue, { ...otherConfig });
    }

    if (!otherConfig) {
      return otherConfig;
    }
    const keys = Object.keys(otherConfig);
    keys.forEach((k) => {
      otherConfig[k] = ruleProcessor(otherConfig[k], conditionValue);
    });
    return otherConfig;
  }
  return config;
}

// 获取 config 对应的 默认 query
function configProcessorByDefaultQuery(config: Config | any): ReportQuery {
  if (isBasicType(config) || isReactNode(config)) {
    return {};
  }
  if (Array.isArray(config)) {
    return config
      .reduce(
        (pervious, current) => ({
          ...pervious,
          ...configProcessorByDefaultQuery(current),
        }),
        {}
      );
  }
  if (isPlainObject(config)) {
    const { dataKey, defaultValue, ...otherConfig } = config || {};
    const q = dataKey ? {
      [dataKey]: defaultValue
    } : {};
    const keys = Object.keys(otherConfig);
    return keys
      .reduce(
        (pervious, current) => ({
          ...pervious,
          ...configProcessorByDefaultQuery(otherConfig[current]),
        }),
        q
      );
  }
  return {};
}

function ruleProcessorQuery(
  env?: EnvType,
  autoState?: any,
  query?: ReportQuery,
  children?: ChildrenComponent,
  count: number = 0,
): Config {
  const conditionValue = {
    os: env?.os,
    isTaxOpen: env?.isTaxOpen,
    isChain: env?.isChain,
    ...autoState,
    query,
  };
  const config = ruleProcessor(children, conditionValue);
  const configDefaultQuery = configProcessorByDefaultQuery(config);
  const queryKeys = Object.keys(query || {});
  const configDataKeys = Object.keys(configDefaultQuery);
  if (count < 5 && !queryKeyEqual(uniq(queryKeys), uniq(configDataKeys))) {
    return ruleProcessorQuery(env, autoState, {
      ...configDefaultQuery,
      ...query
    }, children, count + 1);
  }
  return config;
}

export const getQueryByRefList = (
  refList: ObjectRefValueType<any>[],
  mergeQuery?: ReportQuery
) =>
  refList.reduce((previous, queryRef) => {
    if (queryRef.dataKey) {
      previous[queryRef.dataKey] =
        mergeQuery?.[queryRef.dataKey] || queryRef.value;
    }
    return previous;
  }, {} as ReportQuery);


export const memoizeRuleProcessorQuery = memoize(ruleProcessorQuery,
  // 是否使用缓存的计算, conditionValue 做 deepEqual 判断
  (a, b) => {
    const [oldEnv, oldAutoState, oldQuery, oldChildren] = a;
    const [env, autoState, query, children] = b;
    const isConditionValueEqual = deepEqual(
      {
        os: oldEnv?.os,
        isTaxOpen: oldEnv?.isTaxOpen,
        isChain: oldEnv?.isChain,
        ...oldAutoState,
        query: oldQuery,
      },
      {
        os: env?.os,
        isTaxOpen: env?.isTaxOpen,
        isChain: env?.isChain,
        ...autoState,
        query,
      }
    );
    return isConditionValueEqual && oldChildren === children;
  }
);

// 配置规则实现
export function useRules<DataType>(
  env?: EnvType,
  autoState?: any,
  refObject?: ObjectRefType<ReportValueType<DataType>>,
  children?: ChildrenComponent,
  queryPersistenceValue?: ReportQuery
) {
  const refValueList = refObject?.getRefList();

  const queryRefList = React.useMemo(
    () =>
      (refValueList || []).filter(
        (v) => v.dataKey && v.path.slice(0, -1).includes(refObject?.id || '')
      ) || [],
    [refValueList]
  );

  const [state, setState] = React.useState<{
    query?: ReportQuery;
    config?: Config;
    status?: ComponentStatus;
  }>({});

  React.useLayoutEffect(() => {
    const doneQueryRefList = queryRefList.filter((v) => v.status === 'done');
    setState((preState) => {
      const query = getQueryByRefList(doneQueryRefList, queryPersistenceValue);
      const config = memoizeRuleProcessorQuery(env, autoState, query, children);
      const configDefaultQuery = configProcessorByDefaultQuery(config);
      const queryKeys = Object.keys(query || {});
      const configDataKeys = Object.keys(configDefaultQuery);
      const status =
        queryRefList.length === doneQueryRefList.length &&
        queryKeyEqual(uniq(queryKeys), uniq(configDataKeys))
          ? 'done'
          : ('pending' as ComponentStatus);
      if (!deepEqual(preState.config, config) || preState.status !== status) {
        return {
          query,
          config,
          status,
        };
      }
      return preState;
    });
  }, [env, autoState, queryRefList, children]);

  return { queryRefList, state };
}

export function useRulesX(
  id: string,
  children?: ChildrenComponent,
  queryPersistenceValue?: React.MutableRefObject<ReportQuery | undefined>
) {
  return (store: Store) => {
    const autoState = store.state.$root;
    const { env } = store.getters.$global.state;
    const refValueList = store.state.$state;
    const queryRefList =
      (refValueList || []).filter(
        (v: any) => v.dataKey && v.path.slice(0, -1).includes(id || '')
      ) || [];

    const doneQueryRefList = queryRefList.filter(
      (v: any) => v.status === 'done'
    );
    const query = getQueryByRefList(
      doneQueryRefList as any[],
      queryPersistenceValue?.current
    );

    const config = memoizeRuleProcessorQuery(env, autoState, query, children);
    const queryKeys = Object.keys(query || {});
    const configDefaultQuery = configProcessorByDefaultQuery(config);
    const configDataKeys = Object.keys(configDefaultQuery);
    const status =
      queryRefList.length === doneQueryRefList.length &&
      queryKeyEqual(uniq(queryKeys), uniq(configDataKeys))
        ? 'done'
        : ('pending' as ComponentStatus);
    return {
      queryRefList,
      state: {
        query,
        config,
        status,
      },
    };
  };
}

export default useRules;
