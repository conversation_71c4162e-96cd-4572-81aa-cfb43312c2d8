import React from 'react';
import {
  Input,
} from 'antd';
import { InputProps } from 'antd/es/input';
import { ValueControl } from '../types';

export type InputMaterialProps = {
  name: 'input',
  props?: InputProps,
};

type Props = InputMaterialProps & ValueControl<string>;

export const InputMaterial = (p: Props) => {
  const {
    value, onChange, name, props, ...otherProps
  } = p;
  return (
    <Input
      {...otherProps}
      {...props}
      value={value}
      onChange={(e) => {
        p?.onChange?.(e.target.value);
      }}
    />
  );
};
