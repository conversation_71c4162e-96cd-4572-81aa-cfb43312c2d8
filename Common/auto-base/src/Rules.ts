import { isFunction, isArray } from 'lodash';
import { StateObject } from './Contexts';
import { isReactNode, getValueByPath } from './helper';
import { Config, ConfigProps } from './types';

const ExpRE = /{\{([^}]+)\}\}/g;

function complieExpression<ValueType>(
  value: string,
  stateObject: StateObject<ValueType>
) {
  return value.replace(ExpRE, (replacement, group) => {
    const path = group.split('.');
    if (path.length) {
      return getValueByPath(path, stateObject);
    }
    return replacement;
  });
}

export type LinkageFunctionType<ValueType> = (config: Config, stateObject: StateObject<ValueType>) => Config;

export class Rules {
  private _linkages: Map<string, LinkageFunctionType<any>> = new Map();

  // 表达式处理
  private _complieExpression<ValueType>(
    config: Config,
    stateObject: StateObject<ValueType>
  ) {
    if (config?.props && typeof config.props === 'object') {
      const keys = Object.keys(config.props);
      keys.forEach((k) => {
        const v = config.props?.[k];
        if (v && typeof v === 'string') {
          (config.props as ConfigProps)[k] = complieExpression(v, stateObject);
        }
      });
    }
    return config;
  }

  addLinkage<ValueType>(name: string, l: LinkageFunctionType<ValueType>) {
    this._linkages.set(name, l);
  }

  complie<ValueType>(
    config: Config,
    stateObject: StateObject<ValueType>
  ): Config {
    if (isReactNode(config)) {
      return config;
    }

    // 函数处理
    if (isFunction(config.props)) {
      config = {
        ...config,
        props: config.props(stateObject),
      };
    }

    // 联动协议
    if (config.linkages) {

      const linkages = isArray(config.linkages) ? config.linkages : [config.linkages];
      for (let i = 0; i < linkages.length; i++) {
        const linkage = linkages[i];
        const l = this._linkages.get(linkage.type);
        if (l) {
          config = l({ ...config, props: { ...config.props } }, stateObject);
        }
        if (config === undefined) {
          return config;
        }
      }
    }
    return this._complieExpression({ ...config, props: { ...config.props } }, stateObject);
  }
}

export default Rules;
