import React from 'react';
import { ReportGoodsSpecSelector } from '@mtfe/next-biz/src/components/GoodsSpec';
import {
  Filter,
  GoodsSpecSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: GoodsSpecSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as GoodsSpecSelectorFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportGoodsSpecSelector
        size={props.size}
        useMultiplePopSearch
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
      />
    );
  }

  getDisplayValue(value: { name: string }[]) {
    return toArray(value)?.map(v => v.name).join('，') || '全部';
  }

  getValue(value: { name: string }[]) {
    return toArray(value)?.map(v => v.name);
  }
}
