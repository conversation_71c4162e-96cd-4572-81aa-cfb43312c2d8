import React from 'react';
import { But<PERSON>, Modal } from '@mtfe/sjst-antdx-next';
import { getTableExplain } from './helper';
import withAdapter from '../../../core/withAdapter';
import { dataContext } from '../../../contexts';
import { DescriptionProps } from './types';

interface ExplainProps {
  title: string;
  content: string;
}

function Description(props: DescriptionProps) {
  const { updateStatus, title, content, width, tableExplainKey='', bid, baseRefObject } = props;

  const { env } = React.useContext(dataContext);

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const [isVisiable, setVisiable] = React.useState(false);
  const [tableExplain, setTableExplain] = React.useState<ExplainProps>({title: '', content: ''});
  const _getTableExplain = React.useCallback(async() => {
    const data = await getTableExplain();
    setTableExplain(data?.[tableExplainKey]);
  }, []);

  const toggleDescription = React.useCallback((isClick: boolean) => {
    if(isClick && tableExplainKey) {
      _getTableExplain();
    }
    setVisiable((v) => !v);
  }, []);

  const _content = React.useMemo(() => {
    if (tableExplainKey) {
      return <div dangerouslySetInnerHTML={{ __html: tableExplain?.content }} />;
    } else {
      if (!content) {
        return null;
      }
      if (typeof content === 'string') {
        return <div dangerouslySetInnerHTML={{ __html: content }} />;
      }
      if (typeof content === 'function') {
        return content({ close: () => setVisiable(false) });
      }
      return content;
    }
  }, [content, tableExplain]);

  if (!_content) {
    return null;
  }

  const modal = React.useMemo(() => {
    return (
      <Modal
        title={title || tableExplain?.title || '报表说明'}
        cancelButtonProps={{ hidden: true }}
        visible={isVisiable}
        onCancel={() => toggleDescription(false)}
        onOk={() => toggleDescription(false)}
        maskClosable={false}
        width={width}
        destroyOnClose
        className="report-explain-modal"
      >
        {_content}
      </Modal>
    );
  }, [title, isVisiable, width, _content]);

  return (
    <>
      <Button
        type={env?.os === 'pc' ? 'link' : undefined}
        icon="question-circle"
        onClick={() => {
          baseRefObject?.analytics.moduleClick(bid);
          toggleDescription(true);
        }}
      >
        报表说明
      </Button>
      {modal}
    </>
  );
}

export default withAdapter(Description, { name: 'Description' });
