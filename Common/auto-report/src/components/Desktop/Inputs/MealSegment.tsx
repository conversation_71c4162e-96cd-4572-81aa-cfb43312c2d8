import React from 'react';
import ReportMealSegmentSelector, { Segment } from '@mtfe/next-biz/src/components/MealSegment/ReportMealSegmentSelector';
import {
  Filter,
  MealSegmentSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: MealSegmentSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as MealSegmentSelectorFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportMealSegmentSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
      />
    );
  }

  getDisplayValue(segment?: Segment | Segment[]) {
    return toArray(segment)?.map(o => o.name).join('，') || '全部';
  }

  getValue(segment: Segment | Segment[] | undefined) {
    return toArray(segment)?.map(o => o.id);
  }
}
