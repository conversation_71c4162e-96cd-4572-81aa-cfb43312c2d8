/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable import/no-cycle */
import type { ColProps } from 'antd/lib/col';
import type { RowProps } from 'antd/lib/row';
import type {
  Chart,
} from '@antv/g2';
import type { ColumnProps } from 'antd/es/table/interface';
import type { Filter, Option } from './filter';
import type { ReportContextValue } from './context';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';

/**
 * Basic Type
 */
export type FieldName = string;

export type FieldFormat = 'timestamp[date]' | 'timestamp[time]' | 'currency' | 'percent' | 'number' | 'date' | 'month';

export type Cell = {
  data: any,
  rowSpan: number,
  colSpan: number,
  bold?: boolean,
}

export type Row = {
  id?: number | string,
  children?: Row[],
  isTotalRow?: string | boolean,
} & {
  [k: string]: Cell | undefined,
};

export type CustomFormat = (v: number | string, row?: Row) => (React.ReactNode | null);

export type Formula = string;

export type Props<T> = {
  children?: Array<Config | string | number | Omit<React.ReactElement, 'key'> | null> | React.ReactElement | null,
  view?: string,
  version?: string,
  customExportParams?: (params: any) => any,
} & T;

export type Component<T extends string, A = {}> = {
  type: T,
  props?: Props<A>,
}

/**
 * Report Controller Type
 */
export type ReportHeaderProps = {
  title?: string,
  description?: string | React.ReactElement,
  downloadPermissionCode?: number,
  /** 下载埋点 bid */
  downloadBid?: string,
  /** 设置为常用埋点 bid */
  bookmarkBid?: string,
  /** 报表说明 bid */
  explainBid?: string,
  isSubTitle?: boolean,
  /** 是否展示「导出」按钮，默认为 true **/
  showExportButton?: boolean,
  /** 报表说明标题 */
  explainTitle?: string,
  // 自定义按钮
  extraButtons?: React.ReactNode;
  // 报表说明自定义key
  tableExplainKey?: string;
  hideStar?: boolean;
};

export type ReportHeaderConfig = Component<'ReportHeader', ReportHeaderProps>;

export type ReportFilterProps = {
  /** 不渲染 DOM 节点 */
  hidden?: boolean,
  /** 重置埋点 bid */
  resetBid?: string,
  /** 查询埋点 bid */
  submitBid?: string,
  /** 展开埋点 bid */
  expandBid?: string,
  size?: 'large' | 'default' | 'small',
  filters: Filter[],
  /** 实时更新，不需要用户点击「查询」 */
  realTimeUpdate?: boolean,
  /** 查询条件底部补充说明 */
  extraTips?: React.ReactNode,
  // 最大显示几个查询条件
  maxQueryCount?: number,

  otherParams?: {
    isPos?: boolean,
    description?: string,
    extraButtons?: React.ReactNode;
  }
};
export type ReportFilterConfig = Component<'ReportFilter', ReportFilterProps>;

export type ReportProps = {
  api?: string,
  version?: string,
  view?: string,
  title?: string,
  /** 页面埋点 */
  cid?: string,
  /** 是否使用结算时间做查询，默认为 true */
  useClearTime?: boolean,
  /** 报表是否会渲染 ReportFilter */
  haveFilter?: boolean,
  /** 使用旧的接口请求方式 */
  useBaseQuery?: boolean,
  queryPersistence?: any,
  /** 自定义报表逻辑 */
  useReport?: (props: Props<ReportProps>, haveFilter: boolean) => ReportContextValue,
  transformExportParams?: (<T>(params: T) => T),
  permissionCode?: number; // 页面的权限code
  notCancelDefaultQuery?: boolean;
  businessModuleId?: BusinessModule;
  preventPV?: boolean; // 不需要执行pageView
}

export type ReportConfig = Component<'Report', ReportProps>;

export type ReportControllerConfig = ReportConfig | ReportFilterConfig | ReportHeaderConfig;

export type Column = ColumnProps<Row> & { culcWidth?: number, treeColumn?: boolean, field?: string, alternate?: string };

/**
 * Report Display Type
 */
export type TableColumnConfig = {
  /** 字段说明文本 */
  help?: string,
  /** 字段名称 */
  label: string,
  /** 子字段名称 **/
  subLabel?: string,
  width?: number,
  /** 字段 */
  field?: FieldName,
  /** 子字段，用于展示树表 */
  subField?: FieldName,
  /** 备用字段，如果 field 字段不存在，会显示 alternate 字段 */
  alternate?: FieldName,
  /** 左侧固定 */
  fixed?: boolean,
  /** 字段格式化方式，支持自定义格式化 */
  format?: FieldFormat | CustomFormat,
  /** 子列，用于列单元格合并，使用方式和 Antd 一致 */
  children?: TableColumnConfig[],
  /** 别名，比如分组时用 poiId, 展示时显示 poiName, 这时 poiName 就可以设置为 alias */
  alias?: FieldName,
  /** 只在开启价税分离后展示 */
  showOnPriceTaxEnable?: boolean,
  /** 废弃字段 */
  show?: Formula,
  /** 是否可排序 */
  sortable?: boolean,
  /** 排序字段，默认为 column.field */
  sortField?: string,
  /** 是否为维度，在做多统计维度时用于判断是否要隐藏 */
  isDim?: boolean,
  /** 列默认是否显示 */
  defaultEnabled?: boolean,
  /** 列标题筛选，传给后端的字段 */
  filterField?: string,
  /** 列标题筛选的候选值 */
  filters?: Option[],
  /** 列标题筛选，单选或多选 */
  filterMultiple?: boolean,
  /** 此列是否展示分组合计, 默认为 true */
  showTotal?: boolean,
  /** 只有总部可见，或只有门店可见 */
  loginType?: 'chain' | 'poi',
  /**
   * 在字段设置中是否可隐藏、可排序
   * - 如果是维度的话
   *    - false: 不可排序，不可隐藏（默认）
   *    - true: 不可排序，可隐藏
   * - 如果为指标的话
   *    - false: 不可排序，不可隐藏
   *    - true: 可排序，可隐藏 (默认)
   */
  configurable?: boolean,
  /**
   * 自定义转换函数，可以自定义转换 antd table 的 columns 属性。
   * 增加属性或者修改某些列的行为。
   */
  transform?: (column: Column) => Column | null,
  /**
   * 只在父列为某几种维度下的时候显示，只有作为动态列的子列时有效。
   * 如下面表头，销量只在收银 POS 维度下展示：
   * | 收银 POS    | 收银员点餐   | 手机点餐   |
   * |------------|------------|-----------|
   * | 销量 | 金额 |     销量    |    销量    |
   * |------------|------------|-----------|
   *
   * 对应的配置可以描述为：
   * ```
   * const columns = {
   *    label: '收银渠道',
   *    field: 'channel',
   *    children: [{
   *      label: '销量',
   *      field: 'count',
   *      showWithParentDims: ['收银 POS'],
   *    }, {
   *      label: '金额',
   *      filed: 'amount',
   *    }]
   * }
   * ```
   */
  showWithParentDims?: string[],
  /** 功能如 showWithParentDims */
  hideWithParentDims?: string[],
}

export type ReportTableProps = {
  /** 显示序号列 */
  showIndex?: boolean,
  /** 显示分页 */
  pagination?: boolean,
  pageSize?: number,
  /** 列配置 */
  columns: TableColumnConfig[],
  /** 全屏埋点 bid */
  fullscreenBid?: string,
  /** 是否显示「字段设置」，默认为 true */
  showColumnSorter?: boolean,
  businessModuleId?: number,
  isChainReport?: boolean,
}

export type ReportTableConfig = Component<'ReportTable', ReportTableProps>;

export type Field = {
  label: string,
  field: string,
  format?: FieldFormat,
  index?: number, // 在需要排序的地方，用来标记顺序
}

export type ChartData = Record<string, string | number>[];

export type BasicChartProps = {
  customRenderer?: (chart: Chart) => void,
  customLoadData?: (chart: Chart, data: ChartData | null) => void,
}

export type LineChartProps = {
  height?: number | string,
  width?: number | string,
  x: Field,
  y: Field,
  legend?: Field,
  showArea?: boolean,
  y1?: Field,
  lineColors?: string[],
  areaColors?: string[],
  parseData?: (data: ChartData | null) => ChartData,
} & BasicChartProps;

export type LineChartConfig = Component<'LineChart', LineChartProps>;

export type ColumnChartProps = {
  height?: number | string,
  width?: number | string,
  x: Field,
  y: Field,
  legend?: Field,
  y1?: Field,
  parseData?: (data: ChartData | null) => ChartData,
} & BasicChartProps;

export type ColumnChartConfig = Component<'ColumnChart', ColumnChartProps>;

export type FontSizeType = 'large' | 'default' | 'small';

export type BarChartProps = {
  height?: number | string,
  width?: number | string,
  x: Field,
  x1?: Field,
  y: Field,
  legend?: Field,
  y1?: Field,
  style?: React.CSSProperties,
  pageSize?: number,
  totalPage?: number,
  rank?: boolean,
  size?: FontSizeType,
  customData?: ChartData,
} & BasicChartProps;

export type BarChartConfig = Component<'BarChart', BarChartProps>;

export type PieChartProps = {
  height?: number | string,
  width?: number | string,
  dim: Field,
  value: Field,
  percent?: Field,
  style?: React.CSSProperties,
  size?: FontSizeType,
  customData?: ChartData,
  useAbs?: boolean, // 是否使用绝对值绘图
} & BasicChartProps;

export type PieChartConfig = Component<'PieChart', PieChartProps>;

export type BubbleChartProps = {
  height?: number | string,
  width?: number | string,
  x: Field,
  y: Field,
  size: Field,
  legend?: Field,
  style?: React.CSSProperties,
} & BasicChartProps;

export type BubbleChartConfig = Component<'BubbleChart', BubbleChartProps>;

export type ChinaMapChartProps = {
  height?: number | string,
  width?: number | string,
  value: Field,
  province: Field,
  style?: React.CSSProperties,
} & BasicChartProps;

export type ChinaMapChartConfig = Component<'ChinaMapChart', ChinaMapChartProps>

export type ReportDispalyConfig = ReportTableConfig | LineChartConfig | ColumnChartConfig | PieChartConfig | BarChartConfig | BubbleChartConfig | ChinaMapChartConfig;

/**
 * Report Layout Type
 */
export type ScrollerProps = {
  isFixed?: boolean;
  padding?: string;
  direction?: 'vertical' | 'horizontal' | 'both';
};

export type ScrollerConfig = Component<'Scroller', ScrollerProps>;

export type ContainerProps = React.CSSProperties & {
  className?: string,
};

export type ContainerConfig = Component<'Container', ContainerProps>;

export { ColProps, RowProps };

export type ColConfig = Component<'Col', ColProps>;

export type RowConfig = Component<'Row', ColProps>;

export type CardProps = {
  header?: string | ComponentConfig,
  actions?: ComponentConfig,
}

export type CardConfig = Component<'Card', CardProps>;

export type TabProps = {
  title: string,
  key?: string,
} | {
  key: string,
  title: ComponentConfig,
}

export type TabConfig = Component<'Tab', TabProps>;

export type TabsProps = {
  defaultActiveKey?: string,
  tabs: TabConfig[],
}
export type TabsConfig = Component<'Tabs', TabsProps>;

export type ReportLayoutConfig = ScrollerConfig | ContainerConfig | ColConfig | RowConfig | CardConfig | TabsConfig | TabConfig;

/** Report Widget Type */

export type TitleProps = {
  size?: 'large' | 'medium' | 'small',
  hr?: boolean,
  decoration?: boolean,
}

export type TitleConfig = Component<'Title', TitleProps>;

export type SpinProps = {

}

export type SpinConfig = Component<'Spin', SpinProps>;

export type ReportWidgetConfig = TitleConfig;

// 展示svg各个方向 left表示展示左边上下2个角,以此类推
export enum ShowSvgDirectionType {
  left=1,
  right,
  top,
  bottom,
  none=9999,
}

export enum hideBorderType {
  left=1,
  right,
  top,
  bottom,
  leftAndRight,
  none=9999,
}

/** Report Screen Component Type */
export type ScreenBlockProps = ContainerProps & {
  type: number,
  cornerSize?: number,
  title?: TitleConfig,
  blockKey?: string,
  onSelected?: (value: string) => void,
  hideLine?: boolean, // 是否隐藏块的线条
  svgDirection?: number, //svg 各个方向
  hideBorder?: number, // 隐藏某一个或者多个方向的边线
};

export type ScreenBlockConfig = Component<'ScreenBlock', ScreenBlockProps>;

export type ScreenHeaderProps = ContainerProps & {
  type: number,
  title: string,
};

export type ScreenHeaderConfig = Component<'ScreenHeader', ScreenHeaderProps>;

export type ReportScreenConfig = ScreenBlockConfig | ScreenHeaderConfig;
/**
 * 类型汇总
 */
export type ComponentConfig = ReportControllerConfig | ReportDispalyConfig | ReportLayoutConfig | ReportWidgetConfig | ReportScreenConfig;

export type ViewConfig = {
  version: string,
  view: string,
};
export type Config = ViewConfig | ComponentConfig;
