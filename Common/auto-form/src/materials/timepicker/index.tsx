import React from 'react';
import {
  TimePicker,
} from 'antd';
import { TimePickerProps } from 'antd/es/time-picker';
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type TimePickerMaterialProps = {
  name: 'time-picker',
  props?: TimePickerProps,
};

type Props = TimePickerMaterialProps & ValueControl<number | undefined>;

export const TimePickerMaterial = (p: Props) => {
  const {
    value, onChange, name, props, ...otherProps
  } = p;
  return (
    <TimePicker
      {...otherProps}
      {...props}
      value={number2moment(value)}
      onChange={(e) => {
        onChange?.(moment2number(e));
      }}
    />
  );
};
