import React from 'react';
import {
  DatePicker,
} from 'antd';
import { MonthPickerProps } from 'antd/es/date-picker/interface';
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type MonthPickerMaterialProps = {
  name: 'month-picker',
  props?: MonthPickerProps,
};

type Value = number | undefined;

type Props = MonthPickerMaterialProps & ValueControl<Value>;

const { MonthPicker } = DatePicker;

export const MonthPickerMaterial = (p: Props) => {
  const {
    value, onChange, name, props, ...otherProps
  } = p;
  return (
    <MonthPicker
      {...otherProps}
      {...props}
      value={number2moment(value)}
      onChange={(e) => {
        onChange?.(moment2number(e));
      }}
    />
  );
};
