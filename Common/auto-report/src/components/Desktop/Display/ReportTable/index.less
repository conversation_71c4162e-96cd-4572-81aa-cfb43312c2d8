:root .auto-report-table{
  flex: initial; // 设置为 flex 1 的话，会导致 empty placeholder 位置错误
  margin-top: 0 !important;
  padding-top: 0 !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  overflow: hidden;
 
  .ant-table-row-level-1, .ant-table-row-level-2, .ant-table-row-level-3{
    color: #999;
  }
 
  td.fixed-width{
    text-overflow: initial;
    overflow: initial;

    .ant-table-row-indent, button {
      margin-top: 2px;
      vertical-align: top;
    }
    
    .td-inner {
      white-space: normal;
    }
  }
 
  td, th {
    text-overflow: ellipsis;
    overflow: hidden;
  }
 
  .ant-pagination{
    text-align: right;
    margin: 16px 0;
    position: static;
  }
  td.bold{
    font-weight: 500;
    color: #333;
    background: #fafafa !important;
  }
  // thead.fixed tr:first-child th{
  //   border-top: 1px solid #e2e2e2;
  // }
 
  &.shrink{
    .ant-table{
      border: 0;
    }
    .ant-table-pagination{
      margin-right: 16px;
    }
  }
  td{
    white-space: nowrap;
  }
  .ant-tablex{
    flex-direction: column;
  }
  .ant-table-wrapper{
    display: block;
    overflow: hidden;
    flex: 1;
  }
  .ant-spin-nested-loading{
    width: 100%;
    max-height: 100%;
    display: flex;
    flex: 1;
  }
  .ant-tablex{
    flex: 1;
    display: flex;
    overflow: hidden;
  }
  .ant-spin-container{
    width: 100%;
    display: flex;
    flex: 1;
  }
  .ant-table {
    display: block;
    overflow-y: hidden;
    overflow-x: auto;
    width: 100%;
    max-height: 100%; 
  }
  .ant-table-body{
    overflow: initial !important;
  }
  .ant-table-scroll, .ant-table-content, .ant-table-body{
    // width: fit-content;
    min-width: 100%;
  }
  // .ant-table-content{
  //   // position: relative;
  // }
  .ant-table-placeholder{
    height: 168px;
    position: static;
    .ant-empty{
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  tbody>tr.total-row:last-child {
    will-change: transform;
    transition: none;
    td{
      border-top: 1px solid transparent;
    }
  }

  .report-footer-fixed {
    tbody>tr.total-row:last-child {
      td{
        border-color: rgb(232, 232, 232);

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: 0;
          right: 0;
          top: 100%;
          height: 100px;
          background: #fff;
        }
      }
    }
  }

  thead{
    will-change: transform; 
    transition: none;
  }
  .ant-table-thead{
    tr:first-child th{
      border-top: 1px solid transparent;
    }
  }

  .report-header-fixed {
    .ant-table-thead{
      tr:first-child th{
        border-color: rgb(232, 232, 232);
      }
    }
  }

  .ant-table-thead tr > th .ant-table-column-sorters {
    .ant-table-column-sorter {
      .ant-table-column-sorter-inner {
        span {
          &.active {
            color: #ffbd00;
          }
        }
      }
    }
  }
}
