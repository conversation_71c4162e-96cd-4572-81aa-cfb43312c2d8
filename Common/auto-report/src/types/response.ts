export type DataNode = {
  /** 节点的值，只有为叶子节点是有数据 */
  values?: {
    [field: string]: number | string,
  },
  /** 子元素分组依据 */
  groupDims?: {
    [field: string]: number | string,
  },
  /** 子元素的分组统计，最外层为整体合计值 */
  aggr?: {
    [field: string]: number | string,
  },
  /** 子元素 */
  items?: DataNode[],
  page?: {
    pageNo: number,
    pageSize: number,
    totalCount: number,
    totalPageSize: number,
  },
}
