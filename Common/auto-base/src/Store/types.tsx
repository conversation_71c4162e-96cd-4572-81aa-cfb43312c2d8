// Type definitions for react-redux 7.1
// Project: https://github.com/reduxjs/react-redux
// Definitions by: <PERSON><PERSON><PERSON> <https://github.com/tkqubo>,
//                 <PERSON><PERSON> <https://github.com/kenzierocks>,
//                 <PERSON><PERSON><PERSON> <https://github.com/clayne11>
//                 <PERSON> <https://github.com/tansongyang>
//                 <PERSON> <https://github.com/nicholasboll>
//                 <PERSON><PERSON><PERSON> <https://github.com/mdibyo>
//                 <PERSON> <https://github.com/kallikrein>
//                 Valentin <PERSON> <https://github.com/val1984>
//                 <PERSON> <https://github.com/jrakotoharisoa>
//                 <PERSON><PERSON><PERSON> <https://github.com/apapirovski>
//                 <PERSON> <https://github.com/surgeboris>
//                 <PERSON><PERSON><PERSON> <https://github.com/soerenbf>
//                 <PERSON> <https://github.com/mrwolfz>
//                 <PERSON> <https://github.com/dylanvann>
//                 <PERSON><PERSON> <https://github.com/Lazyuki>
//                 Kazuma Ebina <https://github.com/kazuma1989>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// TypeScript Version: 3.0

import { ComponentType, NamedExoticComponent } from 'react';
import { NonReactStatics } from 'hoist-non-react-statics';

export type Action<S, G, A> = (store: Store<S, G>, payload: A) => void;
export type Getter<S, G, Selected> = (store: Store<S, G>) => Selected;

export type MapAction<S, G> = { [k: string]: Action<S, G, any> };
export type MapGetter<S, G> = { [k: string]: Getter<S, G, any> };

/**
 * This interface can be augmented by users to add default types for the root state when
 * using `react-redux`.
 * Use module augmentation to append your own type definition in a your_custom_type.d.ts file.
 * https://www.typescriptlang.org/docs/handbook/declaration-merging.html#module-augmentation
 */
// tslint:disable-next-line:no-empty-interface
export interface DefaultRootState {}

export interface StoreOptions<S, G> {
  actions?: MapAction<S, G>;
  getters?: MapGetter<S, G>;
}

export interface Store<S = {}, G = {}> {
  state: S;
  getters: G;

  setState: (state: Partial<S>) => void;
  dispatch: (name: string, payload: any) => void;
  subscribe: (listener: () => void) => () => void;
}

export interface StoreProps<S = {}> {
  store: Store<S>;
}

/**
 * A property P will be present if:
 * - it is present in DecorationTargetProps
 *
 * Its value will be dependent on the following conditions
 * - if property P is present in InjectedProps and its definition extends the definition
 *   in DecorationTargetProps, then its definition will be that of DecorationTargetProps[P]
 * - if property P is not present in InjectedProps then its definition will be that of
 *   DecorationTargetProps[P]
 * - if property P is present in InjectedProps but does not extend the
 *   DecorationTargetProps[P] definition, its definition will be that of InjectedProps[P]
 */
export type Matching<InjectedProps, DecorationTargetProps> = {
  [P in keyof DecorationTargetProps]: P extends keyof InjectedProps
    ? InjectedProps[P] extends DecorationTargetProps[P]
      ? DecorationTargetProps[P]
      : InjectedProps[P]
    : DecorationTargetProps[P];
};

/**
 * a property P will be present if :
 * - it is present in both DecorationTargetProps and InjectedProps
 * - InjectedProps[P] can satisfy DecorationTargetProps[P]
 * ie: decorated component can accept more types than decorator is injecting
 *
 * For decoration, inject props or ownProps are all optionally
 * required by the decorated (right hand side) component.
 * But any property required by the decorated component must be satisfied by the injected property.
 */
export type Shared<InjectedProps, DecorationTargetProps> = {
  [P in Extract<
    keyof InjectedProps,
    keyof DecorationTargetProps
  >]?: InjectedProps[P] extends DecorationTargetProps[P]
    ? DecorationTargetProps[P]
    : never;
};

// Infers prop type from component C
export type GetProps<C> = C extends ComponentType<infer P> ? P : never;

export type ConnectedComponent<
  C extends ComponentType<any>,
  T,
  P
> = NamedExoticComponent<
  JSX.LibraryManagedAttributes<
    C,
    Omit<GetProps<C>, keyof Shared<T, GetProps<C>>> & P
  >
> &
  NonReactStatics<C> & {
    WrappedComponent: C;
  };
