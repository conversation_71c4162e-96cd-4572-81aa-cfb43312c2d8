/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { UserContext, UserService } from '@mtfe/next-biz/src/contexts/user';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import { Filter, InputSet, InputConstructor } from '../types/filter';
import { FilterManager, inputSetContext } from '../types/context';
import { ReportProps } from '../types';

/**
 * 过滤器管理
 */
export default function useFilterManager(
  props: ReportProps | null,
  parentManager?: FilterManager,
  haveFilter?: boolean,
): FilterManager {
  const { queryPersistence } = props || {};
  /** 内部数据准备完毕 */
  const [ready, setReady] = React.useState(false);
  /** 初始化完毕，外部可以 filterManager 的数据 */
  const [inited, setInited] = React.useState(false);
  const [defaultQuery, setDefaultQuery] = React.useState<{
    [k: string]: any,
  }>({});
  const userService = React.useContext(UserContext);
  const [originFilters, updateFilters] = React.useState<Filter[]>([]);
  const [errors, setErrors] = React.useState<Record<string, React.ReactNode>>({});
  const Inputs = React.useContext(inputSetContext);

  /** 自持查询条件，用于和父查询区分开，用于存储过滤工具栏的值 */
  const [selfHoldQuery, setSelfHoldQuery] = React.useState<any | null>(null);
  /** 展示组件的查询条件，比如表格的分页/排序 */
  const [displayQuery, updateDisplayQuery] = React.useState<any | null>(null);
  const inputsRef = React.useRef<InputSet>();

  /** 查询条件原始值，会随着用户更改查询条件而变
   * 和 selfHoldQuery 的区别是，selfHoldQuery 只有在点击 Submit 时才会更新
   */
  const [selfHoldOriginQuery, updateOriginQuery] = React.useState<any | null>(null);

  /** 如果父选择器或者自持查询条件更变时，刷新 query */
  const query = React.useMemo(() => ({
    ...parentManager?.query,
    ...selfHoldQuery,
    ...displayQuery,
  }), [
    parentManager?.query,
    selfHoldQuery,
    displayQuery,
  ]);

  /**
   * 如果父选择器或者自持查询条件更变时，刷新 originQuery
   * 和 query 的区别是，query 只有在点击 Submit 时才会更新， 而 originQuery 实时更新
   */
  const originQuery = React.useMemo(() => ({
    ...parentManager?.originQuery,
    ...selfHoldOriginQuery,
  }), [
    parentManager?.originQuery,
    selfHoldOriginQuery,
  ]);

  /** 更新自持查询条件 */
  const updateQuery = React.useCallback((q: any) => {
    setSelfHoldQuery(q);
  }, [setSelfHoldQuery]);

  /** 更新输入组件 */
  const [inputs, filters] = React.useMemo(() => {
    const newInputs: InputSet = {};
    const newFilters: Filter[] = [];
    originFilters?.forEach((filter) => {
      if (filter.loginType === 'poi' && userService?.isHeadOffice()) return;
      if (filter.loginType === 'chain' && !userService?.isHeadOffice()) return;
      let Input: InputConstructor;
      if (typeof filter.type === 'string') {
        Input = Inputs[filter.type];
        if (!Input) {
          console.warn(`配置化报表未找到输入组件：${filter.type}，配置：`, filter);
          return;
        }
      } else {
        Input = filter.type;
      }

      const input = new Input(filter, { userService: userService as UserService });
      // if (input.isVisiable && !input.isVisiable()) return;

      newInputs[filter.field] = input;
      newFilters.push(filter);
    });
    return [newInputs, newFilters];
  }, [userService, originFilters]);

  /** 初始化 hook，获取每个 input 的默认值 */
  React.useEffect(() => {
    (async () => {
      const _defaultQuery: any = {};
      const inputsKeys = Object.keys(inputs);

      if (haveFilter && inputsKeys.length) {
        const p = Promise.all(inputsKeys.map(async (key) => {
          if (defaultQuery[key]) {
            _defaultQuery[key] = defaultQuery[key];
            return;
          }
          const input = inputs[key];
          if (!input.getDefaultValue) return;
          const v = await input.getDefaultValue();
          if (typeof v !== 'undefined') {
            _defaultQuery[key] = v;
          }
        }));
        const success = await doAction(p);
        if (!success) return;
      }
      if (!haveFilter || (haveFilter && inputsKeys.length)) {
        const _originalQuery = queryPersistence?.read() || {};
        setDefaultQuery(_defaultQuery);
        updateOriginQuery((q: any) => ({ ..._defaultQuery, ..._originalQuery, ...q }));
        setReady(true);
      }
    })();
  }, [setDefaultQuery, setReady, updateOriginQuery, inputs, inited, originFilters, haveFilter]);

  /**
   * 数据准备完毕，将筛选默认值装入查询条件，触发查询
   * 只会在初始化时执行一次
   */
  React.useEffect(() => {
    if (ready && !inited) {
      if (parentManager && !parentManager.inited) return;
      const _originalQuery = queryPersistence?.read() || {};
      setSelfHoldQuery((q: any) => ({ ...defaultQuery, ..._originalQuery, ...q }));
      setInited(true);
    }
  }, [setInited, ready, defaultQuery, inited, parentManager?.inited]);

  const [shownInputs, shownFilters] = React.useMemo(() => {
    const _query = { ...defaultQuery, ...originQuery };
    // 处理统计方式和其他筛选项联动的关系
    const { groupBy } = _query;
    const newInputs = { ...inputs };
    const newFilters = filters.filter((filter) => {
      /** ----- showOnGroupBy 字段已被废弃 ------- */
      if (!filter.showOnGroupBy) return true;
      if (!Object.values(groupBy || {}).includes(filter.showOnGroupBy)) {
        delete newInputs[filter.field];
        return false;
      }
      return true;
    });
    inputsRef.current = newInputs;
    return [newInputs, newFilters];
  }, [filters, defaultQuery, originQuery]);

  /** 校验函数 */
  React.useEffect(() => {
    const _errors: typeof errors = {};
    shownFilters.forEach((filter) => {
      if (filter.validator && selfHoldOriginQuery) {
        const error = filter.validator(selfHoldOriginQuery[filter.field]);
        if (!error) return;
        _errors[filter.field] = error;
      }
    });
    setErrors(_errors);
  }, [selfHoldOriginQuery, shownFilters]);

  /**
   * 获取报表导出时需要的序列化查询条件供用户阅读
   */
  const getQueryInfo = React.useCallback(() => {
    if (!query) return '';

    const str = shownFilters.map((filter) => {
      const input = shownInputs[filter.field];
      const value = query[filter.field];
      if (!input) return null;
      let displayValue = value;
      if (input.getDisplayValue) {
        displayValue = input.getDisplayValue(value, query);
      }

      if (filter.label) {
        let label: string | undefined;
        if (input.getLabel) {
          label = input.getLabel(value, query);
        } else {
          label = filter.label;
        }
        return `${label}：【${displayValue}】`;
      } else {
        return displayValue;
      }
    }).filter(Boolean).join('；');

    const parentStr = parentManager?.getQueryInfo();

    return [parentStr, str].filter(Boolean).join('；');
  }, [shownFilters, shownInputs, query, parentManager?.getQueryInfo]);

  /**
   * 准备传递给 API 的查询条件
   */
  const prepareQuery = React.useCallback((hasSave?: boolean) => {
    let result: any = {
      ...parentManager?.prepareQuery() || {},
      pageNo: query.pageNo || 1,
      pageSize: query.pageSize || 20,
      orderBy: query.orderBy,
      orderByType: query.orderByType,
      loginName: userService?.org.name,
      login: userService?.account.login,
      ...displayQuery,
    };

    const _originalQuery: any = {};
    Object.entries(inputsRef.current || {}).forEach(([field, input]) => {
      let value = query[field];
      _originalQuery[field] = value;
      if (!input) return;
      if (input.getValue) {
        value = input.getValue(value);
      }

      if (input.getQuery) {
        result = { ...result, ...input.getQuery(value, result) };
      } else if (field) {
        result[field] = value;
      }
    });

    if (queryPersistence && hasSave) {
      queryPersistence.write(_originalQuery);
    }
    return result;
  }, [userService, inputsRef, query, displayQuery, parentManager?.prepareQuery]);

  const serializedQuery = React.useMemo(() => {
    const result: { [k: string]: string } = {};
    Object.entries(query).forEach(([k, v]) => {
      const input = shownInputs[k];
      if (!input) return;
      const serialize = input.serialize;
      if (!serialize) return;
      result[k] = serialize(v);
    });
    return result;
  }, [query, shownInputs]);

  const updateQueryBySerialized = React.useCallback((value: { [k: string]: string | undefined }) => {
    const result: { [k: string]: any } = {};
    Object.entries(value).forEach(([k, v]) => {
      if (typeof v === 'undefined') return;
      const input = shownInputs[k];
      if (!input) return;
      const deserialize = input.deserialize;
      if (!deserialize) return;
      result[k] = deserialize(v);
    });
    updateOriginQuery(result);
  }, [shownInputs, updateOriginQuery]);

  const _originQuery = React.useMemo(() => ({ ...defaultQuery, ...originQuery }), [defaultQuery, originQuery]);

  /** 返回对象加入缓存 */
  return React.useMemo(() => ({
    errors,
    filters: shownFilters,
    updateFilters,
    getQueryInfo,
    prepareQuery,
    inputs: shownInputs,
    updateQuery,
    updateDisplayQuery,
    updateOriginQuery,
    displayQuery,
    query,
    originQuery: _originQuery,
    defaultQuery,
    isSubFilter: !!parentManager,
    serializedQuery,
    updateQueryBySerialized,
    inited,
  }), [
    inited,
    errors,
    updateQueryBySerialized,
    serializedQuery,
    shownFilters,
    updateFilters,
    getQueryInfo,
    prepareQuery,
    shownInputs,
    query,
    _originQuery,
    defaultQuery,
    updateQuery,
    updateDisplayQuery,
    updateOriginQuery,
    displayQuery,
    parentManager,
  ]);
}
