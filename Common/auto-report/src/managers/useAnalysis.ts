import React from 'react';
import * as analysis from '@mtfe/next-biz/src/utils/analytics';
import { PerfPoint, POINT_ENUM } from '@mtfe/next-biz/src/utils/addPoints';
import { AnalysisManager } from '../types/context';

export default function useAnalysis(
  inited: boolean,
  _cid?: string,
  parent?: AnalysisManager,
  preventPV?: boolean, // 不需要执行pageView
): AnalysisManager {
  // 当第一次加载完成后，进行埋点上报
  const isFirstLoad = React.useRef(true);

  if (isFirstLoad.current && inited) {
    const addPoints = PerfPoint();
    setTimeout(() => { addPoints.send(POINT_ENUM.前端渲染完成时间, performance.timing.navigationStart, Date.now(), { noise: 8000 }); }, 4);
    isFirstLoad.current = false;
  }

  const cid = React.useMemo(() => {
    if (_cid) return _cid;
    if (parent?.cid) return parent.cid;
    return undefined;
  }, [parent]);

  React.useEffect(() => {
    if (_cid && !preventPV) {
      analysis.pageView(_cid)();
    }
  }, [_cid]);

  const pageView = React.useCallback(() => {
    if (!cid) return;
    analysis.pageView(cid)();
  }, [cid]);

  const moduleView = React.useCallback((bid: string) => {
    if (!cid || !bid) return;
    analysis.moduleView(bid, {}, { cid })();
  }, [cid]);

  const moduleClick = React.useCallback((bid: string) => {
    if (!cid || !bid) return;
    analysis.moduleClick(bid, {}, { cid });
  }, [cid]);

  return React.useMemo(() => ({
    pageView,
    moduleClick,
    moduleView,
    cid,
  }), [
    cid,
    pageView,
    moduleClick,
    moduleView,
  ]);
}
