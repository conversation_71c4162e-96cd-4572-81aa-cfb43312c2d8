{"extends": "../common-configs/tsconfig.json", "compilerOptions": {"allowJs": true, "typeRoots": ["./typings/*", "node_modules/@types"], "resolveJsonModule": true, "target": "es5", "rootDirs": ["src", "typings"], "module": "esnext", "lib": ["es2017", "dom", "es2017.object", "es2015"], "types": ["prop-types", "react", "node", "jest"], "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@mtfe/next-biz/*": ["../next-biz/src/*"], "@mtfe/next-biz/es/*": ["../next-biz/src/*"], "@mtfe/auto-base/*": ["../auto-base/src/*"], "@mtfe/auto-base/es/*": ["../auto-base/src/*"], "@mtfe/auto-base": ["../auto-base/src/index"]}}, "include": ["./src"], "exclude": ["*.test.js", "*.test.jsx"]}