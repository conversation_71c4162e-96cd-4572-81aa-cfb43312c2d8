import * as React from 'react';
import { IV } from '../type';

export type DataContextType<T extends {}> = {
  dataPool: Readonly<T>;
  setDataPool: (k: keyof T, v: any) => void;
  setDataPools: (values: Partial<T>) => void;
};

const defaultDataContext = {
  dataPool: {},
  setDataPool: () => undefined,
  setDataPools: () => undefined,
};

export const DataContext = React.createContext<DataContextType<IV>>(defaultDataContext);

type State<T> = DataContextType<T>;

export default class DataContextWrapper<T extends {}> extends React.Component<{}, State<T>> {
  setDataPool = (k: keyof T, value: any) => {
    const { dataPool } = this.state;
    // @ts-ignore
    dataPool[k] = value;
    this.setState({ dataPool });
  };

  setDataPools = (values: Partial<T>) => {
    const { dataPool } = this.state;
    this.setState({ dataPool: { ...dataPool, ...values } });
  };

  state: State<T> = {
    dataPool: {} as T,
    setDataPool: this.setDataPool,
    setDataPools: this.setDataPools,
  };

  render() {
    return <DataContext.Provider value={this.state as DataContextType<IV>}>{this.props.children}</DataContext.Provider>;
  }
}

export const withDataContext = <P extends { forwardedRef?: React.Ref<P> }>(Component: React.ComponentType<P>) => {
  return class extends React.Component<P> {
    // 设置autoForm 标识，初始化时会自动根据此标识收集 form ref
    static isAutoForm = true;
    render() {
      return (
        <DataContextWrapper>
          <Component {...this.props} ref={this.props.forwardedRef} />
        </DataContextWrapper>
      );
    }
  };
};
