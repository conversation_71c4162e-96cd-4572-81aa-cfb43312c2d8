const { isVoid, formatMoney, default: format } = require('./format');

describe('isVoid', () => {
  it('传入 null 返回 true', () => {
    expect(isVoid(null)).toBe(true);
  });

  it('传入 undefined 返回 true', () => {
    expect(isVoid(undefined)).toBe(true);
  });

  it('传入数字、字符串返回false', () => {
    expect(isVoid('')).toBe(false);
    expect(isVoid(0)).toBe(false);
  });
});

describe('formatMoney', () => {
  it('正确补位小数位', () => {
    expect(formatMoney(100)).toBe('1.00');
  });

  it('正确处理千分位', () => {
    expect(formatMoney(10 * 100)).toBe('10.00');
    expect(formatMoney(100 * 100)).toBe('100.00');
    expect(formatMoney(1000 * 100)).toBe('1,000.00');
    expect(formatMoney(10 * 1000 * 1000 * 100)).toBe('10,000,000.00');
  });

  it('非法输入时，返回 0', () => {
    expect(formatMoney('')).toBe('0.00');
    expect(formatMoney(0)).toBe('0.00');
    expect(formatMoney(null)).toBe('0.00');
    expect(formatMoney(undefined)).toBe('0.00');
    expect(formatMoney({})).toBe('0.00');
    expect(formatMoney([])).toBe('0.00');
  });
});

describe('format', () => {
  it('传入不支持的格式化方法时，返回原数据', () => {
    var val = 123;
    expect(format('joidasjf', val)).toBe(val);
    val = {};
    expect(format('439u5', val)).toBe(val);
    val = [];
    expect(format('5490', val)).toBe(val);
  });

  it('支持传入函数', () => {
    const fn = (v) => {
      return 'test'+String(v);
    };
    expect(format(fn, 1)).toBe(fn(1));
    expect(format(fn, '1')).toBe(fn('1'));
    expect(format(fn, 435489)).toBe(fn(435489));
    expect(format(fn, {})).toBe(fn({}));
  });

  describe('currency', () => {
    it('currency 返回金额', () => {
      expect(format('currency', 0)).toBe('0.00');
    });
  })

  describe('percent', () => {
    it('返回百分比字符串', () => {
      expect(format('percent', 0)).toBe('0.00%');
      expect(format('percent', 10)).toBe('10%');
      expect(format('percent', 9.9)).toBe('9.9%');
      expect(format('percent', 20.34)).toBe('20.34%');
      expect(format('percent', 99)).toBe('99%');
    });
  
    it('传入为空时返回 0.00%', () => {
      expect(format('percent', undefined)).toBe('0.00%');
      expect(format('percent', null)).toBe('0.00%');
    });
  
    it('传入非数字或字符串时，返回 0.00%', () => {
      expect(format('percent', {})).toBe('0.00%');
      expect(format('percent', [])).toBe('0.00%');
      expect(format('percent', new Map())).toBe('0.00%');
      expect(format('percent', new Set())).toBe('0.00%');
      expect(format('percent', Symbol())).toBe('0.00%');
    });
  });

  describe('timestamp[date]', () => {
    it('输入时间戳，返回对应日期', () => {
      let date = new Date(2020, 5-1, 9);
      expect(format('timestamp[date]', date.getTime())).toBe('2020/05/09');
      date = new Date(2020, 11 - 1 , 12);
      expect(format('timestamp[date]', date.getTime())).toBe('2020/11/12');
      date = new Date(2000, 14 - 1, 31);
      expect(format('timestamp[date]', date)).toBe('2001/03/03');
    });
    it('支持传入 Date', () => {
      let date = new Date(2020, 5-1, 9);
      expect(format('timestamp[date]', date)).toBe('2020/05/09');
      date = new Date(2020, 11 - 1 , 12);
      expect(format('timestamp[date]', date)).toBe('2020/11/12');
    });
  });


  describe('timestamp[time]', () => {
    it('输入时间戳，返回对应日期', () => {
      let date = new Date(2020, 5-1, 9);
      expect(format('timestamp[time]', date.getTime())).toBe('2020/05/09 00:00:00');
      date = new Date(2020, 11 - 1 , 12);
      expect(format('timestamp[time]', date.getTime())).toBe('2020/11/12 00:00:00');
      date = new Date(2000, 14 - 1, 31);
      expect(format('timestamp[time]', date)).toBe('2001/03/03 00:00:00');
    });
    it('支持传入 Date', () => {
      let date = new Date(2020, 5-1, 9);
      expect(format('timestamp[time]', date)).toBe('2020/05/09 00:00:00');
      date = new Date(2020, 11 - 1 , 12);
      expect(format('timestamp[time]', date)).toBe('2020/11/12 00:00:00');
    });
  });
});
