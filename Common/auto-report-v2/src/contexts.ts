import React from 'react';
import {
  EnvType,
  StoreType,
  DataType,
  DataControlType,
  StoreControlType,
} from './types';

export { EnvType, DataControlType, StoreControlType };

// 这里为不需要变化的数据的 context
export const dataContext = React.createContext<DataType>({});
dataContext.displayName = 'AT.Data.Context';

// auto 全局 State, 绑定 auto 组件的 props.state 跟新
export const stateContext = React.createContext({});
stateContext.displayName = 'AT.State.Context';

// auto 全局内置 store，绑定内部组件状态更新
export const storeContext = React.createContext<StoreType>({ state: [] });
storeContext.displayName = 'AT.Store.Context';
