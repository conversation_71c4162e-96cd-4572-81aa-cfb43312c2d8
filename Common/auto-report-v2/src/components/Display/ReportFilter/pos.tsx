import { forEach, throttle } from 'lodash';
import React from 'react';
import classNames from 'classnames';
import { Button } from '@mtfe/sjst-antdx-next';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import { useManager } from './useManager';
import { Config, ComponentProps, ObjectRefType } from '../../../types';

import './pos.less';

interface Props extends ComponentProps<Config[]> {
  // 默认展示组件的数量，默认是按照默认一行展示
  defaultQueryCount?: number;
  items: Config[];
  analytics?: {
    submitBid: string;
    resetBid: string;
    toggleQueryBid: string;
  };
}

function ReportFilter({
  defaultQueryCount,
  items,
  baseRefObject,
  updateStatus,
  analytics,
  $$auto,
}: Props) {
  const queryContainerRef = React.useRef<HTMLDivElement>(null);

  const refObject = useRefObject(baseRefObject);

  const [hasExpandButton, setHasExpandButton] = React.useState(true);
  const [isExpand, setExpand] = React.useState(false);
  const queryWidthsRef = React.useRef<{ [id: string]: number }>({});

  const { setSelectedId, queryManageList, queryManageButton } = useManager(
    items,
    refObject,
    $$auto,
  );

  React.useEffect(() => {
    if (!queryContainerRef.current) {
      return;
    }

    const calculate = () => {
      const refDom = queryContainerRef.current;
      if (!refDom) {
        return;
      }

      // 预留宽度。切换时间/选择下拉时，输入框会变长，导致折叠
      let reservedWidth = 120;
      // 初始化筛选项宽度
      const _widths: { [id: string]: number } = { ...queryWidthsRef.current };

      const itemDoms = refDom.querySelectorAll('.auto2-query-item') || [];
      forEach(itemDoms, (itemDom) => {
        const key = itemDom.getAttribute('data-key');
        const width = itemDom.clientWidth;
        if (key) {
          _widths[key] = width;
        }
      });

      queryWidthsRef.current = _widths;

      // 计算折叠多少个
      const containerWidth = refDom.clientWidth;
      if (!containerWidth) {
        return;
      }

      const columnCount = Math.floor(containerWidth / 300); // 显示列数
      const data = { firstCount: 1, separatorWidth: 0, preWidth: 0 };
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.props?.dataKey) {
          const width = _widths[item.props?.dataKey];
          if (width) {
            if (containerWidth >= data.preWidth + width + reservedWidth) {
              data.firstCount += 1;
              data.separatorWidth =
                containerWidth - data.preWidth - reservedWidth - 20;
              data.preWidth += width;
              itemDoms[i]?.classList?.add('first');
              itemDoms[i]?.removeAttribute('style');
            } else {
              const isHide = defaultQueryCount ? i >= defaultQueryCount : true;
              itemDoms[i]?.classList?.add('more');
              if (isHide) {
                itemDoms[i]?.classList?.add?.('hide');
              }
              itemDoms[i]?.setAttribute('style', `flex: 0 1 ${100 / (columnCount || 1)}%`);
            }
          }
        }
      }
      setHasExpandButton(defaultQueryCount ? items.length > defaultQueryCount : items.length > data.firstCount);
    };

    const throttleCalculate = throttle(calculate, 1000, {
      leading: true,
      trailing: true,
    });

    const throttleCalculateX = throttle(calculate, 1000, {
      leading: false,
      trailing: true,
    });

    const delayCalculate = (mutationRecords?: MutationRecord[]) => {
      const reportStatus =
        refObject?.version === 2
          ? $$auto.withParentName('Report')?.$self.status
          : (refObject as ObjectRefType<any>)?.withParentName('Report')?.status;
      if (reportStatus !== 'done') {
        setTimeout(() => {
          delayCalculate();
        }, 100);
      } else if (!mutationRecords) {
        throttleCalculate();
      } else if (
        mutationRecords?.findIndex(
          (m) => m.target === queryContainerRef.current
        ) !== -1
      ) {
        // 展开收起
        throttleCalculate();
      } else if (
        mutationRecords?.findIndex((m) => m.type === 'childList') !== -1
      ) {
        // 子节点发生变化
        throttleCalculateX();
      } else if (!mutationRecords) {
        throttleCalculateX();
      }
    };

    delayCalculate();

    window.addEventListener('resize', () => delayCalculate());
    const observer = new MutationObserver(delayCalculate);
    observer.observe(queryContainerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
    });
    return () => {
      window.removeEventListener('resize', calculate);
      observer.disconnect();
    };
  }, [queryContainerRef.current]);

  const toggleExpand = React.useCallback(() => {
    setExpand((a) => !a);
    analytics?.toggleQueryBid &&
      refObject?.analytics.moduleClick(analytics.toggleQueryBid);
  }, []);

  const handleSubmit = React.useCallback(() => {
    refObject?.emit('submit@Report');
    analytics?.submitBid &&
      refObject?.analytics.moduleClick(analytics.submitBid);
  }, []);

  const handleReset = React.useCallback(() => {
    refObject?.emit('reset@Report');
    refObject?.emit('submit@Report', { isReset: true });
    setSelectedId(undefined);
    analytics?.resetBid && refObject?.analytics.moduleClick(analytics.resetBid);
  }, []);

  const expandButton = React.useMemo(() => {
    if (!hasExpandButton) {
      return null;
    }
    return (
      <Button
        onClick={toggleExpand}
        className="auto2-query-toggle"
        icon={isExpand ? 'up' : 'down'}
      >
        {isExpand ? '收起筛选' : '更多筛选'}
      </Button>
    );
  }, [hasExpandButton, isExpand, toggleExpand]);

  const itemNode = React.useMemo(() => {
    return items.filter(Boolean).map((d) => ({
      props: d.props,
      dom: refObject?.renderManager.render(d),
    })).map((item, index) => {
      return (
        <div
          key={`query-${item?.props?.dataKey || index}`}
          data-key={item?.props?.dataKey}
          className={classNames(
            'auto2-query-item',
            item?.props?.className,
            'first'
          )}
        >
          {item?.props?.label && (
            <div className="auto2-query-item-label">{item.props.label}:</div>
          )}
          <div className="auto2-query-item-control">{item.dom}</div>
        </div>
      );
    });
  }, [items]);

  const queryActive = React.useMemo(
    () => (
      <div className="auto2-query-item action">
        {expandButton}
        <Button icon="search" type="primary" onClick={handleSubmit}>
          查询
        </Button>
        <Button icon="redo" onClick={handleReset}>
          重置
        </Button>
        {queryManageButton}
      </div>
    ),
    [expandButton, queryManageButton, handleSubmit, handleReset]
  );

  React.useEffect(() => updateStatus?.('done', items), [items]);

  return React.useMemo(() => {
    return (
      <div className="auto2-filter">
        {queryManageList}
        <div
          className={classNames(
            'auto2-query-container',
            isExpand ? 'expand' : ''
          )}
          ref={queryContainerRef}
        >
          {itemNode}
        </div>
        <div className="query-container-buttons">{queryActive}</div>
      </div>
    );
  }, [queryManageList, isExpand, itemNode, queryActive]);
}

// @ts-ignore
ReportFilter.auto = {
  childrenIsConfig: false,
};

export default withAdapter(ReportFilter, {
  name: 'ReportFilter',
  isComponentRender: false,
});
