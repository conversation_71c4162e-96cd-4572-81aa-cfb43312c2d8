import React from 'react';
import { Tool, ToolsManager } from '../types/context';

export default function useTools(): ToolsManager {
  const [_tools, setTools] = React.useState<Record<string, Tool | null>>({});

  const updateTools = React.useCallback((toolSet: Record<string, Tool>) => {
    setTools(t => ({ ...t, ...toolSet }));
  }, [setTools]);

  const tools: Tool[] = React.useMemo(() => Object.values(_tools).filter(Boolean) as Tool[], [_tools]);

  return React.useMemo(() => ({
    tools,
    updateTools,
  }), [
    tools,
    updateTools,
  ]);
}
