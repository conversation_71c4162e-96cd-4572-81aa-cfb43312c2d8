import WindowHistoryReportQueryPersistence from '@mtfe/sjst-report/es/WindowHistoryReportQueryPersistence';
import { StorageHistoryReportQueryPersistence } from '@mtfe/next-biz/es/lib/StorageHistoryReportQueryPersistence';
import { ReportQueryPersistence } from '../../../types';

export type QueryPersistenceType =
  | 'UrlQueryPersistence'
  | 'StoragePersistence'
  | ReportQueryPersistence;

export const urlQueryPersistence = new WindowHistoryReportQueryPersistence({});
export const storagePersistence = new StorageHistoryReportQueryPersistence({});
export const queryPersistenceMaps = {
  UrlQueryPersistence: urlQueryPersistence,
  StoragePersistence: storagePersistence,
};

// 参数存储
export function getQueryPersistence(queryPersistence?: QueryPersistenceType) {
  if (!queryPersistence) {
    return undefined;
  }
  if (typeof queryPersistence === 'string') {
    return queryPersistenceMaps[queryPersistence];
  }
  return queryPersistence;
}
