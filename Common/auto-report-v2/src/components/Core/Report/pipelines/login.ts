/* eslint-disable @typescript-eslint/no-explicit-any */
import { EnvType, ReportValueType } from '../../../../types';
import manager from './manager';

export const login = async (data: ReportValueType<any>, options: { env: EnvType }) => ({
  ...data,
  query: {
    ...data.query,
    login: options?.env?.user?.login,
    loginName: options?.env?.user?.loginName,
  },
});

manager.add('login', login);

export default login;
