:root .auto-report-page .auto2-filter {
  margin: 8px 0;

  .auto2-query-manage-tags {
    .ant-tag {
      background: #F7F7F7;
      border: 1px solid #E5E5E5;
      border-radius: 6px;
      font-size: 16px;
      color: #222333;
      line-height: 32px;
      padding: 0 16px;
      cursor: pointer;

      &.active {
        background: #fff;
      }

      .anticon-close {
        font-size: 16px;
      }
    }
  }

  .auto2-query-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 8px;
  
    .tree-selector {
      width: 100%;
      > .ant-select {
        display: block;
      }
    }
  
    .ant-select {
      display: block;
      flex: 1;
    }
  
    .ant-input-group-compact .ant-select {
      display: inline-block;
    }
  
    // tag 布局调整
    .ant-tags {
      display: flex;
      padding-top: 3px;
    }
    
    &.expand .auto2-query-item.hide {
      display: flex;
    }
  
    .auto2-query-item {
      display: flex;
      align-items: center;
      height: 45px;
  
      &.first {
        flex-grow: 0;
        flex-shrink: 0;
      }
  
      &.more {
        display: flex;
      }
  
      &.hide {
        display: none;
      }
    }
  
    .auto2-query-item-label {
      margin-right: 8px;
      white-space: nowrap;
    }
  
    .auto2-query-item-control {
      display: flex;
      position: relative;
      padding-right: 16px;
      zoom: 1;
      flex: 1;
    }
    .auto2-query-toggle {
      margin-right: 0;
      padding-left: 0;
      padding-right: 10px;
      color: #fe8c00;
      span {
        margin: 0;
      }
    }
  }

  .query-container-buttons {
    .ant-btn {
      margin-right: 8px;
    }
  }
}
