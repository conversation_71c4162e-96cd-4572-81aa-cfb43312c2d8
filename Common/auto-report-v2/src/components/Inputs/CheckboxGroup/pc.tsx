import React from 'react';
import { Checkbox } from '@mtfe/sjst-antdx-next';
import { ID } from '@mtfe/next-biz/es/services/types';
import withAdapter from '../../../core/withAdapter';
import { CheckboxGroupProps } from './types';

const CheckboxGroup = (props: CheckboxGroupProps) => {
  const {
    options, updateStatus, value, onChange, disabled
  } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return React.useMemo(() => {
    return <Checkbox.Group value={value} disabled={disabled} options={options} onChange={onChange} />;
  }, [value, disabled, options, onChange]);
};

CheckboxGroup.prototype.getDisplayValue = (
  value: ID[],
  props?: CheckboxGroupProps,
) => {
  if (props?.exportHide) {
    return '';
  }
  const displayValue = props?.options?.map(op => {
    let display = `${op.label}【否】`;
    const hasValue = value?.some(i => i === op.value);
    if (op?.title) {
      display = hasValue ? `${op?.title}【是】` : `${op?.title}【否】`;
    } else {
      display = hasValue ? `${op.label}【是】` : `${op.label}【否】`;
    }
    return display;
  });

  return displayValue?.join('；');
};

export default withAdapter(CheckboxGroup, { name: 'CheckboxGroup' });
