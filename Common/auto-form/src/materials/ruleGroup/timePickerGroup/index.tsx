import * as React from 'react';
import moment from 'moment';
import { Button } from 'antd';
import { TimePickerProps } from 'antd/es/time-picker';
import { ValueControl } from '../../types';
import { RuleGroup, RuleGroupType } from '..';
import './index.less';

type MomentType = number | undefined;
type RangesType = Array<MomentType>;

export type TimePickerGroupMaterialProps = {
  name: 'time-range-picker-group';
  props?: Omit<RuleGroupType<RangesType, TimePickerProps>, 'renderAdd' | 'renderDelete' | 'childNode' | 'emptyValue'>;
};

type Props = TimePickerGroupMaterialProps & ValueControl<RangesType[]>;

const DEFAULT_TIME = [+moment().startOf('day'), +moment().endOf('day')];

export const TimePickerGroupMaterial = (p: Props) => {
  const { props, value, onChange } = p;
  const renderAdd = React.useCallback((dom, onClick: () => void) => {
    return (
      <div>
        {dom}
        <div className="add-btn">
          <a onClick={onClick}>添加一个时间段</a>
        </div>
      </div>
    );
  }, []);
  const renderDelete = React.useCallback((dom, _index, onClick) => {
    return (
      <div className="time-range-picker-rule-item" >
        {dom}
        <Button type="primary" shape="circle" icon="minus" size="small" className="delete-btn" onClick={onClick} />
      </div>
    );
  }, []);
  return (
    <RuleGroup
      emptyValue={DEFAULT_TIME}
      {...props}
      value={value}
      onChange={(v: RangesType[]) => {
        onChange?.(v);
      }}
      childNode={{ name: 'time-range-picker', props: { format: 'HH:mm', allowClear: false } }}
      renderAdd={renderAdd}
      renderDelete={renderDelete}
    />
  );
};
