export type ReportPage = {
  total?: number;
  pageNo?: number;
  pageSize?: number;
};

export type ReportQuery = ReportPage & {
  [id: string]: any;
};

export interface ReportQueryPersistence {
  read: () => ReportQuery;
  write: (query: ReportQuery) => void;
}

export type ReportValueType<DataType = {}> = {
  originalQuery?: ReportQuery;
  query?: ReportQuery;
  data?: DataType;
  filters?: any;
  others?: {
    totalCount?: number;
    sumData?: any;
    clearingTime?: any;
  } & {
    [id: string]: any;
  };
  alert?: {
    type: 'success' | 'info' | 'warning' | 'error';
    message: string;
  };
  firstRequestFinished?: boolean,
};
