import React from 'react';
import {
  Col,
} from 'antd';
import {
  ColProps, Props,
} from '../../../types';
import ReportComponent from '../../ReportComponent';
import { registorComponent } from '../registor';
import './Col.less';

export default function ColLayout(props: Props<ColProps>) {
  const { children, ...rest } = props;
  return (
    <Col {...rest} className="auto-report-col">
      <ReportComponent>{children}</ReportComponent>
    </Col>
  );
}

registorComponent('Col', ColLayout);
