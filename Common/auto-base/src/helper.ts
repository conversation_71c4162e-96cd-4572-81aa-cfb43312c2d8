import { isEqualWith, isFunction, get, reduce } from 'lodash';

function isNode(value: any) {
  return value && typeof value === 'object' && '$$typeof' in value;
}

/**
 * 深对比
 */
export const deepEqual = (value: any, other: any) => {
  return isEqualWith(value, other, (a, b) => {
    if ((a?.equals && !b?.equals) || (!a?.equals && b?.equals)) {
      return false;
    }

    if (a?.equals && isFunction(a.equals)) {
      return a.equals(b);
    }

    if (isNode(a) || isNode(b)) {
      return a === b;
    }
  });
};

export function isBasicType(
  element: any
): element is string | number | boolean {
  switch (typeof element) {
    case 'string':
    case 'number':
    case 'boolean':
      return true;
    default:
      return false;
  }
}

export function isReactNode(
  element: any
): element is React.ReactElement | string | number | boolean {
  if (!element) return true;
  if (isBasicType(element)) return true;
  if ('$$typeof' in element) return true;
  return false;
}

export function getValueByKey(
  key: string,
  stateObject: any
) {
  if (key === 'global') {
    return stateObject?.$global;
  }
  if (key === '$self') {
    return stateObject?.$self;
  }
  if (key.indexOf('@') !== -1) {
    return stateObject?.withDataKey(key.replace('@', ''));
  }
  return get(stateObject, [key], '');
}

export function getValueByPath(
  path: string[],
  stateObject: any
) {
  return reduce(
    path,
    (v, p) => {
      return getValueByKey(p, v);
    },
    stateObject as any
  );
}