import { findLast, clone, assignWith } from 'lodash';
import React from 'react';
import { <PERSON><PERSON>, WildcardHandler } from 'mitt';
import {
  Provider,
  create,
  Store as _Store,
  useStore as _useStore,
  Getter,
} from './Store';
import globalManager, { GlobalManager, GlobalValueType } from './GlobalManager';
import { deepEqual } from './helper';
import {
  ComponentStatus,
  Config,
  RootStateType,
  StateObjectValueType,
} from './types';

function customizer(objValue: any, srcValue: any) {
  return srcValue === undefined ? objValue : srcValue;
}

export interface StoreValue {
  $root?: RootStateType; // 绑定 auto 组件的 props.state 更新，使其具备夸系统的引用能力
  $state: StateObjectValueType<any>[]; // 组件状态更新
}

export interface StoreGetter {
  $global: GlobalManager;
  getStateObject<ValueType>(id: string): StateObject<ValueType> | undefined;
  withId<ValueType>(id: string): StateObjectValueType<ValueType> | undefined;
  withDataKey<ValueType>(
    dataKey: string
  ): StateObjectValueType<ValueType> | undefined;
  withParentName<ValueType>(
    currentPath: string[],
    name: string
  ): StateObjectValueType<ValueType> | undefined;
  withChildrenName<ValueType>(
    currentPath: string[],
    name: string
  ): StateObjectValueType<ValueType>[];
}

export interface Store extends _Store<StoreValue, StoreGetter> {}

export type ContextProviderProps = {
  $root?: RootStateType;
  children?: React.ReactNode;
};

export class StateObject<ValueType> {
  private _store: Store;
  private _root?: RootStateType;
  private _global?: GlobalValueType;
  private _self?: StateObjectValueType<ValueType>;

  constructor(
    store: Store,
    idOrSelf?: string | StateObjectValueType<ValueType>
  ) {
    this._store = store;
    this._root = this._store.state.$root;
    this._global = this._store.getters.$global.state;
    if (idOrSelf) {
      if (typeof idOrSelf === 'string') {
        this._self = this._store.getters.withId(idOrSelf);
      } else {
        this._self = idOrSelf;
      }
    }
  }

  get store() {
    return this._store;
  }

  get $root() {
    return this._root;
  }

  get $global() {
    return this._global;
  }

  get $self() {
    return this._self;
  }

  on<T = any>(type: string, handler: Handler<T>): void;
  on(type: '*', handler: WildcardHandler) {
    this.$self?.emitter$.on(type, handler);
  }

  off<T = any>(type: string, handler: Handler<T>): void;
  off(type: '*', handler: WildcardHandler) {
    this.$self?.emitter$.off(type, handler);
  }

  emit<T = any>(type: string, event?: T) {
    const dataKeyStartIndex = type.indexOf('#');
    const parentNameStartIndex = type.indexOf('@');
    if (dataKeyStartIndex !== -1 || parentNameStartIndex !== -1) {
      setTimeout(() => {
        if (dataKeyStartIndex !== -1) {
          const refByDataKey = this.withDataKey(
            type.slice(dataKeyStartIndex + 1)
          );
          refByDataKey?.emit(type.slice(0, dataKeyStartIndex), event);
        } else if (parentNameStartIndex !== -1) {
          const refByParentName = this.withParentName(
            type.slice(parentNameStartIndex + 1)
          );
          refByParentName?.emit(type.slice(0, parentNameStartIndex), event);
        }
      });
    } else {
      this.$self?.emitter$.emit(type, event);
    }
  }

  withId<V = any>(id: string) {
    return this._store.getters.getStateObject<V>(id);
  }

  withDataKey<V = any>(dataKey: string) {
    const state = this._store.getters.withDataKey(dataKey);
    return state?.id ? new StateObject<V>(this._store, state.id) : undefined;
  }

  withParentName<V = any>(name: string) {
    const state = this._store.getters.withParentName(
      this.$self?.path || [],
      name
    );
    return state?.id ? new StateObject<V>(this._store, state.id) : undefined;
  }

  withChildrenName<V = any>(name: string) {
    const states = this._store.getters.withChildrenName(
      this.$self?.path || [],
      name
    );
    return states.map(
      (state: StateObjectValueType<V>) =>
        new StateObject<V>(this._store, state.id)
    );
  }

  updateStatus(status?: ComponentStatus, value?: ValueType) {
    this.store.dispatch('update', {
      id: this.$self?.id,
      status,
      value: value || this.$self?.value,
    });
  }

  complieConfig(config: Config): Config {
    return this._store.getters.$global.rules.complie(config, this);
  }

  equals(other: StateObject<any>): boolean {
    return deepEqual(
      {
        $root: this.$root,
        $global: this.$global,
        $self: this.$self,
      },
      {
        $root: other.$root,
        $global: other.$global,
        $self: other.$self,
      }
    );
  }
}

export function useStore<Selected = any>(
  selector?: Getter<StoreValue, StoreGetter, Selected>,
  deps?: React.DependencyList
) {
  return _useStore(selector, deps);
}

export default function ContextProvider(props: ContextProviderProps) {
  const { $root = {} } = props;

  const storeRef = React.useRef(
    (() => {
      const store = create<StoreValue, StoreGetter>(
        {
          $root,
          $state: [],
        },
        {
          getters: {
            $global: () => globalManager,
            getStateObject: (store) => (id: string) => {
              return new StateObject(store, id);
            },
            withId: (store) => (id: string) =>
              store.state.$state.find((v) => v.id === id),
            withDataKey: (store) => (dataKey: string) =>
              store.state.$state.find((v) => v.dataKey === dataKey),
            withParentName: (store) => (
              currentPath: string[],
              name: string
            ) => {
              const id = findLast(
                currentPath,
                (p) => p.split('@').indexOf(name) !== -1
              );
              return id ? store.getters.withId(id) : undefined;
            },
            withChildrenName: (store) => (
              currentPath: string[],
              name: string
            ) =>
              store.state.$state.filter(
                (v) =>
                  v.path.join('/').startsWith(currentPath.join('/')) &&
                  v.name === name
              ),
          },
          actions: {
            register(store, payload) {
              const { $state } = store.state;
              $state.push(payload);
              store.setState({ $state: $state.filter(Boolean) });
            },
            unRegister(store, payload) {
              const { $state } = store.state;
              const newState = $state.filter((v) => v.id !== payload?.id);
              store.setState({ $state: newState });
            },
            update(store, payload) {
              const { $state } = store.state;
              const updateIndex = $state.findIndex((v) => v.id === payload?.id);
              if (updateIndex !== -1) {
                const newItem = clone($state[updateIndex]);
                assignWith(newItem, payload, customizer);
                $state[updateIndex] = newItem;
              } else {
                $state.push(payload);
              }
              store.setState({ $state: $state.filter(Boolean) });
            },
          },
        }
      );
      return store;
    })()
  );

  React.useEffect(() => {
    storeRef.current?.setState({ $root });
  }, [$root]);

  return <Provider store={storeRef.current}>{props.children}</Provider>;
}