/* eslint-disable @typescript-eslint/no-explicit-any */
import { get } from 'lodash';
import React from 'react';
import { Button, Icon } from '@mtfe/sjst-antdx-next';
import { ua } from '@mtfe/next-biz/es/utils/ua';
import { useTableColumnSorter } from '@mtfe/next-biz/es/components/TableColumnSorter';
import {
  Item,
  SortItem,
} from '@mtfe/next-biz/es/components/TableColumnSorter/types';
import { useStore, Store } from '@mtfe/auto-base';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import { dataContext } from '../../../contexts';
import { ObjectRefType, ReportValueType } from '../../../types';
import { TableColumnProps, TableValueType } from '../Table/types';
import { ColumnSettingProps } from './types';

const transformationColumnsToSortItems = (
  columns: Array<TableColumnProps<any>>
): Item[] =>
  columns.map((column) => ({
    ...column,
    // Item lable 类型设置的是错误的，我没有修改的权限
    label: column.title as any,
    field: column.dataIndex ? `${column.dataIndex}` : `_${column.title}`,
    sortable: get(column, 'sortable', true),
    selectable: get(column, 'selectable', true),
    defaultEnabled: get(column, 'defaultEnabled', true),
    selectableInChildren: get(column, 'selectableInChildren', false),
    children: column.children?.length
      ? transformationColumnsToSortItems(column.children)
      : undefined,
  }));

const transformationSortItemsToColumns = (
  items: SortItem[]
): Array<TableColumnProps<any>> =>
  items.map((item) => {
    if (item.selectableInChildren) {
      item.enabled = Boolean(
        item.children?.filter((v: any) => v.enabled).length
      );
    }

    return {
      ...item,
      hide: !item.enabled,
      children: item.children?.length
        ? transformationSortItemsToColumns(item.children)
        : undefined,
    };
  });

function ColumnSetting(props: ColumnSettingProps) {
  const { storeKey, baseRefObject, updateStatus, onConfirm, forceUseCustomColumns } = props;

  const { env } = React.useContext(dataContext);

  const refObject = useRefObject(baseRefObject);
  const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<ReportValueType<any[]>>;
  const tableRefObject =
    refObject?.version === 2
      ? useStore<any>(
          (store: Store) => {
            const reportRefObject = store.getters.withParentName(
              refObject?.path,
              'Report'
            );
            return store.getters.withChildrenName(
              reportRefObject?.path || [],
              'Table'
            )?.[0];
          },
          [refObject?.path]
        ).value
      : ((refObject as ObjectRefType<any>)
          ?.withParentName('Report')
          ?.withChildrenName('Table')?.[0] as
          | ObjectRefType<TableValueType<any>>
          | undefined);

  const { columnSettingValues } = reportRefObject?.value?.others || {};
  const { columns } = tableRefObject?.value || {};
  const initColumns = React.useMemo(
    () => {
      const _columns = forceUseCustomColumns ? (columnSettingValues || []) : (columns || []);
      return transformationColumnsToSortItems(_columns);
    },
    [columns, columnSettingValues, forceUseCustomColumns]
  );

  const sorting = useTableColumnSorter({
    initValue: initColumns,
    reportName: storeKey || '',
    onConfirm: (items: SortItem[], originItems: SortItem[]) => {
      if (onConfirm) {
        onConfirm(items, refObject, originItems)
      }
    },
  });

  const value = React.useMemo(
    () => transformationSortItemsToColumns(sorting?.sortingResult || []),
    [sorting?.sortingResult]
  );

  const onClick = React.useCallback(() => {
    if (sorting) {
      sorting.showColumnSortor();
    }
  }, [sorting]);

  React.useEffect(() => {
    if (!storeKey) {
      refObject?.logger.error('ColumnSetting 未设置 storeKey！');
    }
  }, [storeKey]);

  React.useEffect(() => {
    updateStatus?.('done', value);
  }, [value]);

  if (env?.os === 'pos' && ua.os === 'winpos') {
    return null;
  }

  return React.useMemo(
    () => (
      <>
        <Button
          type={env?.os === 'pc' ? 'link' : undefined}
          key="ColumnSetting"
          onClick={onClick}
        >
          {props.children || (
            <>
              <Icon type="setting" />
              <span>字段设置</span>
            </>
          )}
        </Button>
        {sorting?.sorterModal}
      </>
    ),
    [sorting]
  );
}

export default withAdapter(ColumnSetting, { name: 'ColumnSetting' });
