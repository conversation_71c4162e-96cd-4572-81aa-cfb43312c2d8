import React from 'react';
import {
  Select,
} from 'antd';
import { SelectProps, OptionProps } from 'antd/es/select';
import { DataSource, ValueControl } from '../types';

export type SelectMaterialProps = {
  name: 'select',
  // eslint-disable-next-line
  props?: SelectProps<any>,
  itemProps?: OptionProps,
  dataSource: DataSource<OptionProps, OptionProps['value']>,
};

type Props = SelectMaterialProps & ValueControl<SelectProps['value']>

export const SelectMaterial = (p: Props) => {
  const {
    dataSource, props = {}, itemProps = {},
    name, onChange, value, ...otherProps
  } = p;
  return (
    <Select
      {...otherProps}
      {...props}
      value={value}
      onChange={onChange}
    >
      {
        dataSource.map((_) => {
          const { name: text, value: v, props: currentProps } = _;
          const newProps = { ...itemProps, ...currentProps };
          return (
            <Select.Option value={v} {...newProps} key={v}>{text}</Select.Option>
          );
        })
      }
    </Select>
  );
};
