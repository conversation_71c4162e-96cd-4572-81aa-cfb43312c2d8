/* eslint-disable @typescript-eslint/no-explicit-any */
import { isUndefined, merge, set } from 'lodash';
import {
  AutoDataNode,
  TableColumnProps,
  TablePopelineValueType,
} from '../types';
import manager from './manager';

/** 获取数据中的维度信息 */
const getDims = (root: AutoDataNode) => {
  const _dims = new Map<string, { [k: string]: boolean }>();
  function iter(node?: AutoDataNode | null) {
    if (!node) return;
    const { groupDims } = node;
    if (groupDims) {
      Object.entries(groupDims).forEach(([group, value]) => {
        const _set = _dims.get(group) || {};
        if (value !== null) {
          _set[String(value)] = true;
        }
        _dims.set(group, _set);
      });
    }
    node.items?.forEach(iter);
  }
  iter(root);
  return _dims;
};

/** 获取列 */
const getColumns = (
  columns: Array<TableColumnProps<any>>,
  dims: Map<string, { [k: string]: boolean }>,
  preDataIndex?: string,
) => {
  const _columns: Array<TableColumnProps<any>> = [];
  columns.forEach((column) => {
    // 新的 dataIndex
    column.dataIndex = preDataIndex
      ? `${preDataIndex}-${column.dataIndex}`
      : column.dataIndex;

    // 如果列是一个维度，则这个列就是「动态列」
    if (column.isDim) {
      const _set = dims.get(`${column.dataIndex || ''}`);
      const setKeys = Object.keys(_set || {});
      // 如果 children 有配置，要按照维度的值横向扩展开
      if (setKeys.length && column.children && column.children.length) {
        setKeys.forEach((group) => {
          const dataIndex = `${column.dataIndex}[${group}]`;
          const newColumn = {
            ...column,
            title: group,
            dataIndex,
            children: getColumns(column.children || [], dims, dataIndex),
          };
          _columns.push(newColumn);
        });
      } else if (!isUndefined(_set)) {
        _columns.push(column);
      }
    } else {
      _columns.push(column);
    }
  });
  return _columns;
};

/** 获取普通表数据 */
const getTableData = (root: AutoDataNode, columns: Array<TableColumnProps<any>>, isChild?: boolean) => {
  const subFields = isChild ? columns.filter(v => Boolean(v.subField)) : [];
  return (
    root.items?.map((item) => {
      const { aggr, values, groupDims } = item;
      const children = (item.items?.length
        ? getTableData(item, columns, true)
        : undefined) as Array<{
        [field: string]: number | string;
      }>;
      const itemData = merge(
        {
          children,
        },
        aggr,
        values,
        groupDims,
      );
      subFields.forEach((v: TableColumnProps<any>) => {
        if (v.subField) {
          set(itemData, v.dataIndex as string, itemData[v.subField]);
        }
      });
      return itemData;
    }) || []
  );
};

// TODO: 交叉表的数据之后再实现
/** 获取交叉表数据 */
const getCrossTableData = () => [];

const getCrossTableSumData = () => ({});

/**
 * Auto-Report 的接口数据的兼容
 *
 * @param data
 * @returns
 */
export const auto = async (value: TablePopelineValueType<AutoDataNode>) => {
  if (!value.data) {
    return value;
  }

  const { columns, data, ...otherValues } = value;
  const root = Array.isArray(data) ? data[0] : data;
  const dims = getDims(root);
  const newColumns = getColumns(columns || [], dims);

  /** 是否为交叉表, 之前的逻辑 */
  const isCrossTable = columns.some(
    column => !!column.children?.length && column.dataIndex,
  );

  return merge(
    {
      ...otherValues,
    },
    {
      columns: newColumns,
      data: isCrossTable ? getCrossTableData() : getTableData(root, newColumns),
      others: {
        totalCount: root.page?.totalCount,
        sumData: isCrossTable ? getCrossTableSumData() : root.aggr,
      },
    },
  );
};

manager.add('auto', auto);

export default auto;
