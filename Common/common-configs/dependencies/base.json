{"name": "通用依赖-base", "version": "0.0.1", "private": true, "description": "项目将以此作为基础依赖", "engines": {"node": ">=8.0.0"}, "devDependencies": {"@mtfe/git-flow": "^0.4.8", "@mtfe/net-typings": "^5.9.0", "@types/react": "^16.8.24", "@types/react-dom": "^16.8.5", "@types/react-router": "^5.0.3", "@types/react-router-dom": "^4.3.4", "@mtfe/mpack": "1.0.55", "typescript": "4.4.4"}, "dependencies": {"@mtfe/auto-report": "1.0.0", "@mtfe/next-react-router": "^1.0.0", "@mtfe/rmsform-antd": "^1.19.14", "@mtfe/sjst-antdx": "1.20.57", "@mtfe/sjst-antdx-next": "npm:@mtfe/sjst-antdx@2.1.9-beta-1936515.0", "@mtfe/sjst-chart": "2.1.9-beta-1936515.0", "@mtfe/sjst-form": "2.1.9-beta-1936515.0", "@mtfe/sjst-report": "2.1.9-beta-1936515.0", "@mtfe/sjst-ui": "^1.17.13", "@mtfe/sjst-ui-next": "npm:@mtfe/sjst-ui@2.1.9-beta-1936515.0", "classnames": "^2.2.6", "core-js": "^3.6.4", "history": "^4.7.2", "lodash": "4.17.11", "mobx": "^4.9.4", "mobx-react": "^5.4.3", "moment": "^2.24.0", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "5.1.2", "react-router-dom": "^5.0.0", "axios": "0.21.0"}, "resolutions": {"@mtfe/rms-table": "0.0.15"}}