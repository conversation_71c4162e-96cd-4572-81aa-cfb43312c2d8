import React from 'react';
import ContextProvider from './Contexts';
import Schema from './Schema';
import globalManager from './GlobalManager';
import componentManager from './ComponentManager';
import { Config } from './types';

export interface AutoProps<StateType> {
  /**
   * 加载状态
   */
  loading?: Boolean;
  /**
   * 额外 state 数据，使其具备夸系统的引用能力
   */
  state?: StateType;
  children?: Config | any;
}

export function Auto<StateType = {}>(
  props: AutoProps<StateType> & { [k: string]: any }
) {
  const { loading, state: rootState, children, ...otherProps } = props;

  const Suspense =
    componentManager.getSuspenseFallbackComponent() || React.Fragment;

  const childrenComponent = React.useMemo(() => <Schema config={children} />, [
    children,
  ]);

  const [state, setState] = React.useState<{ loading: Boolean }>({
    loading: true,
  });

  React.useEffect(() => {
    (async () => {
      await globalManager.init(otherProps);
      setState((preState) => ({ ...preState, loading: false }));
    })();
  }, []);

  return loading || state.loading ? (
    <Suspense />
  ) : (
    <ContextProvider $root={rootState}>{childrenComponent}</ContextProvider>
  );
}

export default Auto;
