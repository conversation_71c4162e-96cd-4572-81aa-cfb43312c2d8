import { Store, StoreOptions, MapAction, MapGetter } from './types';

export interface Listener {
  (): void;
}

class XStore<S = {}, G = any> implements Store<S, G> {
  state: S;
  private _getterr: any = {};

  private _listeners: Listener[] = [];

  private _mapAction: MapAction<S, G> = {};
  private _mapGetter: MapGetter<S, G> = {};

  // 更新 getters
  private _updateGetter() {
    const getterKeys = Object.keys(this._mapGetter);
    getterKeys.forEach((k) => {
      const getter = this._mapGetter[k];
      this._getterr[k] = getter?.(this);
    });
  }

  // 触发监听
  private _runListener() {
    for (let i = 0; i < this._listeners.length; i++) {
      this._listeners[i]();
    }
  }

  constructor(initialState: S, options?: StoreOptions<S, G>) {
    this.state = initialState;
    this._mapAction = options?.actions || {};
    this._mapGetter = options?.getters || {};
    this._updateGetter();
  }

  get getters(): G {
    return this._getterr;
  }

  setState(state: S) {
    this.state = { ...this.state, ...state };
    this._updateGetter();
    this._runListener();
  }

  dispatch(name: string, payload: any) {
    this._mapAction[name]?.(this, payload);
  }

  subscribe(listener: Listener) {
    this._listeners.push(listener);
    return () => {
      const index = this._listeners.indexOf(listener);
      this._listeners.splice(index, 1);
    };
  }
}

export function create<S, G>(
  initialState: S,
  options?: StoreOptions<S, G>
): Store<S, G> {
  return new XStore<S, G>(initialState, options);
}
