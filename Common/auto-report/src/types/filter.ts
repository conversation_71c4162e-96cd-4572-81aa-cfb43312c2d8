/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { UserService } from '@mtfe/next-biz/src/contexts/user';
import type { Org } from '@mtfe/next-biz/src/services/org/types';

export type FilterProps = {
  otherProps: any,
  size?: 'small' | 'default' | 'large',
  moduleClick?: (bid?: string) => void,
};

export type Context = {
  userService: UserService | null,
}

export interface InputType {
  render?: (props: FilterProps, deps: any) => React.ReactElement | null,
  getLabel?: (value: any, deps: any) => string | undefined,
  showLabel?: () => boolean,
  getDisplayValue?: (value: any, deps?: any) => string | null | undefined,
  getDefaultValue?: () => any,
  getDeps?: () => {[k: string]: string},
  getValue?: (value: any) => any,
  getQuery?: (value: any, result: any) => any,
  isVisiable?: () => boolean,
  serialize?: (v: any) => string,
  deserialize?: (str: string) => any,
}

export interface InputConstructor {
  new (filter: Filter, context: Context): InputType;
}

export type InputSet = {[k: string]: InputType};

export type InputConstructorSet = {[k: string]: InputConstructor};

export type Filter =
  CustomFilter
  | NumberFilter
  | DateFilter
  | StringFilter
  | PoiFilter
  | EnumFilter
  | BoolFilter
  | DefaultFilter
  | GroupByCheckboxFilter
  | GroupByRadioFilter
  | GroupBySelectorFilter
  | GoodsSelectorFilter
  | AreaSelectorFilter
  | TableSelectorFilter
  | GoodsCategorySelectorFilter
  | FeeSelectorFilter
  | BrandSelectorFilter
  | GoodsSpecSelectorFilter
  | DiscountSelectorFilter
  | MealSegmentSelectorFilter
  | PaymentSelectorFilter
  | StaffSelectorFilter
  | DepartSelectorFilter
  | DictionarySelectorFilter
  | PoiCitySelectorFilter
  | CheckboxFilter;

export type FilterBase = {
  bid?: string,
  /** @deprecated 只在某种统计方式下才展示 */
  showOnGroupBy?: string,
  placeholder?: string,
  field: string,
  label?: string,
  /** 只有总部可见，或只有门店可见 */
  loginType?: 'chain' | 'poi',
  defaultValue?: number | string | Array<string | number> | null,
  validator?: (v: any) => React.ReactNode,
  help?: string,
}

export type CustomFilter = {
  /** 自定义过滤器 */
  type: InputConstructor,
} & FilterBase;

export type DefaultFilter = {
  label?: string,
  type: 'Default',
} & FilterBase;

export type NumberFilter = {
  type: 'Number',
  range: boolean, // 是否为数值区间过滤
} & FilterBase;

export type StringFilter = {
  type: 'StringInput',
} & FilterBase;

export type Option<T = any> = {
  label: string,
  value: T,
  children?: Option[],
  bid?: string,
  loginType?: 'chain' | 'poi',
};

export type DateFilterMode = 'date' | 'yesterday' | 'month' | 'week' | 'time' | '30days' | string;
export type DateFilterOption = Option & {
  mode?: DateFilterMode,
}

export type DateFilter = {
  /** 日期过滤 */
  type: 'DatePicker',
  mode: DateFilterMode,
  maxDistance?: number,
  allowClear?: boolean,
  quickOptions?: string[],
  queryType?: DateFilterOption[],
  optionsIsTag?: boolean, // queryType选择组件是否为Tags组件方式
  displayByMode?: boolean, // 导出展示的时候按当前mode格式化日期，负责按照groupBy
  maxDateCut?: boolean,
  getLabel?: (value: any, deps: any) => string | undefined,
} & FilterBase;

export type EnumFilter = {
  /** 枚举过滤 */
  type: 'EnumSelector',
  multiple: boolean,
  options: Option[],
  /** 交互类型，下拉选择或者平铺选择, 默认为 select */
  selectType?: 'select' | 'treeSelect' | 'radio',
} & FilterBase;

export type BoolFilter = {
  /** 布尔值过滤器 */
  type: 'Bool',
} & FilterBase;

export type GroupByRadioFilter = {
  /** 单选过滤器 */
  type: 'GroupByRadio',
  options: Array<Option<Array<number | string> | number | string | null>>,
} & FilterBase;

export type GroupByCheckboxFilter = {
  /** 维度单选过滤器 */
  type: 'GroupByCheckbox',
  value: string | Array<string>,
  unCheckedValue?: string | Array<string>,
} & FilterBase;

export type PoiFilter = {
  /** 业务 门店 过滤 */
  type: 'PoiSelector',
  multiple?: boolean,
  customFilter?: {
    businessModuleId: number,
  },
  useSimpleValue?: boolean,
} & FilterBase;

export type PoiAndSalesDepartFilter = {
  /** 业务 门店 & 销售部门 过滤 */
  type: 'PoiAndSalesDepartSelector',
  multiple?: boolean,
  orgFilter?: (o: Org) => boolean,
} & FilterBase;

export type OptionSet<T = any> = {
  loginType?: 'chain' | 'poi',
  label?: string,
  hide?: boolean,
  options: Option<T>[],
};

export type GroupBySelectorFilter = {
  /** 统计方式 过滤 */
  type: 'GroupBySelector',
  optionSet: Array<OptionSet>,
} & FilterBase;

export type GoodsSelectorFilter = {
  /** 菜品过滤器 */
  type: 'GoodsSelector',
  goodsType?: 'normal' | 'side' | 'box',
  multiple?: boolean,
  showBanquet?: number,
} & FilterBase;

export type GoodsCategorySelectorFilter = {
  /** 菜品分类过滤器 */
  type: 'GoodsCategorySelector',
  multiple?: boolean,
  customFilter?: {
    publishTypes: number[],
  },
  // 显示菜品分类名称，默认false
  showCateNames?: boolean,
} & FilterBase;

export type FeeSelectorFilter = {
  /** 服务费过滤器 */
  type: 'FeeSelector',
  multiple?: boolean,
} & FilterBase;

export type AreaSelectorFilter = {
  /** 桌台区域过滤器 */
  type: 'AreaSelector',
  multiple?: boolean,
} & FilterBase;

export type TableSelectorFilter = {
  /** 桌台过滤器 */
  type: 'TableSelector',
  multiple?: boolean,
} & FilterBase;

export type BrandSelectorFilter = {
  /** 品牌过滤器 */
  type: 'BrandSelector',
  multiple?: boolean,
} & FilterBase;

export type GoodsSpecSelectorFilter = {
  /** 规格过滤器 */
  type: 'GoodsSpecSelector',
  multiple?: boolean,
} & FilterBase;

export type DiscountSelectorFilter = {
  /** 优惠过滤器 */
  type: 'DiscountSelector',
  multiple?: boolean,
} & FilterBase;

export type MealSegmentSelectorFilter = {
  /** 餐段过滤器 */
  type: 'MealSegmentSelector',
  multiple?: boolean,
} & FilterBase;

export type PaymentSelectorFilter = {
  /** 支付方式过滤器 */
  type: 'PaymentSelector',
  paymentType?: Array<'payment' | 'discount'>,
  multiple?: boolean,
} & FilterBase;

export type StaffSelectorFilter = {
  /** 员工过滤器 */
  type: 'StaffSelector',
  mode?: 'staff' | 'account',
  multiple?: boolean,
} & FilterBase;

export type DepartSelectorFilter = {
  /** 部门过滤器 */
  type: 'DepartSelector',
  mode?: Array<'production' | 'administration' | 'sales'>,
  multiple?: boolean,
} & FilterBase;

export type DictionarySelectorFilter = {
  /** 字典过滤器 */
  type: 'DictionarySelector',
  mode?: 'orderSource' | 'orderCategory' | 'orderBizType' | 'orderDiningMode' | 'allSource' | 'orderInstoreBizType',
  multiple?: boolean,
} & FilterBase;

export type PoiCitySelectorFilter = {
  /** 门店所在城市选择器 */
  type: 'PoiCitySelectorFilter',
  multiple?: boolean,
} & FilterBase;

export type CheckboxFilter = {
  /** 单选过滤器 */
  type: 'Checkbox',
} & FilterBase;
