/* eslint-disable @typescript-eslint/no-explicit-any */
import { TableColumnProps } from './types';

export const recursiveColumns = (
  columns: Array<TableColumnProps<any>>,
  callback: (column: TableColumnProps<any>) => TableColumnProps<any>,
  filter?: (column: TableColumnProps<any>) => boolean
): Array<TableColumnProps<any>> => {
  return columns
    .filter((v) => (filter ? filter(v) : true))
    .map((column) => {
      return {
        ...callback(column),
        children: column.children?.length
          ? recursiveColumns(column.children, callback, filter)
          : column.children,
      };
    });
};

export const equalDataIndex = (a: string | string[], b: string | string[]) => {
  const _a = Array.isArray(a) ? a.join('.') : a;
  const _b = Array.isArray(b) ? b.join('.') : b;
  return _a === _b;
};


export const columnSort = (columns: Array<TableColumnProps<any>>, sort: {
  field: string | string[],
  order: 'descend' | 'ascend' | null
}): Array<TableColumnProps<any>> => {
  return columns.map((column) => {
    if (equalDataIndex(column.field || '', sort.field)) {
      return {
        ...column,
        sortOrder: sort.order,
      };
    }
    return {
      ...column,
      children: column.children?.length ? columnSort(column.children, sort) : column.children,
    };
  });
}