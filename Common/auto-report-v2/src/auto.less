:root .auto-report-loading-wrap {
  position: relative;
  flex: 1;
  height: 100%;
  min-height: 250px;
}

:root .auto-report-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);

  @keyframes rotation {
    from {
      transform: rotate(0deg);
      -webkit-transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
      -webkit-transform: rotate(360deg);
    }
  }

  .pageloading {
    width: 50px;
    height: 50px;
    animation: rotation 1s linear infinite;
    -webkit-animation: rotation 1s linear infinite;
  }
}
