import React, { useCallback, useMemo, useState } from 'react';
import { Navigator, BottomButton, ListItem } from '@mtfe/sjst-ui-next';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import { Config, ComponentProps } from '../../../types';
import './h5.less';

const { PickerItem, LabelItem } = ListItem;

/**
 *  影响modal内的展示大小
 *  inline: 只在modal内部暴露，和label行内用LabelItem显示
 *  picker: 只在modal内部暴露，和label行内用PickerItem显示
 *  block: 只在modal内部暴露，和label两行内用TitleItem显示
 *  默认: inline
 */
export type UiType = 'inline' | 'picker' | 'block';

/**
 *  影响外露位置
 *  默认: 只在modal内部暴露
 *  only: 只在外面暴露
 *  both: 同时在外面和modal内部暴露
 */
export type ShowType = 'only' | 'both';

interface Props extends ComponentProps<Config[]> {
  items: Config[];
  config?: {
    showType?: ShowType;
    uiType?: UiType;
  };
  analytics?: {
    submitBid: string;
    resetBid: string;
    toggleQueryBid: string;
  };
}

// 超过这个数就折叠
const DefaultExpandCount = 2;

function ReportFilter({
  items,
  baseRefObject,
  updateStatus,
  analytics,
}: Props) {
  const refObject = useRefObject(baseRefObject);
  const [isExpand, setExpand] = useState(false);

  const toggleExpand = useCallback(() => {
    analytics?.toggleQueryBid
      && refObject?.analytics.moduleClick(analytics.toggleQueryBid);
    setExpand(!isExpand);
  }, [isExpand]);

  const handleSubmit = useCallback(() => {
    analytics?.submitBid
      && refObject?.analytics.moduleClick(analytics.submitBid);
    refObject?.emit('submit@Report');
    setExpand(false);
  }, [refObject]);

  const handleReset = useCallback(() => {
    analytics?.resetBid && refObject?.analytics.moduleClick(analytics.resetBid);
    refObject?.emit('reset@Report');
    refObject?.emit('submit@Report', { isReset: true });
  }, [refObject]);

  React.useEffect(() => {
    updateStatus?.('done', items);
  }, [items]);

  const ExpandBtn = useMemo(() => {
    if (items.length < DefaultExpandCount) {
      return null;
    }
    return (
      <div onClick={toggleExpand} className="auto2-query-toggle">
        筛选
      </div>
    );
  }, [isExpand, toggleExpand, items]);

  const QueryActive = useMemo(
    () => (
      <BottomButton
        className="filter-active"
        leftButton={{
          word: '重置',
          onClick: handleReset,
        }}
        rightButton={{
          word: '查询',
          onClick: handleSubmit,
        }}
      />
    ),
    [handleSubmit, handleReset],
  );

  const renderQueryItem = useCallback((item, index) => {
    const _props = item?.props || {};
    const key = _props.dataKey;
    const classNames = ['auto2-query-item', `auto2-query-item_${key}`];

    if (item.config?.showType) {
      classNames.push(`show-type-${item.config?.showType}`);
    }

    return (
      <div key={key} className={classNames.join(' ')}>
        <div className="auto2-query-item-control">
          {refObject?.renderManager.render(item)}
        </div>
      </div>
    );
  }, []);

  const renderModalQueryItem = useCallback((item, index) => {
    const _props = item?.props || {};
    const key = index ? `${_props.dataKey}-${index}` : _props.dataKey;
    const classNames = ['auto2-query-item', `auto2-query-item_${key}`];

    const renderItem = refObject?.renderManager.render(item, {
      isTemporary: true,
    });

    if (item.config?.showType) {
      classNames.push(`show-type-${item.config?.showType}`);
    }

    switch (item?.config?.uiType) {
      case 'picker':
        classNames.push('picker_item');
        return (
          <div key={`picker_${key}`} className={classNames.join(' ')}>
            <div className="query-label"><h4>{_props.label}</h4></div>
            <PickerItem>{renderItem}</PickerItem>
          </div>
        );
      case 'block':
        classNames.push('block_item');
        return (
          <div key={`block_${key}`} className={classNames.join(' ')}>
            <label>
              <div className="query-label">
                <h4>{_props.label}</h4>
                {/* 这里输入框比较特殊单独判断一下 */}
                {item.type !== 'TextInput' ? <div className="title-right">{_props.placeholder}</div> : null}
              </div>
              <LabelItem>{renderItem}</LabelItem>
            </label>
          </div>
        );
      case 'inline':
      default:
        classNames.push('inline_item');
        return (
          <div key={`inline_${key}`} className={classNames.join(' ')}>
            <PickerItem label={_props.label} childrenTextAlign="right">
              {renderItem}
            </PickerItem>
          </div>
        );
    }
  }, []);

  return (
    <div className="auto2-query-container">
      {isExpand ? (
        <div className="container-filter-modal">
          <Navigator
            title="筛选"
            leftButtonContent={{ icon: 'x' }}
            onLeftButtonClick={toggleExpand}
          />
          <div className="filter-more">
            {items.map((item, index) => renderModalQueryItem(item, index))}
          </div>
          {QueryActive}
        </div>
      ) : (
        items.length > 0 && (
          <div className={`${items[0]?.props?.className} filter-first`}>
            {items.map((item, index) => renderQueryItem(item, index))}
            {ExpandBtn}
          </div>
        )
      )}
    </div>
  );
}

export default withAdapter(ReportFilter, { name: 'ReportFilter' });
