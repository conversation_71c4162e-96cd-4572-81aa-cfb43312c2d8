const { execFileSync, execSync, execFile } = require('child_process');
const path = require('path');

function logProcess(task) {
  return new Promise((resolve, reject) => {
    if (task) {
      task.stdout.pipe(process.stdout);
      task.stderr.pipe(process.stderr);
      task.on('exit', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject();
        }
      });
    }
  });
}
const mpackPath = path.resolve(__dirname, '../mpack/bin/index.js');

logProcess(execFile(mpackPath, ['dll'], {
  env: {
    DLL_TYPE: process.env.DLL_TYPE,
    ...process.env
  }
}))