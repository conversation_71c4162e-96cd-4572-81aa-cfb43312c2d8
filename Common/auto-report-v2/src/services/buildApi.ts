/* eslint-disable @typescript-eslint/no-explicit-any */
import { IConfig } from 'net4j';
import { IV } from '@mtfe/sjst-report/es/ReportCore/utils';
import net from '@mtfe/next-biz/es/lib/Net';
import JSONBig from '@mtfe/next-biz/es/utils/json';

type DataHandler = (data: IV) => IV;

const timeout = 20000; // 超时20S

const stringifyParams = (params: IV) => {
  const stringParams = { ...params };
  Object.entries(params).forEach(([key, value]) => {
    if (typeof value === 'object') {
      stringParams[key] = JSON.stringify(value);
    }
  });
  return stringParams;
};

export function buildGetApi(path: any, dataHandler: DataHandler | null = null) {
  return async (params: any) => {
    let data = await net.get(path, {
      params,
      timeout,
    });
    if (!data) {
      throw new Error('请求失败');
    }

    if (dataHandler) {
      // eslint-disable-next-line
      data = dataHandler(data) as any;
    }
    return data as any;
  };
}

export function buildPostApi(
  path: any,
  dataHandler: DataHandler | null = null,
  config?: IConfig,
) {
  return async (params: any) => {
    let data = await net.post(path, params, {
      timeout,
      ...config,
    });
    if (dataHandler) {
      // eslint-disable-next-line
      data = dataHandler(data) as any;
    }
    return data as any;
  };
}

export function buildPostApiCustom<QueryType, ResponseType>(
  path: string,
  dataHandler: DataHandler | null = null,
  config?: IConfig,
) {
  return async (params: QueryType) => {
    let data = await net.post(path, params, {
      timeout,
      ...config,
    });
    if (dataHandler) {
      // eslint-disable-next-line
      data = dataHandler(data) as any;
    }
    return data as ResponseType;
  };
}

/**
 * 数据组接口返回值是 json string，这里统一处理
 */
export function buildDBGetApi(path: any, dataHandler: DataHandler | null = null) {
  return async (params: any) => {
    let data = await net.get(path, {
      params,
      timeout,
    });
    if (typeof data === 'string') { // 数据组后端返回的是 json str
      data = JSONBig()(data);
    }

    if (dataHandler) {
      // eslint-disable-next-line
      data = dataHandler(data) as any;
    }
    return data as any;
  };
}

/**
 * 数据组接口入参、返回值都是 json string，这里统一处理
 */
export function buildDBPostApi(path: any, dataHandler: DataHandler | null = null) {
  return async (params: any) => {
    let data = await net.post(path, {
      reqJson: JSON.stringify(params),
    } as IV, {
      timeout,
    });
    if (typeof data === 'string') { // 数据组后端返回的是 json str
      data = JSONBig()(data);
    }

    if (dataHandler) {
      // eslint-disable-next-line
      data = dataHandler(data) as any;
    }
    return data as any;
  };
}

/**
 * 数据组接口入参、返回值都是 json string，这里统一处理
 */
export function buildMergePostApi(path: any) {
  return async (params: any) => {
    const data = await net.post(path, stringifyParams(params), {
      timeout,
    });
    if (typeof data !== 'object') return null;
    const result: IV = {};
    const errors = new Set();
    Object.entries(data).forEach(([k, v]) => {
      if (typeof (v as IV)?.data === 'string') {
        result[k] = JSONBig()((v as IV).data);
      } else if (typeof result[k] === 'object') {
        result[k] = (v as IV).data;
      }

      if ((v as IV)?.code && ((v as IV)?.code !== 0 || (v as IV)?.code !== 200)) {
        errors.add(`${(v as IV)?.msg}`);
      }
    });
    if (errors.size > 0) {
      const e = new Error(Array.from(errors).join(', '));
      e.stack = 'MergeRequestFailed';
      throw e;
    }
    return result as any;
  };
}

/**
 * 导出API
 */
export function buildDownloadApi(path: any) {
  return async (params: any) => await net.get(path, {
    params,
    timeout,
  });
}

export function buildAutoApi(queryKey: string) {
  // @ts-ignore
  const func = buildDBPostApi(`api/v2/reports/common-api/query/${queryKey}`);
  return (params: IV) => func({
    ...params,
    queryKey,
  }) as Promise<any>;
}
