/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { FormInstance } from '@mtfe/rmsform-antd';

export type InnerContext = {
  formRefs: { [i: string]: any };
  registerFormRef?: (id: string, ref: any) => void;
  unregistterFormRef?: (id: string) => void;
  topRef: React.RefObject<FormInstance>;
};

export const AutoFormContext = React.createContext<InnerContext>({
  formRefs: {},
  topRef: React.createRef(),
});

export class ContextWrapper<T, F> extends React.Component<T, F> {
  registerFormRef = (key: string, ref: any) => {
    this.innerContext.formRefs[key] = ref;
  };

  unregistterFormRef = (key: string) => {
    delete this.innerContext.formRefs[key];
  };

  innerContext: InnerContext = {
    formRefs: {},
    registerFormRef: this.registerFormRef,
    unregistterFormRef: this.unregistterFormRef,
    topRef: React.createRef(),
  };

  checkAllRefs = () => {
    const refs = Object.values(this.innerContext.formRefs);
    return Promise.all(refs.map((_: any) => _?.check()));
  };

  setTopRef = (f: React.RefObject<FormInstance>) => {
    this.innerContext.topRef = f;
  };

  wrapContext = (dom: JSX.Element) => {
    const { innerContext } = this;
    return <AutoFormContext.Provider value={innerContext}>{dom}</AutoFormContext.Provider>;
  };
}
