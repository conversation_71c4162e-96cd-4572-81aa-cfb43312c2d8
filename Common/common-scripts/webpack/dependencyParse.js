/* eslint-disable */
const DependenceParsePlugin = require('dependency-parse-plugin');

// Talos部署环境，可以用来区分本地开发还是Talos发布打包
const env = process.env.AWP_DEPLOY_ENV || '';

const log = (arr, name) => {
  console.log(`检测到${arr.length}个非法${name}引入`);
  arr.forEach((_) => {
    let str = `========================${name} 调用栈========================\r\nImport ${name}:\r\n`;
    str += _.join(`\r\n`)
    str += `\r\n\r\n`;
    console.log(str);
  });
};


const callback = ({
  detectDependences,
  directDeps,
  circyleDeps,
}) => {
  const arr = detectDependences.filter((_) => _.paths.length);
  const logArr = () => {
    arr.forEach(({name, paths}) => {
      log(paths, name);
    });
  };
  // 循环依赖
  if (circyleDeps.length) {
    // 日志后置，避免被冲掉，忽略信息~
    setTimeout(() => {
      console.log(`\r\n\r\n===========当前依赖检测存在循环依赖 ${circyleDeps.length} 个 ===========\r\n`);
      console.log(JSON.stringify(circyleDeps, null, 2));
      console.log(`\r\n===========当前依赖检测存在循环依赖 ${circyleDeps.length} 个 ===========\r\n`);
    }, 100)
  }

  if ((arr.length)) {
    if (env) {
      logArr();
      // throw new Error('依赖检测不通过，请修复后再进行发布');
    } else {
      // 日志后置，避免被冲掉，忽略信息~
      setTimeout(logArr, 100)
    }
  }
};

module.exports = () => {
  return new DependenceParsePlugin({
    callback,
    excludes: [
    ],
    detectDependences: [{
        name: '@dp/knb',
        blackList: [/.*/],
        whiteList: [
          'packages/projects/rms-help/src/pages/h5/index.tsx',
          'packages/projects/rms-help/src/pages/pos/index.tsx',
          'packages/projects/rms-message/src/pages/mobile/index.tsx',
          'packages/projects/rms-online/src/pages/h5/index.tsx',
          'packages/projects/rms-online/src/pages/pos/index.tsx',
          'Pages/rms-payment/src/pages/h5/index.tsx',
          'rms-report/src/pages/h5/index.tsx',
          'rms-report/src/pages/pos/index.tsx',
          'Modules/h5-rms-online/index.tsx',
          'Modules/pos-rms-online/index.tsx',
          'packages/projects/rms-online/src/utils/mobileReady.ts',
          'rms-service-market/src/pages/H5/Renewal/index.tsx',
          'rms-service-market/src/utils/mobileReady.ts',
        ],
      },
      {
        name: '@mtfe/micro',
        blackList: [
          'packages/projects/rms-help/src/pages/h5',
          'packages/projects/rms-help/src/pages/pos',
          'packages/projects/rms-message/src/pages/mobile',
          'packages/projects/rms-online/src/pages/h5',
          'packages/projects/rms-online/src/pages/pos',
          'Pages/rms-payment/src/pages/h5',
          'rms-report/src/pages/h5',
          'rms-report/src/pages/pos',
        ],
        whiteList: [],
      },
    ]
  });
};