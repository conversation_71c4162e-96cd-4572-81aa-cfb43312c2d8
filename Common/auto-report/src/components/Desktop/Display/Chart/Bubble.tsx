/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
} from '@antv/g2';
import { BubbleChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart, { Data } from './useBasicChart';
import { Color } from '../../../common/chartConst';
import formatter from '../../../common/format';

function sortField(props: Props<BubbleChartProps>) {
  const arr = [props.x, props.y, props.size];
  // 按照 index 排序
  arr.sort((a, b) => {
    const ai = a.index || 0;
    const bi = b.index || 0;
    return ai - bi;
  });
  return arr;
}

export default function BubbleChart(props: Props<BubbleChartProps>) {
  const {
    y,
    x,
    legend,
    width,
    height,
    size,
    customLoadData,
    customRenderer,
  } = props;
  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    const tooltipArr = sortField(props);

    // 添加主线
    const geo = chart
      .point()
      .size(size.field)
      .position(`${x.field}*${y.field}`)
      .color({
        values: Color,
      })
      .tooltip(tooltipArr.map(t => t.field).join('*'))
      .shape('circle');

    chart.scale({
      [x.field]: {
        alias: x.label,
        nice: true,
        formatter: v => formatter(x.format, v),
      },
      [y.field]: {
        alias: y.label,
        nice: true,
        formatter: v => formatter(y.format, v),
      },
      [size.field]: {
        alias: size.label,
        min: 0,
        range: [0, 1],
        formatter: v => formatter(size.format, v),
      },
    });

    if (legend) {
      chart.scale(legend.field, { alias: legend.label });
      chart.legend(legend.field, {
        position: 'bottom',
        slidable: false,
      });
      geo.color(legend.field, Color);
    } else {
      geo.color({
        fields: [x.field],
        values: Color,
      });
      chart.legend(false);
    }

    chart.axis(x.field, {
      title: {},
      grid: {
        line: {
          style: {
            stroke: '#d9d9d9',
            lineWidth: 1,
            lineDash: [2, 2],
          },
        },
      },
    });

    chart.axis(y.field, {
      title: {},
    });

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
      showTitle: false,
      itemTpl: '<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}</li>',
    });

    chart.filter(x.field, null);
    chart.filter(size.field, null);
    chart.filter(y.field, null);

    return chart;
  }, [x, y, legend]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    chart.data(data);
  }, [x]);

  const dims = React.useMemo(() => [x.field, legend?.field], [x, legend]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
    customLoadData,
    customRenderer,
  });

  return dom;
}

registorComponent('BubbleChart', BubbleChart);
