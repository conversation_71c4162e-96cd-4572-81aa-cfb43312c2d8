import React from 'react';
import { PoiTreeSelectorV2 } from '@mtfe/next-biz/src/components/Org/Poi';
import { Org } from '@mtfe/next-biz/src/services/org/types';
import {
  Filter,
  PoiFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';
import { serialize, deserialize } from './utils';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: PoiFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as PoiFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  getDefaultValue() {
    if ('defaultValue' in this.config) {
      return this.config.defaultValue;
    }
    return [];
  }

  serialize(org: Org) {
    return serialize([org.id, org.poiId || 0, org.name]);
  }

  deserialize(str: string) {
    const [id, poiId, name] = deserialize(str);
    return { id, poiId, name };
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <PoiTreeSelectorV2
        size={props.size}
        useEntity
        useMultiplePopSearch
        multiple={!!multiple}
        placeholder="全部"
        options={{
          forceAuthControl: true,
          showCityFilter: true,
        }}
        customFilter={{
          frozen: true,
          ...this.config?.customFilter,
          businessModuleId: undefined,
        }}
        useSimpleValue={this.config?.useSimpleValue || false}
        {...otherProps}
      />
    );
  }

  getDisplayValue(org?: Org | Org[]) {
    return toArray(org)?.map(o => o.name).join('，') || '全部';
  }

  getValue(org: Org | Org[] | undefined) {
    const { userService } = this.context;
    let empty = false;
    if (Array.isArray(org) && !org.length) {
      empty = true;
    } else if (!org) {
      empty = true;
    }
    if (empty && userService?.isHeadOffice()) {
      return userService.account.managedPoiIds;
    }
    return toArray(org)?.map(o => o.poiId || 0);
  }
}
