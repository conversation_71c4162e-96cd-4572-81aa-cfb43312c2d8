const React = require('react');
const { renderHook, act } = require('@testing-library/react-hooks');
const renderer = require('react-test-renderer');
const { default: useFullscreen } = require('./useFullscreen');
const ReactDom = require('react-dom');
const {
  ReportContext,
} = require('../../../types');

describe('useFullscreen', () => {
  const wrapper = ({ children }) => (
    <ReportContext.Provider value={report} >{children}</ReportContext.Provider>
  );
  let tools;
  const report = {
    toolsManager: {
      updateTools: (t) => tools = t,
    },
  };

  let oldCreatePortal, portal;
  beforeEach(() => {
    const oldCreatePortal = ReactDom.createPortal;
    ReactDom.createPortal = (dom, container) => {
      portal = container;
      return dom;
    };
  });

  afterEach(() => {
    ReactDom.createPortal = oldCreatePortal;
  });

  it('会在工具栏上设置进入全屏的按钮', () => {
    renderHook(() => useFullscreen({}), { wrapper });
    expect(tools.fullscreen.label).toBe('全屏展示');
  });

  it('点击全屏后，进入全屏状态，点击返回取消全屏状态', async () => {
    let instance;
    renderer.act(() => {
      const Component = () => {
        const container = useFullscreen({});
        return container(<div id="testdiv1234" />);
      };
      instance = renderer.create(
        <ReportContext.Provider value={report} >
          <Component />
        </ReportContext.Provider>
      );
    });
    renderer.act(() => {
      tools.fullscreen.onClick();
    });
    let dom = instance.root.findByProps({ className: 'auto-report-fullscreen'})
    expect(dom).not.toBeNull();
    dom = instance.root.findByProps({ id: 'testdiv1234'})
    expect(dom).not.toBeNull();
    expect(portal.parentElement).toBe(document.body);

    const title = instance.root.findByProps({ className: 'auto-report-fullscreen-title'})
    renderer.act(() => {
      title.props.onClick();
    });
    expect(() => {
      instance.root.findByProps({ className: 'auto-report-fullscreen'})
    }).toThrowError();
    dom = instance.root.findByProps({ id: 'testdiv1234'})
    expect(dom).not.toBeNull();
  });
});
