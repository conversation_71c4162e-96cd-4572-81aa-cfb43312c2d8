import React from 'react';
import {
  DataNode,
} from '../../types/response';

type Result = {
  rows: Record<string, string | number>[],
  dims: Record<string, string | number>,
  deepth: number,
};

export default function useParseData(root: DataNode | null, dimsToPick: (string | undefined)[]) {
  const data = React.useMemo(() => {
    if (!root) return null;
    const newdimsToPick = dimsToPick.filter(Boolean);
    newdimsToPick.pop();

    const iter = (r: Result, node: DataNode) => {
      const { groupDims, aggr, values } = node;
      let stopIter = false;
      if (r.deepth !== 0) {
        const dim = newdimsToPick[r.deepth - 1];
        if (!dim || (groupDims && !(dim in groupDims))) {
          stopIter = true;
        }
      }

      if (stopIter || !node.items?.length) {
        const rData = {
          ...r.dims, ...groupDims, ...aggr, ...values,
        };
        if (Object.keys(rData).length) {
          r.rows.push(rData);
        }
      } else {
        node.items.reduce(iter, {
          dims: { ...r.dims, ...groupDims },
          rows: r.rows,
          deepth: r.deepth + 1,
        });
      }
      return r;
    };

    const result = [{ ...root, groupDims: undefined }].reduce(iter, {
      deepth: 0,
      dims: {},
      rows: [],
    });
    return result.rows;
  }, [root, dimsToPick]);
  return { data };
}
