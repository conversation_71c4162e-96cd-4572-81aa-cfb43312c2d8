const React = require('react');
const { default: useColumns, indexColumn } = require('./useColumns');
const renderer = require('react-test-renderer');
const { Tooltip, Icon } = require('antd');

describe('useColumns', () => {
  let columns, getResult, dims, showIndex;
  function reset() {
    columns = [];
    getResult = null;
    dims = new Map();
    showIndex = false;
  }

  reset();

  beforeEach(() => {
    let result;
    const Component1 = () => {
      result = useColumns({ columns, dims, showIndex });
      return null;
    };
    getResult = () => {
      renderer.act(() => {
        renderer.create(<Component1 />);
      });
      return result;
    };
  });

  afterEach(reset);

  describe('tableColumns', () => {
    it('默认column.field 与 column.dataIndex 相等', () => {
      columns = [{
        field: '1234',
      }];
      const { tableColumns } = getResult();
      expect(tableColumns[0].dataIndex).toBe('1234');
    });
    describe('列', () => {
      it('如果format为金额，列的title上自动增加「（元）」', () => {
        columns = [{
          label: '金额',
          field: '1234',
          format: 'currency',
        }];
        const { tableColumns } = getResult();
        expect(tableColumns[0].title).toMatch(/\(元\)$/);
      });

      it('如果format为金额或者数组，则列右对齐', () => {
        columns = [{
          format: 'currency',
          field: '123',
        }, {
          format: 'number',
          field: '4355',
        }, {
          format: 'string',
          field: '456',
        }];
        const { tableColumns } = getResult();
        expect(tableColumns[0].align).toBe('right');
        expect(tableColumns[1].align).toBe('right');
        expect(tableColumns[2].align).toBe(undefined);
      });

      it('设置format后，会按照指定格式渲染', () => {
        columns = [{
          label: '金额',
          field: 'amount',
          format: 'currency',
        }];

        const { tableColumns } = getResult();
        expect(tableColumns[0].render({ data: 112300 }).children).toBe('1,123.00');
      });

      it('如果设置了help，则title旁边加入图标，且被ToolTip包裹', () => {
        columns = [{
          label: '标题',
          help: '说明文案',
        }];
        const { tableColumns } = getResult();
        const column = tableColumns[0];
        const title = renderer.create(column.title);
        expect(title.root.type).toBe(Tooltip);
        expect(title.root.findAllByType(Icon).length).toBe(1);
        expect(title.root.props.children[0]).toBe(columns[0].label);
      });

      it('transform返回null时，隐藏列', () => {
        columns = [{
          field: 'a',
        }, {
          field: 'b',
          transform: () => null,
        }];

        const { tableColumns } = getResult();
        expect(tableColumns.length).toBe(1);
        expect(tableColumns[0].field).toBe('a');
      });

      it('transform 接收一个参数，且返回一个新的 column', () => {
        const column = { field: 'b' };
        const transform = jest.fn((v) => {
          expect(v.dataIndex).toBe('b');
          expect(v.title).toBe('test');
          return column;
        });
        columns = [{
          field: 'a',
        }, {
          label: 'test',
          field: 'b',
          transform,
        }];
        const { tableColumns } = getResult();
        expect(transform.mock.calls.length).toBe(1);
        expect(tableColumns[1]).toBe(column);
      });

      it('showIndex为true时，添加一列序号', () => {
        showIndex = true;
        const { tableColumns } = getResult();
        expect(tableColumns[0].dataIndex).toEqual(indexColumn.field);
      });
    });

    describe('多维度', ( ) => {
      const dimValues =  {'key1': true, 'key2': true, 'key3': true, 'key4': true };
      beforeEach(() => {
        columns = [{
          field: 'parentKey',
          children: [{
            field: 'subKey1',
          }, {
            field: 'subKey2',
          }],
        }];
        dims = new Map();
        dims.set('parentKey', dimValues);
      });

      it('当 dims 存在维度，且column没有子列，不展开column', () => {
        columns = [{ field: 'parentKey' }];
        const { tableColumns } = getResult();
        expect(tableColumns.length).toBe(1);
        expect(tableColumns[0].dataIndex).toBe('parentKey');
      });

      it('当 dims 存在维度，且column有子列，横向展开column', () => {
        const { tableColumns } = getResult();
        const keys = Object.keys(dimValues);
        expect(tableColumns.length).toBe(keys.length);
        tableColumns.forEach((column, index) => {
          expect(column.dataIndex).toBe(`parentKey[${keys[index]}]`);
          expect(column.children[0].dataIndex).toBe(`parentKey[${keys[index]}]-subKey1`);
          expect(column.children[1].dataIndex).toBe(`parentKey[${keys[index]}]-subKey2`);
        });
      });

      it('showWithParentDims在维度为某几个值时才显示', () => {
        columns[0].children[0].showWithParentDims = ['key1', 'key4'];
        const { tableColumns } = getResult();
        expect(tableColumns[0].children[0].dataIndex).toEqual('parentKey[key1]-subKey1');
        expect(tableColumns[0].children[1].dataIndex).toEqual('parentKey[key1]-subKey2');
        expect(tableColumns[1].children[0].dataIndex).toEqual('parentKey[key2]-subKey2');
        expect(tableColumns[1].children.length).toBe(1);
        expect(tableColumns[2].children[0].dataIndex).toEqual('parentKey[key3]-subKey2');
        expect(tableColumns[2].children.length).toBe(1);
        expect(tableColumns[3].children[0].dataIndex).toEqual('parentKey[key4]-subKey1');
        expect(tableColumns[3].children[1].dataIndex).toEqual('parentKey[key4]-subKey2');
      });

      it('hideWithParentDims在维度为某几个值时才显示', () => {
        columns[0].children[0].hideWithParentDims = ['key2', 'key3'];
        const { tableColumns } = getResult();
        expect(tableColumns[0].children[0].dataIndex).toEqual('parentKey[key1]-subKey1');
        expect(tableColumns[0].children[1].dataIndex).toEqual('parentKey[key1]-subKey2');
        expect(tableColumns[1].children[0].dataIndex).toEqual('parentKey[key2]-subKey2');
        expect(tableColumns[1].children.length).toBe(1);
        expect(tableColumns[2].children[0].dataIndex).toEqual('parentKey[key3]-subKey2');
        expect(tableColumns[2].children.length).toBe(1);
        expect(tableColumns[3].children[0].dataIndex).toEqual('parentKey[key4]-subKey1');
        expect(tableColumns[3].children[1].dataIndex).toEqual('parentKey[key4]-subKey2');
      });
    });
  });
});
