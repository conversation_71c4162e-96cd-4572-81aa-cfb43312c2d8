/* eslint-disable react/no-children-prop */
import * as React from 'react';
import { Spin, message } from '@mtfe/sjst-antdx-next';
import { chainCancelDefaultQuery } from '../../../auto-report-v2/src/services/common';
import { getService } from '@mtfe/next-biz/src/services/user';
import useReport from '../managers/useReport';
import ReportComponent from './ReportComponent';
import { envsContext, ContainerContext, ReportContextValue } from '../types/context';
import {
  Props, ReportContext, ReportProps, ViewConfig, Component,
} from '../types';
import './Report.less';

export default function Report(props: Props<ReportProps>) {
  const { notCancelDefaultQuery } = props;
  const envs = React.useContext(envsContext);
  const containerContext = React.useContext(ContainerContext);
  let report: ReportContextValue;

  const haveReportFilter = React.useMemo(() => {
    if (props.haveFilter) return true;
    // eslint-disable-next-line
    const iter = (children: Props<unknown>['children'] | string | number | ViewConfig | Component<any>): boolean => {
      if (!children) return false;
      if (Array.isArray(children)) {
        return !!children.find(iter);
      } else if (typeof children === 'object') {
        if ('type' in children && children.type === 'Report') return false;
        if ('type' in children && children.type === 'ReportFilter') return true;
        if ('props' in children) return iter(children.props.children);
        return false;
      }
      return false;
    };
    return iter(props.children);
  }, [props.children, props.haveFilter]);

  if (props.useReport) {
    report = props.useReport(props, haveReportFilter);
  } else {
    report = useReport(props, haveReportFilter);
  }

  const { filterManager, requestManager, inited } = report || {};
  const {
    prepareQuery,
  } = filterManager || {};
  const { updateDataByQuery } = requestManager || {};
  const { visiable } = containerContext || {};
  const isFirstLoad = React.useRef(true);
  const firstRequestFinished = React.useRef(false);

  /** 当查询条件更新时，发起请求 */
  React.useEffect(() => {
    (async () => {
      if (containerContext && !visiable) return;
      if (!prepareQuery || !updateDataByQuery) return;
      if (!inited) return;
      if (isFirstLoad.current) {
        isFirstLoad.current = false;
        const user = await getService();
        const isTll = await chainCancelDefaultQuery();
        if (user.isHeadOffice() && isTll && !notCancelDefaultQuery) {
          return;
        } else {
          firstRequestFinished.current = true;
        }
      } else {
        firstRequestFinished.current = true;
      }
      const _query = prepareQuery(true);
      updateDataByQuery(_query, () => {
        if (envs.os === 'pos') {
          message.success('查询成功');
        }
      });
    })()
  }, [updateDataByQuery, containerContext, visiable, prepareQuery, inited, notCancelDefaultQuery]);

  if (!report) {
    return (
      <Spin
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />
    );
  }

  return (
    <ReportContext.Provider value={{...(report || {}), firstRequestFinished: firstRequestFinished.current}}>
      <ReportComponent>{props.children}</ReportComponent>
    </ReportContext.Provider>
  );
}
