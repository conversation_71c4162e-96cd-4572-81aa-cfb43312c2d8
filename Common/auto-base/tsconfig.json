{"extends": "../common-configs/tsconfig.json", "compilerOptions": {"allowJs": true, "typeRoots": ["./typings/*", "node_modules/@types"], "resolveJsonModule": true, "target": "es5", "rootDirs": ["src", "typings"], "module": "esnext", "lib": ["es2017", "dom", "es2017.object", "es2015"], "types": ["prop-types", "react", "node", "jest"], "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {}}, "include": ["./src"], "exclude": ["*.test.js", "*.test.jsx"]}