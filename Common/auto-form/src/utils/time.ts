/**
 * 时间操作相关
 */
import moment, { Moment } from 'moment';

export const number2moment = (stamp?: number) => {
  if (stamp === undefined) {
    return undefined;
  }
  return moment(stamp);
};

export const moment2number = (m?: Moment | null) => {
  if (m) {
    return +m.format('x');
  }
  return undefined;
};

export const time2String = (m?: number | null, _format?: string) => {
  if (m) {
    return moment(m).format(_format);
  }
}

// 时间段组转 string[]
export const rangeToStringList = (list: number[][], _format: string) => {
  if (!list) {
    return []
  }
  return list.filter((item) => item.length) .map((range) => {
    return `${moment(range[0]).format(_format)}-${moment(range[1]).format(_format)}`;
  })
}

export const stringToRangeList = (list?: string[], format?: string) => {
  if (!list) {
    return []
  }
  return list.map((timeStr) => {
    const range = timeStr.split('-');
    return [+moment(range[0], format), +moment(range[1], format)];
  })
}
