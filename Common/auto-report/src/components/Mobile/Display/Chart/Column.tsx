/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
  Data,
} from '@antv/f2';
import { ColumnChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart from './useBasicChart';
import { Color } from '../../../common/chartConst';

export default function ColumnChart(props: Props<ColumnChartProps>) {
  const {
    y,
    x,
    legend,
    width,
    height,
    y1,
  } = props;
  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    // 添加主线
    const line = chart
      .interval()
      .position(`${x.field}*${y.field}`)
      .adjust({
        type: 'dodge',
        marginRatio: 0.05,
      });

    if (legend) {
      chart.legend(legend.field, {
        position: 'bottom',
        align: 'center',
      });
      line.color(legend.field, Color);
    } else {
      line.color(Color);
    }
    chart.tooltip({
      triggerOn: ['touchstart', 'touchmove', 'mousedown', 'mousemove'],
      triggerOff: ['touchend', 'mouseout'],
      showCrosshairs: true,
      showItemMarker: false,
    });

    if (y1) {
      chart.line()
        .position(`${x.field}*${y1.field}`)
        .color(Color);
      chart.axis(y1.field, { grid: null });
    }

    return chart;
  }, [x, y, legend]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    chart.source(data);
  }, [x]);

  const dims = React.useMemo(() => [x.field, legend?.field], [x, legend]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
  });

  return dom;
}

registorComponent('ColumnChart', ColumnChart);
