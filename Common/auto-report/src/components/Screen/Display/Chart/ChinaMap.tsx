/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
} from '@antv/g2';
import DataSet from '@antv/data-set';
import { View } from '@antv/data-set/lib/view';
import { divide } from 'number-precision';
import { ChinaMapChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasicChart, { Data } from './useBasicChart';
import format from '../../../common/format';
import './ChinaMap.less';

type Geo = typeof import('./chinaGeo').default;

type Options = { field: string; geoDataView: View; geoView: View; as: [string, string] };

function transform(view: View, options: Options) {
  const field = options.field;

  const geoView = options.geoView || options.geoDataView; // alias
  const as = options.as;
  const lonField = as[0];
  const latField = as[1];
  view.rows.forEach((row) => {
    const feature = geoView.findRow((r: { properties: { id: number; }; }) => r.properties.id === row[field]);
    if (feature) {
      if (geoView._projectedAs) {
        row[lonField] = feature[geoView._projectedAs[0]];
        row[latField] = feature[geoView._projectedAs[1]];
      } else {
        row[lonField] = feature.longitude;
        row[latField] = feature.latitude;
      }
    }
  });
}

DataSet.registerTransform('geo.id', transform);

export default function ChinaMapChart(props: Props<ChinaMapChartProps>) {
  const {
    width,
    height,
    style,
    province,
    value,
  } = props;
  const [mapView, setMapView] = React.useState<View | null>(null);
  const dataSet = React.useMemo(() => new DataSet(), []);
  const [geo, setGeo] = React.useState<Geo | null>(null);

  const loadGeo = React.useCallback(async () => {
    const _geo = (await import('./chinaGeo')).default;
    const map = dataSet.createView('back')
      .source(_geo, {
        type: 'GeoJSON',
      });
    setMapView(map);
    setGeo(_geo);
  }, [dataSet, setMapView, setGeo]);

  React.useEffect(() => {
    loadGeo();
  }, [loadGeo]);

  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    if (!mapView) return;
    chart.scale({
      longitude: {
        sync: true,
      },
      latitude: {
        sync: true,
      },
      [value.field]: {
        formatter: v => format(value.format, v),
        alias: value.label,
      },
      [province.field]: {
        formatter: v => format(province.format, v),
        alias: province.label,
      },
    });

    chart.tooltip({
      showTitle: false,
    });

    chart.scale({
      [province.field]: {
        alias: province.label,
      },
      [value.field]: {
        alias: value.label,
        formatter: v => format(value.format, v),
      },
    });
    return chart;
  }, [mapView, dataSet, province, value]);

  type ArrayElement<T> = T extends Array<infer U> ? U : never;
  const parseData = React.useCallback((data: Data | null) => {
    const map: {
      [k: string]: ArrayElement<Data>,
    } = {};
    if (data) {
      data.forEach((d) => {
        const id = d[province.field];
        map[String(id || '').slice(0, 2)] = d;
      });
    }
    return geo?.features.map((feature) => {
      const id = feature.properties.id;
      const d = map[id];
      return {
        [province.field]: id,
        [value.field]: (d && d[value.field]) || 0,
      };
    }) as Data;
  }, [geo, province, value]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    if (!mapView) return;
    const max = data.reduce((m, d) => Math.max(m, Number(d[value.field] || 0)), 0);
    const userDv = dataSet.createView()
      .source(data)
      .transform({
        geoDataView: mapView,
        field: province.field,
        // @ts-ignore type 暂无法扩展
        type: 'geo.id',
        as: ['longitude', 'latitude'],
      });

    const userView = chart.createView();
    const rows = userDv.rows;
    userView.axis(false);
    userView.data(rows);

    userView.polygon()
      .position('longitude*latitude')
      .color(value.field, '#BAE7FF-#002D99')
      .style({
        stroke: 'rgb(3,10,49)',
        lineWidth: 1.5,
      });
    userView.interaction('element-active');
    chart.legend(value.field, {
      min: 0,
      max: divide(max, 100),
      slidable: false,
      position: 'bottom-left',
      track: {
        style: {
          fill: 'l(0) 0:#BAE7FF 1:#002D99',
        },
      },
    });
  }, [dataSet, mapView, province, value]);

  const dims = React.useMemo(() => [province.field], [province]);
  const children = (
    <img className="nanhai" src={require('../../../../assets/nanhai.png')} />
  );

  const dom = useBasicChart({
    className: 'auto-report-screen-chinamap',
    renderer,
    parseData,
    dims,
    width,
    height,
    loadData,
    style,
    children,
  });

  return dom;
}

registorComponent('ChinaMapChart', ChinaMapChart);
