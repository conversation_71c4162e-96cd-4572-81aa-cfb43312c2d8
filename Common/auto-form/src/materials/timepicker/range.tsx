import React from 'react';
import {
  TimePicker,
} from 'antd';
import classNames from 'classnames';
import { TimePickerProps } from 'antd/es/time-picker';
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type TimeRangePickerMaterialProps = {
  name: 'time-range-picker',
  props?: TimePickerProps,
  startProps?: TimePickerProps,
  endProps?: TimePickerProps,
};

type Value = number | undefined;
type Props = TimeRangePickerMaterialProps & ValueControl<Value[] | undefined>;

export const TimeRangePickerMaterial = (p: Props) => {
  const {
    value = [], onChange, startProps, endProps,
    name, props, ...otherProps
  } = p;
  return (
    <div className={classNames('auto-form-time-range-picker', props?.className || '')}>
      <TimePicker
        {...otherProps}
        {...props}
        {...startProps}
        value={number2moment(value[0])}
        onChange={(e) => {
          onChange?.([moment2number(e), value[1]]);
        }}
      />
      <span style={{margin: '0 8px'}}>至</span>
      <TimePicker
        {...otherProps}
        {...props}
        {...endProps}
        value={number2moment(value[1])}
        onChange={(e) => {
          onChange?.([value[0], moment2number(e)]);
        }}
      />
    </div>
  );
};
