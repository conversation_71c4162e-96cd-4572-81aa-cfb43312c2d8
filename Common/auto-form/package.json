{"name": "@mtfe/auto-form", "version": "1.0.0", "description": "", "main": "src", "scripts": {"init": "yarn", "start:doc": "cm-tools storybook start", "build:doc": "cm-tools storybook build"}, "author": "", "license": "ISC", "devDependencies": {"@mtfe/sjst-cm-tools": "^0.0.6-alpha.17", "klaw-sync": "^6.0.0", "@mtfe/mpack": "^1.0.17", "@mtfe/next-router": "^1.0.62", "@mtfe/sjst-antdx": "^1.14.6", "@types/invariant": "^2.2.30", "@types/js-cookie": "^2.2.2", "@types/prop-types": "^15.0.0", "@types/react": "^16.0.0", "@types/react-dom": "^16.0.0", "@types/react-router-dom": "^4.3.4", "archer-svgs": "^0.2.4", "braft-editor": "~2.3.7", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "handsontable": "6.2.2", "jest": "^24.8.0", "jest-css-modules": "^2.1.0", "jest-enzyme": "^7.1.2", "react": "^16.8.0", "react-dom": "^16.8.0", "ts-jest": "^24.3.0", "typescript": "3.8.2", "utility-types": "^3.7.0"}}