/* eslint-disable @typescript-eslint/no-explicit-any */
import { TableColumnProps } from '../Table/types';
import {
  Config,
  ReportQuery,
  ComponentProps,
  ObjectRefType,
  UserType,
} from '../../../types';

export type ExportDataType = {
  user?: UserType | null; // 用户信息
  title?: string;
  originalQuery?: ReportQuery;
  query?: ReportQuery;
  queryItems?: Config[];
  columns?: TableColumnProps<any>[];
  options?: { [id: string]: any };
  totalCount?: number;
  queryInfo?: string;
  reportValue?: any;
  exportPermissionCode?: number | number[];
};

export type exportCallback = (
  refObject: ObjectRefType,
  data: ExportDataType
) => Promise<{
  code?: number;
  reportName?: string;
  link?: string;
  totalCount?: number; // -1 为超过 20 万条
  resTotalCount?: number; // 导出接口返回的总数
  isNotAuth?: boolean;
}>;

export interface ExportProps extends ComponentProps {
  /** 权限 code */
  permissionCode?: number | number[];
  passWhileSome?: boolean;
  /** 埋点 bid */
  bid?: string;
  type: 'auto-report' | 'excel-get-export'| 'excel-post-export' | exportCallback;
  beforeCallback?: (data: ExportDataType, refObject?: ObjectRefType) => Promise<ExportDataType>;
  callback?: (error?: Error) => void;
  options?: { [id: string]: any };
  /** 前端是否做总数限制，默认限制 */
  notTotalLimit?: boolean;
  /** 前端限制的数量，单位万，比如限制20万，传20就行 */
  limitTotal?: number;
  /** 导出时是否校验表格数据为空，默认校验 */
  isJudgeDatalength?: boolean
}
