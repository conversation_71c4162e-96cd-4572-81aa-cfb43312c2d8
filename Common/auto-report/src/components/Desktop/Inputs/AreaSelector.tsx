import React from 'react';
import ReportAreaSelector, { SimplifiedArea } from '@mtfe/next-biz/src/components/Area/ReportAreaSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  AreaSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';
import { serialize, deserialize } from './utils';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: AreaSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as AreaSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  serialize(area: SimplifiedArea) {
    return serialize([area.id, area.name]);
  }

  deserialize(str: string) {
    const [id, name] = deserialize(str);
    return { id, name };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportAreaSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(area?: SimplifiedArea | SimplifiedArea[]) {
    return toArray(area)?.map(o => o.name).join('，') || '全部';
  }

  getValue(area: SimplifiedArea | SimplifiedArea[] | undefined) {
    return toArray(area)?.map(o => o.id);
  }
}
