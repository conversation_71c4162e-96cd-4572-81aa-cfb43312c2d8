import { Emitter } from 'mitt';

export { Store } from './Store';

/** pending 加载中， done 加载完成 */
export type ComponentStatus = 'pending' | 'done';

export interface StateObjectValueType<ValueType> {
  id: string;
  name?: string;
  path: string[];
  dataKey?: string;
  value?: ValueType;
  valueEntity?: any;
  defaultValue?: ValueType;
  status?: ComponentStatus;
  emitter$: Emitter;
  temporaryValue?: ValueType; // 临时 value
  temporaryValueEntity?: any; // 临时 valueEntity
  relations?: string[];
  ohterProps?: any;
}

export type RootStateType = {
  [id: string]: any;
};

export type GlobalStateType = {
  [id: string]: any;
};

export interface LinkageType {
  type: string;
  condition?: string,
  assertion?: string,
  [id: string]: any;
}

export interface ConfigProps<State = {}> {
  children?: Config<State>;
  [id: string]: any;
}

export type Config<State = {}> = {
  /** 对应的组件 name */
  type: string;
  /** 组件唯一标识，主要用于快速定位操作性组件, 全局唯一，不能重复 */
  dataKey?: string;
  /**
   * 联动协议
   */
  linkages?: LinkageType | LinkageType[];
  /**
   *  事件处理
   *  - change->{EventType} 内容变更触发 EventType,
   *    EventType 格式：{事件名称} 或 '{事件名称}{#dataKey}' 或 '{事件名称}{@parentName}'
   */
  relations?: string[];
  props?: ConfigProps<State>;
};