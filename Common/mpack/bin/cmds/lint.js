/* eslint-disable no-console */
const { execFile, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const { logProcess, resolveNodeModulesBin, getGitRootDir } = require('../util');

module.exports = async function () {
  // TypeScript
  try {
    const tscPath = resolveNodeModulesBin('typescript', 'tsc');
    const tsconfigPath = path.resolve(process.cwd(), 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      await logProcess(
        execFile(tscPath, [
          '--noEmit',
          '--skipLibCheck',
          '--project',
          tsconfigPath,
        ]),
      );
    }
  } catch (e) {
    e && console.error(e);
    process.exit(1);
  }
  // ESLint
  try {
    const eslintPath = resolveNodeModulesBin('eslint');
    await logProcess(
      execFile(eslintPath, [
        '--config',
        path.resolve(__dirname, '../../.eslintrc.js'),
        ...process.argv.slice(3),
      ]),
    );
  } catch (e) {
    console.error('eslint检查不通过', e || '');
    process.exit(1);
  }
};
