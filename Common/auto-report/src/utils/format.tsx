/// <reference path="../types/global.d.ts" />
// ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️这里的函数被被App端使用，请不用使用巨无霸 moment，严重影响性能⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️️⚠️️⚠️️⚠️
// import moment from 'moment';
import format from 'date-fns/format';
import zh_cn from 'date-fns/locale/zh_cn';
import { divide, times } from 'number-precision'; // 保证精度不丢失
import { WindowHistoryReportQueryPersistence } from '@mtfe/sjst-report';
import { NV } from '@mtfe/sjst-antdx-next/es/common/utils';
import React from 'react';
import { isRange } from '@mtfe/next-biz/es/utils/money';
import { StorageHistoryReportQueryPersistence } from '@mtfe/next-biz/es/lib/StorageHistoryReportQueryPersistence';
import moment from 'moment';

/**
 * 计算表格列宽百分比
 *
 * @param width 当前宽度, scrollX: 可滚动最大宽度大小
 */
export const computeOtherColWidth = function (width: number, scrollX: number) {
  const percent = 100 * width / scrollX;
  return `${percent}%`;
};

export const formatNull = function (value: string | number) {
  if (value === 0) {
    return 0;
  }
  if (!value) {
    return '--';
  }
  return value;
};

export const formatArrayNull = function (value: string[]) {
  if (!value) {
    return '--';
  }
  return value.join('；') || '--';
};

export const formatNumerical = (v: string | number) => v || 0;

// 千分位
export const formatThousand = (money: string | number): string => {
  // 后端会返回null
  if (!money) {
    return '--';
  }
  return money.toString().replace(/(?=(\B\d{3})+\.\d{2}$)/g, ',');
};
export const formatNumber = (money: number) => (money ? money.toLocaleString() : 0);

export const formatMoney = (money?: string | number | undefined): string => {
  if (!money) {
    return '0.00';
  }
  money = divide(Number(money), 100);
  if (isNaN(money)) {
    return '0.00';
  }
  money = money.toFixed(2);
  return money.replace(/(?=(\B\d{3})+\.\d{2}$)/g, ',');
};

export const formatMoneyByYuan = (money?: string | number | undefined): string => {
  if (!money) {
    return '0.00';
  }
  money = Number(money);
  if (isNaN(money)) {
    return '0.00';
  }
  money = money.toFixed(2);
  return money.replace(/(?=(\B\d{3})+\.\d{2}$)/g, ',');
};

export const formatMoneyUndefined = (money?: string | number | undefined): string => {
  if (money === undefined) {
    return '--';
  }
  return formatMoney(money);
};

export const formatMoneyNull = (money?: string | number | undefined | null): string => {
  if (money === undefined || money === null) {
    return '--';
  }
  return formatMoney(money);
};

export const formatCompareMoneyNull = (money?: string | number | undefined | null): string => {
  if (money === undefined || money === null) {
    return '--';
  }
  const newV = formatMoney(money);
  return money > 0 ? `+${newV}` : newV;
};

export const formatReverseMoney = (money: number) => { // 金额展示的时候取相反的值
  money = -money;
  if (money === 0) {
    return '-0.00';
  }
  return formatMoney(money);
};

export const formatPercentByPercentile = function (value: number | string | undefined): string {
  if (!value) {
    return '0%';
  }
  value = times(Number(value), 100);
  if (isNaN(value)) {
    return '0%';
  }
  return `${value}%`;
};

export const formatPercent = function (value: number | string | undefined): string {
  if (!value) {
    return '0.00%';
  }
  value = Number(value);
  if (isNaN(value)) {
    return '0.00%';
  }
  value = value.toFixed(2);
  return `${value}%`;
};

export const formatPercentNull = (value?: string | number | undefined | null): string => {
  if (value === undefined || value === null) {
    return '--';
  }
  return formatPercent(value);
};

export const formatComparePercentNull = (value?: string | number | undefined | null): string => {
  if (value === undefined || value === null) {
    return '--';
  }
  const newV = formatPercent(value);
  return value > 0 ? `+${newV}` : newV;
};

export const formatComparePercentNullColor = (value?: string | number | undefined | null): React.ReactNode => {
  if (value === undefined || value === null) {
    return '--';
  }
  const newV = formatPercent(value);
  if (value === '0' || value === 0) {
    return '持平';
  }
  return <span style={{ color: value > 0 ? '#FE8C00' : '#00C8B4' }}>{value > 0 ? `+${newV}` : newV}</span>;
};

export const formatComparePercentNullColorNew = (value?: string | number | undefined | null): React.ReactNode => {
  if (value === undefined || value === null) {
    return '--';
  }
  // const newV = formatPercent(value);
  if (value === '0' || value === 0) {
    return <span className="chiping">持平</span>;
  }
  if (value > 0) {
    return <span className="tisheng">{formatPercent(Number(value))}</span>;
  }
  return <span className="xiajiang">{formatPercent(Math.abs(Number(value)))}</span>;
};

export const formatPercentUndefined = function (value: number | string | undefined): string {
  if (value === undefined) {
    return '--';
  }
  return formatPercent(value);
};

export const formatDate = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY/MM/DD');
};

export const formatDateChinese = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY年MM月DD日');
};

export const formatDateMonth = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY/MM');
};

export const formatDateWeek = function (value: number) {
  if (!value) {
    return '--';
  }
  return moment(value).format('gggg/ww周');
};

export const formatMonthDay = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'MM/DD');
};

// 从其他格式转成 标准格式
export const formatDateByString = function (date: string) {
  if (!date) {
    return '--';
  }
  return format(date, 'YYYY/MM/DD');
};

export const formatDateTime = function (value?: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY/MM/DD HH:mm:ss');
};

export const formatDateTimeMinute = function (value?: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY/MM/DD HH:mm');
};

export const formatShortDateTime = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'MM/DD HH');
};

export const formatYear = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY');
};

export const formatHourMin = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'HH:mm');
};

export const formatDateTimeWithF = function (value: number, f: string) {
  if (!value) {
    return '--';
  }
  return format(value, f);
};

export const formatDateTimeByEat = function (value: number, mealSegment: string, base: IV) {
  if (!value) {
    return '--';
  }
  let eatTime = '';
  try {
    const ext = JSON.parse(base.extra);
    const eatTimeData = typeof ext.eatTimeData === 'string' ? JSON.parse(ext.eatTimeData) : ext.eatTimeData;
    if (eatTimeData && Number(eatTimeData.type) === 2) {
      eatTime = `（${eatTimeData.showTime}） `;
    }
  } catch (e) {
    // do nothing
  }
  return `${format(value, 'YYYY/MM/DD dddd', { locale: zh_cn })} ${eatTime}${format(value, 'HH:mm')} ${mealSegment}`;
};

export const formatDateMin = function (value: number) {
  if (!value) {
    return '--';
  }
  return format(value, 'YYYY/MM/DD HH:mm');
};

export function bigString(str: string, max = 8): string {
  if (str.length > max) {
    str = `${str.substr(0, max)}...`;
  }
  return str;
}

const dayMap = {
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
};

type dayMapKey = keyof typeof dayMap;

// 获取周几
export const formatDayByString = function (date: string | number) {
  const d: unknown = format(date, 'E');
  return dayMap[d as dayMapKey];
};

export const transKey2CN = function (key: string, map: IV) {
  if (!map) {
    return '';
  }
  return map[key] || '';
};

export const transKeys2CNs = function (keys: string[], map: IV) {
  if (!map || !keys) {
    return [];
  }
  return keys.map(key => map[key]);
};

// 从字典渲染下载条件
export function formatSingleCondition(title: string, value: string | number | null, map: StringMap | undefined): string {
  let rs: string = '';
  if (map && value) {
    rs = map[value];
  } else {
    rs = '全部';
  }
  return `${title}【${rs}】`;
}

// 从字典渲染下载条件
export function formatMultiCondition(title: string, valueArr: string[], map: StringMap | undefined = undefined): string {
  let rs: string = '';
  if (valueArr.length === 0) {
    rs = '全部';
  } else if (valueArr.length > 1) {
    rs = `已选${valueArr.length}个`;
  } else if (map) {
    rs = map[valueArr[0]];
  } else {
    rs = valueArr[0];
  }
  return `${title}【${rs}】`;
}

export type Poi = {
  poiId: string | number;
  poiName?: string;
  tenantId?: number;
}
// 渲染下载条件-门店
export function formatPoiCondition(title: string, poi?: {name?: string, poiName?: string}[] | {name?: string, poiName?: string}) {
  let rs: string = '';
  if (Array.isArray(poi)) {
    if (poi.length === 0) {
      rs = '全部';
    } else if (poi.length > 1) {
      rs = `已选${poi.length}个`;
    } else {
      rs = poi[0].poiName || poi[0].name || '';
    }
  } else {
    if (!poi) {
      rs = '全部';
    } else {
      rs = poi.poiName || poi.name || '';
    }
  }

  return `${title}【${rs}】`;
}

// 转化称树结构
export function map2tree(map?: {[index: string]: string}, sort?: (a: string, b: string) => number) {
  if (!map) {
    return [];
  }
  let arr = Object.keys(map);
  if (sort) {
    arr = arr.sort(sort);
  }
  return arr.map(k => ({
    title: map[k],
    value: k,
  }));
}

// 给两层数组数据增加 key
export const generateRowKey = function (list: IV[], childKey: string, index = '1') {
  if (!list || !list.length) {
    return [];
  }
  list.forEach((item: IV, i: number) => {
    const key = `${index}-${i}`;
    item.key = key;
    if (childKey && item[childKey] && item[childKey].length) {
      generateRowKey(item[childKey], childKey, key);
    }
  });
  return list;
};
// 从配置里面选取顺序值升序排列
export const sortByValueConfig = function (config: NumberMap) {
  return function (a: string, b: string): number {
    if (config[a] === undefined && config[b] === undefined) {
      return 0;
    }
    if (config[a] === undefined) {
      return 1;
    }
    if (config[b] === undefined) {
      return -1;
    }
    return config[a] - config[b];
  };
};

// 判断 pos 客户端版本号是否满足条件
export const isPosClientVersionOK = function (androidVer: string, winVer: string) {
  let isCheck = false;
  let targetVer = '';
  const appCode = (window as IV).appCode || ''; // mobileReady 里面注入的
  if (appCode === '43') { // android pos
    isCheck = true;
    targetVer = androidVer;
  } else if (appCode === '44') { // winpos
    isCheck = true;
    targetVer = winVer;
  }
  if (!isCheck) {
    return true;
  }
  const ua = navigator.userAgent;
  const match = ua.match(/\bappVersionCode\/(\d+)\b/);
  if (match && match[1] && match[1] >= targetVer) {
    return true;
  }
  return false;
};

/**
 * @deprecated
 */
export const oldQueryPersistence = new WindowHistoryReportQueryPersistence({});
export const queryPersistence = new StorageHistoryReportQueryPersistence({});
export const queryPersistenceWithIgnoreFields = (ignoreFields: string[]) => new WindowHistoryReportQueryPersistence({
  filter: (key => ignoreFields.includes(key)),
});

export function map2nv(map: IV): NV[] {
  return Object.keys(map).map(value => ({
    name: map[value],
    value,
  }));
}

export function formatRank(name: React.ReactNode, _: IV, index: number) {
  return (
    <span><strong
      style={{ color: index > 2 ? 'rgba(0,0,0,0.65)' : '#FE8C00' }}
    >{index + 1} </strong> {name}</span>
  );
}

export function isPositiveIntOfValidRange(val: string, min: number, max: number) {
  return val && !(val.includes('.') || !Number(val) || isRange(val, min, max, 0));
}

// 根据月总额计算周总额和日总额（不是最精确，因为最后会取整）
export function calculateWeekAndDayByMonth(monthMoney: string, min: number, max: number): undefined | {
  dayMoney: string,
  weekMoney: string
} {
  // 如果不是有效范围（大于min，小于max）内
  if (!isPositiveIntOfValidRange(monthMoney, min, max)) {
    return undefined;
  }
  const current = new Date();
  const currentMonthDays = new Date(current.getFullYear(), current.getMonth() + 1, 0).getDate();
  const dayMoney = divide(Number(monthMoney), currentMonthDays);
  const weekMoney = times(dayMoney, 7);
  return {
    dayMoney: dayMoney.toFixed(0),
    weekMoney: weekMoney.toFixed(0),
  };
}

export function getNames(obj?: ({name: string} | string)[] | {name: string} | string | null | undefined) {
  if (!obj) return;
  if (!Array.isArray(obj)) {
    obj = [obj];
  }
  return obj.map(t => (typeof t === 'string' ? t.trim() : t.name));
}

export function getIds(obj?: ({id: string | number} | string)[] | {id: string | number} | string | null | undefined) {
  if (!obj) return;
  if (!Array.isArray(obj)) {
    obj = [obj];
  }
  return obj.map(t => (typeof t === 'string' ? t.trim() : t.id));
}

export function encodeHTML(html: string) {
  html = html.replace(/</g, '*lt~');
  html = html.replace(/>/g, '*gt~');
  return html;
}

export function padStaffNo(target: string | number) {
  let s = String(target);
  if (s.length >= 5) {
    return s;
  }
  s = `00000${s}`;
  return s.slice(s.length - 5);
}

export function transformTableDataSourceByRowkey(dataSource: IV[], prefix = '') {
  return dataSource.map((value: IV, index: number) => {
    if (value.children && value.children.length) {
      value.children = transformTableDataSourceByRowkey(value.children, `${prefix}${index}-`);
    }
    value.tableIndexKey = `${prefix}${index}`;
    return value;
  });
}

//获取异常退款流水的操作状态
export function formatDealStatus(status: number) {
  enum statusList {
    '待处理',
    '退款中',
    '退款成功',
    '退款失败'
  }
  return statusList[status] || '';
}

//获取异常支付是否可退
export function formatCanRefund(status: number) {
  enum refundList {
    '不可退',
    '可退'
  }
  return refundList[status] || '';
}

//按业务类型分类
export function formatBusinessDimension(value: number[]) {
  const valueLabel = value.map((item) => {
    if (item === 1) {
      return '【按业务大类统计】';
    } else if (item === 2) {
      return '【按业务小类统计】';
    } else {
      return '';
    }
  });
  return valueLabel.toString();
}

//  脱敏-姓名
export function desensitizationName (value: string) {
  console.log(value,'value')
  if(value){
    return value.substring(0, 1)+'**';
  }
  return '';
}

//  脱敏-电话
export function desensitizationMobile(value: string){
  if(value){
      let pat=/(\d{4})\d*(\d{3})/;
      return value.replace(pat,'$1****$2');
  } else {
      return "";
  }
}

//脱敏-地址
export function desensitizationAddress(value: string){
  let re = /[A-Za-z0-9]/g
  if(value){
    return value.replace(re, '*')
  }else{
    return ''
  }
}

