import React from 'react';
import { useUpdateQuery, useQuery } from '@mtfe/next-router-v2';
import { throttleFn } from '@mtfe/next-biz/src/utils/throttle';
import { FilterManager, SerializedQuery, RouterManager } from '../types/context';

export default function useRouter(filterManager: FilterManager, parentRouterManager?: RouterManager) {
  const { serializedQuery, updateQueryBySerialized } = filterManager;

  const updateQuery = useUpdateQuery({ replace: true });
  const query = useQuery();
  const total = React.useRef<SerializedQuery>();
  const inited = React.useRef(false);

  const updateUrlQuery = React.useMemo(() => {
    const _updateUrlQuery = (v: SerializedQuery) => {
      updateQuery(v);
    };
    return throttleFn(_updateUrlQuery, 1000);
  }, []);

  const updateSerializedQuery = React.useCallback((v: SerializedQuery) => {
    total.current = { ...total.current, ...v };
    if (parentRouterManager && parentRouterManager.updateSerializedQuery) {
      parentRouterManager.updateSerializedQuery(total.current);
    } else {
      updateUrlQuery(total.current);
    }
  }, [total, updateUrlQuery]);

  React.useEffect(() => {
    updateSerializedQuery(serializedQuery);
  }, [serializedQuery]);

  React.useEffect(() => {
    if (inited.current) return;
    inited.current = true;
    if (query) {
      updateQueryBySerialized(query);
    }
  }, [updateQueryBySerialized, inited]);

  return React.useMemo(() => ({
    updateSerializedQuery,
  }), [
    updateSerializedQuery,
  ]);
}
