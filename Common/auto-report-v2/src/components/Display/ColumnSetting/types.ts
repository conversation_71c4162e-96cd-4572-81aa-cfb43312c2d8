import { TableColumnProps } from '../Table/types';
import { ComponentProps } from '../../../types';
import { SortItem } from '@mtfe/next-biz/es/components/TableColumnSorter/types';

export interface ColumnSettingProps extends ComponentProps<Array<TableColumnProps<any>>> {
  storeKey: string;
  bid?: string;
  onConfirm?: (items: SortItem[], refObject: any, originItems: SortItem[]) => void;
  forceUseCustomColumns?: boolean;
}
