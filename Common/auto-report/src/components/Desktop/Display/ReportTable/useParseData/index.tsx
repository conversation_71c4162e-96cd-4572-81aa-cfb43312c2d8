/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { UserContext } from '@mtfe/next-biz/src/contexts/user';
import { ua } from '@mtfe/next-biz/src/utils/ua';
import {
  TableColumnConfig,
  DataNode,
  ReportTableProps,
  ReportContext,
} from '../../../../../types';
import useColumns from './useColumns';
import useFormula from '../../../../../managers/useFormula';
import useRows from './useRows';
import useTotalRow from './useTotalRow';

/** 获取数据中的维度信息 */
export function useDims(roots: Array<DataNode | null | undefined>) {
  const dims = React.useMemo(() => {
    const _dims = new Map<string, {[k: string]: boolean}>();
    function iter(node?: DataNode | null) {
      if (!node) return;
      const { groupDims } = node;
      if (groupDims) {
        Object.entries(groupDims).forEach(([group, value]) => {
          const set = _dims.get(group) || {};
          if (value !== null) {
            set[String(value)] = true;
          }
          _dims.set(group, set);
        });
      }
      node.items?.forEach(iter);
    }
    roots.forEach(root => iter(root));
    return _dims;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...roots]);

  return {
    dims,
  };
}
/**
 * useParseDataV2 的特点是增加了对于交叉表、动态列的支持
 * 后端的数据结构由扁平变成 树形结构，结构说明文档：
 * https://km.sankuai.com/page/*********
 */
export default function useParseDataV2(props: ReportTableProps, reportName?: string) {
  const { showIndex, showColumnSorter } = props;
  const report = React.useContext(ReportContext);
  const userService = React.useContext(UserContext);
  const { updateTools } = report?.toolsManager || {};
  const { rootNode } = report?.requestManager || {};
  // const groupBy = report?.filterManager.query.groupBy || [];
  const { priceTaxEnabled, updateHavePriceTaxField } = report?.priceTaxManager || {};
  const [columns, updateColumns] = React.useState<TableColumnConfig[]>([]);
  const runFormula = useFormula();
  /** 是否为交叉表 */
  const isCrossTable = React.useMemo(() => columns.some(column => !!column.children?.length && column.field), [columns]);
  const isTreeTable = React.useMemo(() => columns.some(column => !!column.subField), [columns]);

  /** 获取行数据 */
  const { rows } = useRows({
    isTreeTable,
    isCrossTable,
    rootNode,
    columns,
    showIndex,
  });

  /** 获取交叉表的合计行 */
  const [rowsWithTotal, totalRoot] = useTotalRow(
    rows,
    props.columns,
    isCrossTable,
    report?.requestManager,
    props.showIndex,
  );

  /** 根据交叉表的合计行和数据，获取分组维度和 */
  const { dims } = useDims([totalRoot, report?.requestManager.rootNode]);
  /** 获取列配置 */
  const {
    tableColumns,
    // width,
    showColumnSortor,
    sorterModal,
    exportColumns,
  } = useColumns({
    reportName,
    dims,
    isCrossTable,
    columns,
    showIndex,
  });

  /** 在工具栏添加「字段设置」选项 */
  React.useEffect(() => {
    if (!updateTools) return;
    // winpos 暂时取消字段设置功能
    if (showColumnSorter === false || ua.os === 'winpos') {
      updateTools({
        columnSorter: null,
      });
    } else {
      updateTools({
        columnSorter: {
          icon: 'setting',
          label: '字段设置',
          onClick: showColumnSortor,
        },
      });
    }
  }, [showColumnSortor, updateTools, showColumnSorter]);

  /** 根据用户选择的统计维度同步列配置 */
  React.useEffect(() => {
    const iter = (_columns: TableColumnConfig[]): TableColumnConfig[] => _columns.map((column) => {
      let show = true;
      if (column.isDim) {
        // show = !column.isDim || groupBy.includes(column.field) || groupBy.includes(column.alias) || groupBy.includes(column.alternate);
        show = dims.has(column.field || column.alias || column.alternate || '');
      }

      if (show && column.show) {
        show = runFormula(column.show);
      }

      if (column.loginType === 'chain' && show) {
        show = !!userService?.isHeadOffice();
      } else if (column.loginType === 'poi' && show) {
        show = !userService?.isHeadOffice();
      }

      /** 受价税分离控制的列 */
      if ('showOnPriceTaxEnable' in column && show) {
        updateHavePriceTaxField && updateHavePriceTaxField(true);
        if (column.showOnPriceTaxEnable === priceTaxEnabled) {
          show = true;
        } else {
          show = false;
        }
      }
      if (show) {
        if (column.children) {
          const children = iter(column.children);
          return { ...column, children };
        }
        return column;
      } else {
        return null;
      }
    }).filter(Boolean) as TableColumnConfig[];

    updateColumns(iter(props.columns));
  }, [props.columns, userService, runFormula, updateHavePriceTaxField, priceTaxEnabled, updateColumns, dims]);

  return {
    sorterModal,
    exportColumns,
    tableColumns,
    rows: rowsWithTotal,
    // width,
    isCrossTable,
    isTreeTable,
  };
}
