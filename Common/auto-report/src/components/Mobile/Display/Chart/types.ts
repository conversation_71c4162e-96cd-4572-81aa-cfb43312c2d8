declare module '@antv/f2' {
  export type CanvasAttr = {
    fill?: string | CanvasGradient | CanvasPattern,
    fillStyle?: string | CanvasGradient | CanvasPattern,
    fillOpacity?: number,
    stroke?: string | CanvasGradient | CanvasPattern,
    strokeStyle?: string | CanvasGradient | CanvasPattern,
    opacity?: number,
    textAlign?: 'left' | 'right' | 'center',
    textBaseline?: 'top' | 'bottom' | 'middle',
    fontStyle?: string,
    fontSize?: number,
    fontFamily?: string,
    fontWeight?: 'normal' | 'bold' | 'bolder' | 'lighter',
    lineHeight?: number,
    rotate?: number,
  }

  export type Data = Record<string, string | number>[];
  export type Color = string | string[];

  export type EventName = string;

  export type AdjustType = 'dodge' | 'stack';
  export type Adjust = {
    type: AdjustType,
    marginRatio: number,
  }

  export interface Geometry {
    position(p: string): this,

    color(field: string, color: Color): this;
    color(color: Color): this;

    adjust(type: AdjustType): this;
    adjust(type: Adjust): this;
    adjust(types: Adjust[]): this;
  }

  export type LegendConfig = {
    position?: 'top' | 'right' | 'bottom' | 'left',
    align?: 'left' | 'center' | 'right',
    verticalAlign?: 'top' | 'middle' | 'bottom',
    itemWidth?: number | 'auto',
    showTitle?: boolean,
  }

  export type TooltipConfig = {
    triggerOn?: EventName[],
    triggerOff?: EventName[],
    showCrosshairs?: boolean,
    showItemMarker?: boolean,
    layout?: string,
  }

  export type AxisConfig = {
    labelOffset?: number,
    tickLine?: CanvasAttr | null,
    line?: CanvasAttr | null,
    label?: CanvasAttr | ((text: string, index: number, total: number) => CanvasAttr) | null;
    grid?: CanvasAttr | ((text: string, index: number, total: number) => CanvasAttr) | null;
    position?: 'bottom' | 'left' | 'right',
  }

  export type SourceConfig = {
    [k: string]: {
      range?: number[],
      tickCount?: number,
      min?: number,
      type?: 'timeCat',
    },
  }

  export interface Chart {
    source(data: Data, configs?: SourceConfig): this;

    legend(show: boolean): this,
    legend(name: string, show: boolean): this,
    legend(name: string, configs?: LegendConfig): this,

    tooltip(show: boolean): this;
    tooltip(configs: TooltipConfig): this;

    axis(show: boolean): this;
    axis(field: string, show: boolean): this;
    axis(field: string, configs: AxisConfig): this;

    render(): void;

    line(): Geometry;

    path(): Geometry;

    area(): Geometry;

    point(): Geometry;

    interval(): Geometry;

    polygon(): Geometry;

    schema(): Geometry;
  }

  export type ChartOptions = {
    id: string,
    pixelRatio?: number,
  };

  export interface ChartConstructor {
    new(options: ChartOptions): Chart;
  }

  export const Chart: ChartConstructor;
}
