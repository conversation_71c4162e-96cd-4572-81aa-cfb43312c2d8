{
  "extends": "../common-configs/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "typeRoots": ["./typings/*", "node_modules/@types"],
    "resolveJsonModule": true,
    "target": "es5",
    "rootDirs": ["src", "examples"],
    "module": "esnext",
    "lib": ["es2017", "dom", "es2017.object", "es2015"],
    "types": ["prop-types", "react", "node", "jest"],
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "@mtfe/next-biz/es/*": ["../next-biz/src/*"],
      "@mtfe/next-biz/*": ["../next-biz/*"],
      "@typings/*": ["../../typings/*"],
    }
  },
  "include": ["./src", "./examples"],
  "exclude": ["*.test.js", "*.test.jsx"]
}
