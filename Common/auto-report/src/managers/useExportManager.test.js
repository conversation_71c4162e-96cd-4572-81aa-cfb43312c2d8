const postRequest = jest.fn(() => ({
  url: '',
}));
jest.mock('@mtfe/next-biz/src/lib/Net', () => ({
  post: postRequest,
}));
const { default: useExportManager } = require('./useExportManager');
const { renderHook } = require('@testing-library/react-hooks');

describe('useExportManager', () => {
  let requestManager, filterManager, props;

  beforeEach(() => {
    props = {
      title: '',
    };
    filterManager = {
      getQueryInfo: () => '',
    };
    requestManager = {
      query: {

      },
      rootNode: { items: [{}] },
    }
  });

  it('没有数据时不会触发下载逻辑', async () => {
    requestManager.rootNode.items = [];
    const { result } = renderHook(() => useExportManager(props, requestManager, filterManager));
    await result.current.getExportUrl();
    expect(postRequest.mock.calls.length).toBe(0);
  });

  it('下载时会请求地址 /api/v1/excel/nest/report/download', async () => {
    const { result } = renderHook(() => useExportManager(props, requestManager, filterManager));
    await result.current.getExportUrl();
    expect(postRequest.mock.calls[0][0]).toBe('/api/v1/excel/nest/report/download');
  });

  it('文件名会过滤特殊字符', async () => {
    props.title = '/\\*[]';
    const { result } = renderHook(() => useExportManager(props, requestManager, filterManager));
    await result.current.getExportUrl();
    expect(postRequest.mock.calls[0][1].reportName).not.toContain(props.title);
  });


});
