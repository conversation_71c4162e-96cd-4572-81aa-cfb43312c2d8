import React from 'react';
import ReportDiscountSelector, { SimplifiedDiscount } from '@mtfe/next-biz/src/components/Discount/ReportDiscountSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  DiscountSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: DiscountSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as DiscountSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportDiscountSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(discount?: SimplifiedDiscount | SimplifiedDiscount[]) {
    return toArray(discount)?.map(o => o.name).join('，') || '全部';
  }

  getValue(discount: SimplifiedDiscount | SimplifiedDiscount[] | undefined) {
    return toArray(discount)?.map(o => o.id);
  }
}
