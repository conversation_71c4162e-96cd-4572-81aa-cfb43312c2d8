import React from 'react';
import moment from 'moment';
import { RangePickerX } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { DatePickerProps, TimeValue } from './types';
import { ReportQuery } from '../../../types';

const defaultTimeRange = {
  start: moment().startOf('day').valueOf(),
  end: moment().endOf('day').valueOf(),
  option: '今日',
};

const defaultQueryConfig = {
  startKey: 'startDate',
  endKey: 'endDate',
};

function DatePicker(props: DatePickerProps) {
  const {
    baseRefObject,
    updateStatus,
    value,
    onChange,
    ...otherProps
  } = props;

  const ref = React.useRef<RangePickerX | null>(null);

  React.useEffect(() => {
    if (ref.current?.props?.value) {
      updateStatus?.(
        'done',
        v => v || (ref.current?.props?.value as TimeValue)
      );
    }
  }, [ref.current?.props?.value]);

  const _onChange = React.useCallback(
    (v) => {
      onChange?.(v);
    },
    [onChange],
  );

  return React.useMemo(
    () => (
      <RangePickerX
        {...otherProps}
        ref={ref}
        value={(value || null) as TimeValue} // FormItemComponent 会忽略掉 undefined 的新值，所以传入 null
        onChange={_onChange}
      />
    ),
    [
      otherProps,
      value,
      _onChange,
    ],
  );
}

DatePicker.prototype.getQuery = async (query: ReportQuery, props?: DatePickerProps) => {
  const timeRangeKey = props?.dataKey || 'timeRange';
  const {
    startKey, endKey,
  } = {
    ...defaultQueryConfig,
    ...props?.queryConfig,
  };
  const timeRange = query[timeRangeKey] || defaultTimeRange;
  query[startKey] = timeRange.start;
  query[endKey] = timeRange.end;
  return {
    query,
  };
};

DatePicker.prototype.getDisplayValue = (
  value: TimeValue,
  props?: DatePickerProps,
) => {
  if (!value) return;
  const startDate = value.start;
  const endDate = value.end;
  const mode = props?.modes as String;
  let display = '';
  switch (mode) {
    case 'month':
      display = `${moment(startDate || 0).format('YYYY/MM')} 至 ${moment(endDate).format('YYYY/MM')}`;
      break;
    case 'time':
      display = `${moment(startDate || 0).format('YYYY/MM/DD HH:mm:ss')} 至 ${moment(endDate).format('YYYY/MM/DD HH:mm:ss')}`;
      break;
    case 'week':
    case 'date':
    default:
      display = `${moment(startDate || 0).format('YYYY/MM/DD')} 至 ${moment(endDate).format('YYYY/MM/DD')}`;
      break;
  }
  return display;
};

export default withAdapter(DatePicker, { name: 'DatePicker' });
