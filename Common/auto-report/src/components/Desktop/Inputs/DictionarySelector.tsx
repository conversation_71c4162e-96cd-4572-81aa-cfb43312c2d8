import React from 'react';
import ReportDictionarySelector, { Dictionary, DictionOptions } from '@mtfe/next-biz/src/components/Dictionary/ReportDictionarySelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  DictionarySelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: DictionarySelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as DictionarySelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMode() {
    switch (this.config.mode) {
      case 'allSource':
        return DictionOptions.AllSource;
      case 'orderBizType':
        return DictionOptions.OrderBizType;
      case 'orderCategory':
        return DictionOptions.OrderCategory;
      case 'orderDiningMode':
        return DictionOptions.OrderDiningMode;
      case 'orderSource':
        return DictionOptions.OrderSource;
      case 'orderInstoreBizType':
        return DictionOptions.OrderInstoreBizType;
      default:
        return null;
    }
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    const mode = this.getMode();
    return (
      <ReportDictionarySelector
        size={props.size}
        useMultiplePopSearch
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        options={mode}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(dicts?: Dictionary | Dictionary[]) {
    if (!dicts) {
      return '全部';
    }
    if (Array.isArray(dicts) && !dicts.length) {
      return '全部';
    }
    return toArray(dicts)?.map(o => o.name).join('，');
  }

  getValue(dicts: Dictionary | Dictionary[] | undefined) {
    return toArray(dicts)?.map(o => o.id);
  }
}
