/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Filter,
  InputType,
} from '../../../types/filter';
import {
  ReportFilterProps,
  ReportContext,
} from '../../../types';
import { registorComponent } from '../registor';
import ReportComponent from '../../ReportComponent';
import {
  ClearingTimeQueryConfig,
  QueryContainerConfig,
  QueryItemConfig,
} from './types';

type OnChange = (v: any) => void;

type ComponentCache = {
  [k: string]: React.FunctionComponent<{
    value: any,
    onChange: OnChange,
    deps: any,
  }>,
}

export default function ReportFilter(props: ReportFilterProps) {
  const report = React.useContext(ReportContext);
  const {
    size, hidden, realTimeUpdate, otherParams,
  } = props;
  const { filterManager, requestManager, analysisManager } = report || {};
  const {
    errors,
    originQuery,
    filters,
    inputs,
    updateQuery,
    updateOriginQuery,
    updateDisplayQuery,
    updateFilters,
    defaultQuery,
  } = filterManager || {};
  const {
    loading,
    clearTime,
    noDailySettlement,
  } = requestManager || {};
  const { moduleClick } = analysisManager || {};
  const componentCache: ComponentCache = React.useMemo(() => ({}), []);

  const { filters: _filters } = props;
  React.useEffect(() => {
    if (_filters && updateFilters) {
      updateFilters(_filters);
    }
  }, [_filters, updateFilters]);

  /** 提交查询条件 */
  const onSubmit = React.useCallback((query: typeof originQuery) => {
    if (errors && Object.keys(errors).length) return;
    moduleClick && moduleClick(props.submitBid);
    if (!updateQuery) return;
    if (query) {
      updateQuery({ ...originQuery, ...query, _: Math.random() });
      updateDisplayQuery && updateDisplayQuery({pageNo: 1});
    } else {
      updateQuery({ ...originQuery, _: Math.random() });
    }
  }, [originQuery, updateQuery, moduleClick, props.submitBid, errors, updateDisplayQuery]);

  const onChangeMap = React.useMemo(() => {
    const map: {[k: string]: OnChange} = {};
    if (!updateOriginQuery) return map;
    filters?.forEach((f) => {
      const onChange: OnChange = (v: any) => {
        updateOriginQuery((query: any) => {
          query = { ...query, [f.field]: v };
          if (realTimeUpdate) {
            setTimeout(() => {
              onSubmit(query);
            }, 0);
          }
          return query;
        });
      };
      map[f.field] = onChange;
    });
    return map;
  }, [updateOriginQuery, filters, realTimeUpdate, onSubmit]);

  /** 获取组件的联动条件 */
  const getInputDeps = React.useCallback((input: InputType) => {
    if (input.getDeps && inputs) {
      const result: { [k: string]: any } = {};
      const depsConfig = input.getDeps();
      Object.entries(depsConfig).forEach(([targetType, field]) => {
        const targetFilter = filters?.find(f => f.type === targetType && f.field === field);
        if (!targetFilter) return;
        const targetInput = inputs[targetFilter.field];
        if (!targetInput) return;
        const targetValue = originQuery && originQuery[targetFilter?.field];
        result[field] = targetInput.getValue ? targetInput.getValue(targetValue) : targetValue;
      });
      return result;
    } else {
      return undefined;
    }
  }, [filters, originQuery, inputs]);

  /**
   * 获取过滤器组件
   */
  const getFilterComponent = React.useCallback((filter: Filter): ([React.ReactElement, InputType, string | undefined] | null) => {
    if (!inputs) return null;
    const input = inputs[filter.field];
    if (!input) {
      return null;
    }
    if (!input.render) return null;
    const dependences = getInputDeps(input);
    let Component = componentCache[filter.field];
    Component = Component || ((_props: any) => {
      if (input.render) {
        const { deps, ...otherProps } = _props;
        return input.render({ otherProps, size, moduleClick }, deps);
      } else {
        return null;
      }
    });
    componentCache[filter.field] = Component;
    const value = originQuery[filter.field];
    const onChange = onChangeMap[filter.field];
    const result = <Component deps={dependences} value={value} onChange={onChange} />;
    const showLabel = input.showLabel ? input.showLabel() : true;
    let label: string | undefined;
    if (showLabel) {
      if (input.getLabel) {
        label = input.getLabel(value, dependences);
      } else {
        label = filter.label;
      }
    }
    // 设置缓存
    return [result, input, label];
  }, [inputs, getInputDeps, componentCache, onChangeMap, originQuery, size, moduleClick]);

  let count = 0;
  const children = (filters || []).map((f: Filter) => {
    const component = getFilterComponent(f);
    if (!component) return null;
    const [content, input, label] = component;
    if (input.isVisiable && !input.isVisiable()) {
      return null;
    }
    const error = errors && errors[f.field];

    const config: QueryItemConfig = {
      type: 'QueryItem',
      props: {
        view: f.field,
        moduleClick,
        bid: f.bid,
        size: props.size,
        index: count++,
        children: content,
        help: f.help,
        label,
        error,
      },
    };
    return <ReportComponent key={f.field}>{config}</ReportComponent>;
  }).filter(Boolean) as Array<React.ReactElement>;

  /** 重置查询条件 */
  const onReset = React.useCallback(() => {
    moduleClick && moduleClick(props.resetBid);
    updateOriginQuery && updateOriginQuery({});
    updateDisplayQuery && updateDisplayQuery({});
    updateQuery && updateQuery({
      ...defaultQuery,
      _: Math.random(),
    });
  }, [updateQuery, updateOriginQuery, moduleClick, props.resetBid, defaultQuery]);

  if (!report) return null;

  const clearingTimeQueryDisplay: ClearingTimeQueryConfig | null = report.isSubReport ? null : {
    type: 'ClearingTimeQueryDisplay',
    props: {
      clearTime,
      noDailySettlement,
      originQuery,
    },
  };

  if (!clearingTimeQueryDisplay && !children.length) return null;

  const containerConfig: QueryContainerConfig = {
    type: 'QueryContainer',
    props: {
      hidden,
      realTimeUpdate: props.realTimeUpdate,
      size,
      loading: loading || false,
      onReset,
      onSubmit,
      errors,
      count,
      tip: clearingTimeQueryDisplay && <ReportComponent>{clearingTimeQueryDisplay}</ReportComponent>,
      moduleClick,
      expandBid: props.expandBid,
      children,
      extraTips: props.extraTips,
      maxQueryCount: props.maxQueryCount,
      otherParams,
    },
  };

  return (
    <ReportComponent>{containerConfig}</ReportComponent>
  );
}

registorComponent('ReportFilter', ReportFilter);
