/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { RefObject, useContext, useEffect, useMemo, useRef, useState } from 'react';
import moment from 'moment';
import {
  RangePickerX,
  Select,
  Tags,
  IRangePickerXProps,
} from '@mtfe/sjst-antdx-next';
import PoiService, {
  ClearingPeriodType,
  cn2PeriodType,
} from '@mtfe/next-biz/src/services/poi';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import {
  formatDate,
  formatDateMonth,
  formatDateTime,
} from '../../../utils/format';
import {
  Filter,
  DateFilter,
  DateFilterOption,
  DateFilterMode,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';
import { ReportContext } from '../../../types';
import { getDefaultDateType, getTimeRangeByPermission, TimeRangeType, DefaultDateType, isInLimitTimeListAb } from '../../../services/common';
import { valueIsTwoYearsAgo } from '../../../utils/common';
import './DatePicker.less';
import { serialize, deserialize } from './utils';
import { isFunction, min } from 'lodash';
import { dateMode2QuickOptions } from '@mtfe/sjst-antdx-next/es/quick-date-options/index';

type Value = {
  /** 选择的起始时间 */
  start: number;
  /** 选择的结束时间 */
  end: number;
  /** 今日、昨日、本月、今年 等快捷时间选择中文名 */
  option?: any;
  /** 今日、昨日、本月、今年 等快捷时间选择类型 */
  periodType: ClearingPeriodType;
};

type OnChangeAndValue = {
  value: any;
  onChange: (v: any, mode?: string) => void;
};

const TOMORROW: Value = {
  start: moment().add(1, 'day').startOf('day').valueOf(),
  end: moment().add(1, 'day').endOf('day').valueOf(),
  periodType: ClearingPeriodType.Other,
}

function getModes(type?: string) {
  switch (type) {
    case 'time':
    case 'date':
      return 'day';
    case 'week':
      return 'week';
    case 'month':
      return 'month';
    default:
      return undefined;
  }
}

export const customOptions = ['今日', '昨日', '本周', '上周', '本月', '上月', '近四周', '今年', '去年', '近三月', '本季度', '上季度', '近半年', '未来7天', '未来30天'];

export const customQuikOptions = {
  getValueByOption: (option: string) => {
    let start = moment().startOf('day');
    let end = moment().endOf('day');
    switch (option) {
      case '今日':
        break;
      case '昨日':
        start = start.subtract(1, 'day');
        end = end.subtract(1, 'day');
        break;
      case '本月':
        start = start.startOf('month');
        break;
      case '上月':
        start = start.subtract(1, 'month').startOf('month');
        end = end.subtract(1, 'month').endOf('month');
        break;
      case '近三月':
        start = start.startOf('month').subtract(2, 'month');
        break;
      case '近半年':
        start = start.startOf('month').subtract(5, 'month');
        break;
      case '本周':
        start = start.startOf('isoWeek');
        break;
      case '上周':
        start = start.subtract(1, 'week').startOf('isoWeek');
        end = end.subtract(1, 'week').endOf('isoWeek');
        break;
      case '近四周':
        start = start.subtract(3, 'week').startOf('isoWeek');
        break;
      case '今年':
        start = start.startOf('year');
        break;
      case '去年':
        start = start.subtract(1, 'year').startOf('year');
        end = end.subtract(1, 'year').endOf('year');
        break;
      case '本季度':
        start = start.startOf('quarter');
        break;
      case '上季度':
        start = start.subtract(1, 'quarter').startOf('quarter');
        end = end.subtract(1, 'quarter').endOf('quarter');
        break;
      case '未来7天':
        end = end.add(7, 'days');
        break;
      case '未来30天':
        end = end.add(30, 'days');
        break;
      default:
        break;
    }

    return {
      start: start.valueOf(),
      end: end.valueOf(),
      option,
    };
  },
};

function PickerWithOptions(
  props: IRangePickerXProps & {
    options: DateFilterOption[];
    queryKey: string;
    mode: DateFilterMode;
    moduleClick?: (bid?: string) => void;
    optionsIsTag?: boolean;
    refCore?: RefObject<RangePickerX>;
  } & OnChangeAndValue,
) {
  const {
    value,
    onChange,
    options,
    queryKey,
    mode,
    moduleClick,
    optionsIsTag,
  } = props;

  const onSelectChange = React.useCallback(
    (v: string) => {
      onChange && onChange({ ...value, [queryKey]: v });
      const option = props.options.find(o => o.value === v);
      moduleClick && moduleClick(option?.bid);
    },
    [value, onChange, queryKey, props.options, moduleClick],
  );

  const activeOption = React.useMemo(() => {
    let v: any;
    if (value) {
      if (value[queryKey]) v = value[queryKey];
    }
    if (typeof v === 'undefined' && options.length) {
      v = options[0].value;
    }

    return (options || []).find(o => o.value === v);
  }, [value, options, queryKey]);
  const modes = getModes(activeOption?.mode ? activeOption.mode : mode);
  return (
    <div className="auto-form-date-picker">
      {optionsIsTag ? (
        <Tags
          options={options.map(i => ({ name: i.label, value: i.value }))}
          max={1}
          min={1}
          value={activeOption?.value}
          className="auto-form-date-picker-query-tag"
          onChange={onSelectChange}
        />
      ) : (
        <Select
          className="auto-form-date-picker-query-type"
          onChange={onSelectChange}
          value={activeOption?.value}
          dropdownMatchSelectWidth={false}
        >
          {options.map(o => (
            <Select.Option value={o.value} key={o.value}>
              {o.label}
            </Select.Option>
          ))}
        </Select>
      )}
      <RangePickerX
        customOptionConfigs={customQuikOptions}
        {...props}
        ref={props.refCore}
        value={value}
        onChange={(e) => { onChange(e, activeOption?.mode || mode); }}
        maxDistance={366}
        // customMaxDay={366}
        modes={modes}
      />
    </div>
  );
}

export default class implements InputType {
  poiService: PoiService;

  config: DateFilter;

  options: DateFilterOption[] | null = null;

  queryKey: string = '';

  /** 初始值 */
  initValue: Value | null = null;

  inited = false;

  constructor(filter: Filter, public context: Context) {
    this.poiService = new PoiService();
    this.config = filter as DateFilter;
    const { config } = this;
    if (config.queryType) {
      this.options = config.queryType;
      this.queryKey = 'dataFilterType';
    }
  }

  serialize(value: Value) {
    return serialize([value.start, value.end]);
  }

  deserialize(str: string) {
    const [start, end] = deserialize(str).map(Number);
    return { start, end };
  }

  async getClearingTime(
    type: ClearingPeriodType,
    defaultValue?: {
      start: number;
      end: number;
    },
  ) {
    const { userService } = this.context;
    if (!userService || userService.isHeadOffice()) return defaultValue;
    const time = await this.poiService.getClearingTimeByType(
      userService.org.poiId || 0,
      type,
    );
    return {
      start: time.startZero,
      end: time.endZero,
    };
  }

  getPeriodTypeFromMode(mode: string): ClearingPeriodType {
    switch (mode) {
      case 'year':
        return ClearingPeriodType.ThisYear;
      case 'month':
        return ClearingPeriodType.ThisMonth;
      case '30days':
        return ClearingPeriodType.ThirtyDays;
      case 'week':
        return ClearingPeriodType.ThisWeek;
      case 'yesterday':
        return ClearingPeriodType.Yesterday;
      case 'date':
      default:
        return ClearingPeriodType.Today;
    }
  }

  getDescFromMode(mode: string): string {
    switch (mode) {
      case 'year':
        return '今年';
      case 'month':
        return '本月';
      case '30days':
        return '近30天';
      case 'week':
        return '本周';
      case 'yesterday':
        return '昨日';
      case 'date':
      default:
        return '今日';
    }
  }

  async getDefaultValue() {
    if (this.config.defaultValue) return this.config.defaultValue;
    const type = this.getPeriodTypeFromMode(this.config.mode);
    const desc = this.getDescFromMode(this.config.mode);
    const time = await this.getClearingTime(type);
    this.inited = true;
    if (time) {
      return {
        ...time,
        periodType: type,
        option: desc,
      };
    }
    if (type === ClearingPeriodType.Today) {
      const defaultType = await getDefaultDateType();
      if (defaultType === DefaultDateType.YESTERDAY) {
        return this.getValueFromMode(ClearingPeriodType.Yesterday, '昨日');
      }
      if (defaultType === DefaultDateType.TOMORROW) {
        return TOMORROW;
      }
    }
    return this.getValueFromMode(type, desc);
  }

  getValueFromMode(periodType: ClearingPeriodType, option: string): Value {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const date = now.getDate();
    let day = now.getDay();
    day = day === 0 ? 7 : day;
    switch (periodType) {
      case ClearingPeriodType.ThisYear:
        return {
          start: moment().startOf('year').valueOf(),
          end: moment().endOf('day').valueOf(),
          periodType,
          option,
        };
      case ClearingPeriodType.ThisMonth:
        return {
          start: moment().startOf('month').valueOf(),
          end: moment().endOf('day').valueOf(),
          periodType,
          option,
        };
      case ClearingPeriodType.ThirtyDays:
        return {
          start: new Date(year, month, date - 30, 23, 59, 59, 999).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
          periodType,
          option,
        };
      case ClearingPeriodType.ThisWeek:
        return {
          start: moment().startOf('isoWeek').valueOf(),
          end: moment().endOf('day').valueOf(),
          periodType,
          option,
        };
      case ClearingPeriodType.Yesterday:
        return {
          start: moment().subtract(1, 'day').startOf('day').valueOf(),
          end: moment().subtract(1, 'day').endOf('day').valueOf(),
          periodType,
          option,
        };
      case ClearingPeriodType.Today:
      default:
        return {
          start: moment().startOf('day').valueOf(),
          end: moment().endOf('day').valueOf(),
          periodType,
          option,
        };
    }
  }

  showLabel = () => !this.config.queryType;

  getLabel = (value: Value, deps: { groupBy?: string[] }) => {
    if (this.config.getLabel) {
      return this.config.getLabel(value, deps);
    } else {
      return this.config.label;
    }
  };

  render(props: FilterProps, deps: { groupBy?: string[] }) {
    const [timeRange, setTimeRange] = useState<TimeRangeType>();
    const [inLimitTimeList, setInLimitTimeList] = useState(false);
    const report = useContext(ReportContext);
    const { otherProps } = props;
    const { config } = this;
    const permissionCode = report?.props.permissionCode;
    let mode = config.mode;
    if (deps.groupBy?.includes('dateKey')) {
      mode = 'date';
    } else if (deps.groupBy?.includes('monthKey')) {
      mode = 'month';
    }

    const inited = React.useRef(false);

    React.useEffect(() => {
      if (inited.current === false) {
        inited.current = true;
        return;
      }
      (async () => {
        const type = this.getPeriodTypeFromMode(mode);
        const desc = this.getDescFromMode(mode);
        const time = await this.getClearingTime(type);
        if (time) {
          otherProps.onChange({
            ...time,
            option: desc,
            periodType: type,
          });
        } else {
          const value = this.getValueFromMode(type, desc);
          otherProps.onChange(value);
        }
      })();
    }, [mode, inited]);

    const _props: IRangePickerXProps & OnChangeAndValue = {
      ...otherProps,
      quickOptions: config.quickOptions || undefined,
      inlineQuickOptions: true,
      maxDistance: config.maxDistance || 366,
      allowClear: config.allowClear,
    };

    useEffect(() => {
      (async () => {
        if (permissionCode) {
          const range = await getTimeRangeByPermission(permissionCode)
          setTimeRange(range);
        }
        const _inLimitTimeList = await isInLimitTimeListAb();
        setInLimitTimeList(_inLimitTimeList);
      })();
    }, [permissionCode]);
    const { customMaxDay, quickOptions, inlineQuickOptions, canSelectFuture } = _props;
    const _customMaxDay = useMemo(() => {
      let diff = timeRange?.beginTime ? moment().diff(moment(timeRange.beginTime), 'day') + 1 : undefined;
      let customMaxDayResult = customMaxDay;
      if (diff && customMaxDay) {
        customMaxDayResult = min([diff, customMaxDay]);
      } else {
        customMaxDayResult = customMaxDay || diff;
      }
      return customMaxDayResult;
    }, [customMaxDay, timeRange])

    const _quickOptions = useMemo(() => {
      const modes = getModes(mode) || 'day';
      let options = dateMode2QuickOptions(modes);
      if (quickOptions) {
        if (isFunction(quickOptions)) {
          options = quickOptions(modes);
        } else {
          options = quickOptions;
        }
      }
      return getQuickOptionsByBeginTime({options, beginTime: timeRange?.beginTime});
    }, [quickOptions, mode, timeRange])

    const pickerProps = useMemo(() => {
      let _minDate: number | undefined;
      if (_customMaxDay) {
        const customMaxDate = moment().subtract(_customMaxDay - 1, 'day').valueOf();
        _minDate = customMaxDate;
      }
      const modes = getModes(mode) || 'day';
      const unit = this.options ? 'day' : (modes === 'month' ? 'month' : 'day');
      return {
        start: !!_minDate || !canSelectFuture
        ? {
          disabledDate: (_: IV, v: moment.Moment | null) => {
            if (_minDate && v?.isBefore(moment(_minDate), 'day')) {
              return true;
            }
            if (!canSelectFuture && v?.isAfter(moment().add(1, 'day'), unit)) {
              return true;
            }
            return false;
          },
        } : undefined,
        end: !!_minDate || !canSelectFuture
        ? {
          disabledDate: (value2: IV, v:  moment.Moment | null) => {
            if (_minDate && v?.isBefore(moment(_minDate), 'day')) {
              return true;
            }
            if (!canSelectFuture && v?.isAfter(moment().add(1, 'day'), unit)) {
              return true;
            }
            const start = (value2 || {}).start || 0;
            return Boolean(start != null && (v?.isBefore(start, 'day') || (v?.isAfter(moment(start).add(366, 'day'), 'day'))));
          },
        }
        : undefined
      };
    }, [_customMaxDay, canSelectFuture, mode]);

    const _inlineQuickOptions = useMemo(() => inlineQuickOptions === false ? false : !!_quickOptions.length, [inlineQuickOptions, _quickOptions]);

    const originValue = useRef<Value>();

    const datePickRef = React.useRef<RangePickerX | null>(null);

    const onChange = React.useCallback(
      async (v: Omit<Value, 'periodType'>, dateMode?: string) => {
        const curMode = dateMode || mode;
        const modes = getModes(curMode);
        if (v.start && v.end) {
          if (v.start === moment(v.start).endOf('day').valueOf()) {
            v.start = moment(v.start).startOf('day').valueOf();
          }
          if (v.end === moment(v.end).startOf('day').valueOf()) {
            v.end = moment(v.end).endOf('day').valueOf();
          }
          // 不能超过最大距离: modes === 'month'
          if (modes === 'month') {
            v.end = moment(v.end).endOf('month').valueOf();
            const diffMonth = Math.floor(366 / 30) - 1;
            if (Math.abs(moment(v.end).diff(moment(v.start), 'month')) > diffMonth) {
              v.end = moment(v.start).add(diffMonth, 'month').endOf('month').valueOf();
            }
          }
          // 不能超过最大距离: modes === 'day'
          if (modes === 'day') {
            if (Math.abs(moment(v.end).diff(moment(v.start), 'day')) > 366 - 1) {
              v.end = moment(v.start).add(366 - 1, 'day').endOf('day').valueOf();
            }
          }
        }
        if (this.config.maxDateCut && v?.end && v.start) {
          if (modes === 'month') {
            if (moment(v.start).format('YYYY/MM') === moment(v.end).format('YYYY/MM')) {
              const currentDate = moment().endOf('day').valueOf();
              const endDate = moment(v.start).endOf('month').valueOf();
              v.end = endDate < currentDate ? endDate : currentDate;
            }
          }
        }
        // DatePicker 在初始化时会触发器一次 onChange，会导致 auto-report 初始化时请求两次，所以这里忽略掉
        if (!this.inited) return;
        if (!_props.onChange) return;
        const periodType = cn2PeriodType(v.option);
        if (periodType === ClearingPeriodType.Other) {
          if (valueIsTwoYearsAgo(v.start, inLimitTimeList) && originValue.current) {
            _props.onChange(originValue.current);
            setTimeout(() => {
              datePickRef?.current?.setState({ openStart: false, openEnd: false });
            }, 100);
          } else {
            _props.onChange({ ...v, periodType });
          }
        } else {
          const time = await doAction(this.getClearingTime(periodType, v));
          if (!time) return;
          _props.onChange({ ...v, ...time, periodType });
        }
      },
      [_props.onChange, inLimitTimeList],
    );
    originValue.current = _props.value;
    if (this.options) {
      return (
        <PickerWithOptions
          moduleClick={props.moduleClick}
          size={props.size}
          {..._props}
          refCore={datePickRef}
          customMaxDay={_customMaxDay}
          startPickerProps={pickerProps.start}
          endPickerProps={pickerProps.end}
          quickOptions={_quickOptions}
          inlineQuickOptions={_inlineQuickOptions}
          options={this.options}
          queryKey={this.queryKey}
          mode={mode}
          optionsIsTag={this.config.optionsIsTag}
          onChange={onChange}
        />
      );
    } else {
      const value = _props.value || null;
      if (!this.initValue) this.initValue = value;
      return (
        <RangePickerX
          customOptionConfigs={customQuikOptions}
          {..._props}
          ref={datePickRef}
          modes={getModes(mode)}
          value={value}
          onChange={onChange}
          panelNoChange={this.config.maxDateCut || undefined}
          keepRealtimeOnDayChange={this.config.maxDateCut || undefined}
          startPickerProps={pickerProps.start}
          endPickerProps={pickerProps.end}
          customMaxDay={_customMaxDay}
          quickOptions={_quickOptions}
          inlineQuickOptions={_inlineQuickOptions}
        />
      );
    }
  }

  getDeps() {
    return {
      GroupBySelector: 'groupBy',
      GroupByCheckbox: 'groupBy',
      GroupByRadio: 'groupBy',
    };
  }

  getDisplayValue = (timeRange: Value, deps: { groupBy?: string[] }) => {
    timeRange = timeRange || this.initValue;
    if (!timeRange) return;
    const startDate = timeRange.start;
    const endDate = timeRange.end;
    let display = '';
    let mode = this.config.mode;
    if (!this.config.displayByMode) {
      if (deps.groupBy?.includes('dateKey')) {
        mode = 'date';
      } else if (deps.groupBy?.includes('monthKey')) {
        mode = 'month';
      }
    }
    switch (mode) {
      case 'month':
        display = `${formatDateMonth(startDate || 0)} 至 ${formatDateMonth(
          endDate,
        )}`;
        break;
      case 'week':
      case 'date':
        display = `${formatDate(startDate || 0)} 至 ${formatDate(endDate)}`;
        break;
      case 'time':
      default:
        display = `${formatDateTime(startDate || 0)} 至 ${formatDateTime(
          endDate,
        )}`;
        break;
    }
    
    if (this.options) {
      const v = (timeRange as { [k: string]: number })[this.queryKey];
      const option = this.options.find(q => q.value === v);
      if (option) {
        display = `${option.label}：${display}`;
      }
    }

    return display;
  };

  getQuery(timeRange: Value, query: any) {
    timeRange = timeRange || this.initValue;
    if (!timeRange) return;
    const result: Record<string, string | number> = {
      startDate: timeRange.start,
      endDate: timeRange.end,
      periodType: timeRange.periodType,
    };
    if (this.options) {
      let r = query[this.queryKey] || [];
      const v = (timeRange as { [k: string]: string | number })[this.queryKey];
      if (this.options.length && typeof v === 'undefined') {
        r = this.options[0].value;
      } else {
        r = v;
      }
      result[this.queryKey] = r;
    }

    return result;
  }
}

/*
 * 时间组件支持范围控制
 * 根据可查看范围生成快捷选项
 */
 export function getQuickOptionsByBeginTime(props: {beginTime?: number, options: string[] }): string[] {
  const { beginTime, options } = props;
  if (!beginTime) {
    return options;
  }
  const getValueByOption = customQuikOptions.getValueByOption;
  return options.filter(item => {
    if (customOptions.includes(item)) {
      const date = getValueByOption(item);
      return moment(beginTime).diff(moment(date.start), 'day') <= 0;
    }
    return false;
  });
}