/* eslint-disable react/no-children-prop */
import React from 'react';
import type { Chart } from '@antv/g2';
import {
  Empty,
} from 'antd';
import { ReportContext, ChartData, BasicChartProps } from '../../../../types';
import useG2 from './useG2';
import useParseData from '../../../common/useChartData';

export type Data = ChartData;

type Props = {
  renderer: ((Chart: Chart) => void),
  width?: number | string,
  height?: number | string,
  dims: Array<string | undefined>,
  loadData?: (chart: Chart, data: Data) => void,
  children?: React.ReactNode,
  className?: string,
  style?: React.CSSProperties,
  parseData?: (data: Data | null) => Data,
  customData?: Data,
  empty?: React.ReactNode,
} & BasicChartProps;

export default function useBasicChart(props: Props) {
  const G2 = useG2();
  const {
    dims,
    width = '100%',
    height = 300,
    customData,
    loadData,
    renderer,
    className,
    parseData,
    customLoadData,
    customRenderer,
  } = props;

  const report = React.useContext(ReportContext);
  const { requestManager } = report || {};
  const { rootNode, error } = requestManager || {};
  const { data } = customData ? { data: customData } : useParseData(rootNode || null, dims);
  const [chart, updateChart] = React.useState<Chart | null>(null);
  const id = React.useMemo(() => `auto-report-line-chart-${Math.round(Math.random() * 100000000)}`, []);

  /** 初始化图表 */
  const initChart = React.useCallback(() => {
    if (!G2 || chart) return;
    const _chart = new G2.Chart({
      container: id,
      autoFit: true,
    });
    renderer(_chart);
    customRenderer && customRenderer(_chart);
    updateChart(_chart);
  }, [G2, renderer, chart, updateChart, id, customRenderer]);

  React.useEffect(initChart, [G2, initChart]);

  /** 组件销毁时，销毁 chart 实例，解除事件绑定 */
  React.useEffect(() => () => (chart ? chart.destroy() : undefined), [chart]);

  React.useEffect(() => {
    if (!chart) return;
    chart.clear();
    renderer(chart);
    customRenderer && customRenderer(chart);
    let _data = data;
    if (parseData) {
      _data = parseData(_data);
    }
    if (customLoadData) {
      chart.clear();
      customLoadData(chart, _data);
      chart.render();
    } else if (_data && _data.length) {
      if (loadData) {
        loadData(chart, _data);
      } else {
        chart.data(_data);
      }
      chart.render();
    } else {
      chart.annotation().clear(true);
      chart.clear();
      chart.data([]);
      chart.render();
    }
  }, [chart, data, loadData, renderer, parseData, customLoadData, customRenderer]);

  let empty: React.ReactNode = props.empty || null;
  if (!data?.length) {
    empty = (
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
      }}
      >
        { empty || <Empty description={error ? error.message : '暂无数据'} image={Empty.PRESENTED_IMAGE_SIMPLE} /> }
      </div>
    );
  } else {
    empty = null;
  }

  return (
    <div
      id={id} style={{
        width, height, position: 'relative', ...props.style,
      }} className={className}
    >
      {props.children}
      {empty}
    </div>
  );
}
