/* eslint-disable react/no-children-prop */
import React from 'react';
import { Icon } from '@mtfe/sjst-antdx-next';
import { ScrollerProps, Props } from '../../../types/config';
import { ScrollerContext } from '../../../types/context';
import { registorComponent } from '../registor';
import ReportComponent from '../../ReportComponent';
import './Scroller.less';

export default function Scroller(props: Props<ScrollerProps>) {
  const {
    children, direction, padding, isFixed, ...rest
  } = props;
  const ref = React.useRef<HTMLDivElement | null>(null);
  // const [scrollState, updateScrollState] = React.useState<ScrollState | null>(null);

  // const onScroll = React.useCallback((event: React.UIEvent<HTMLDivElement, UIEvent>) => {
  //   const scroller = event.target as HTMLDivElement;
  //   if (!scroller) return;
  //   const {
  //     scrollLeft,
  //     scrollTop,
  //     scrollWidth,
  //     scrollHeight,
  //   } = scroller;

  //   updateScrollState({
  //     scrollLeft,
  //     scrollTop,
  //     scrollWidth,
  //     scrollHeight,
  //   });
  // }, []);

  const style: React.CSSProperties = {};
  style.padding = padding;
  switch (direction) {
    case 'vertical':
      style.overflowX = 'hidden'; break;
    case 'horizontal':
      style.overflowY = 'hidden'; break;
    case 'both':
    default:
  }

  const scrollToTop = React.useCallback(() => {
    ref.current?.scroll(0, 0);
  }, []);

  return (
    <ScrollerContext.Provider value={{ isFixed, domRef: ref }}>
      <div className="auto-report-scroller" {...rest} ref={ref} style={style}>
        <ReportComponent children={children} />
        <a className="goto-top-button" onClick={() => scrollToTop()}><Icon type="vertical-align-top" /></a>
      </div>
    </ScrollerContext.Provider>
  );
}

registorComponent('Scroller', Scroller);
