const pageView = jest.fn(() => () => null);
const moduleView = jest.fn(() => () => null);
const moduleClick = jest.fn(() => () => null);
jest.mock('@mtfe/next-biz/src/utils/analytics', () => ({
  pageView,
  moduleView,
  moduleClick,
}));
const { default: useAnalysis } = require('./useAnalysis');
const { renderHook } = require('@testing-library/react-hooks');

describe('useAnalysis', () => {
  let analysis;
  const cid = 2342342432;
  
  beforeEach(() => {
    const { result, waitForNextUpdate } = renderHook(() => useAnalysis(true, cid));
    analysis = result.current;
  });

  afterEach(() => {
    pageView.mockClear();
    moduleView.mockClear();
    moduleClick.mockClear();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  it('mount 时触发 pageview', () => {
    expect(pageView.mock.calls.length).toBe(1); 
    expect(pageView.mock.calls[0]).toEqual([cid]); 
  });


});
