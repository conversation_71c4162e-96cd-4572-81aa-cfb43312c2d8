import React from 'react';
import { withAdapter } from '../../../core/withAdapter';
import { ComponentProps, Config } from '../../../types';
import './pc.less';

interface Props extends ComponentProps<string> {
  title?: string;
  titleHelp?: Config;
  isFixed?: boolean;
  children?: React.Component;
}

function PageHeader({
  children,
  title,
  titleHelp,
  isFixed,
  baseRefObject,
  updateStatus,
}: Props) {
  const _classNames = ['auto2-page-header'];

  // 没有标题时，浮动显示到右上角
  if (isFixed) {
    _classNames.push('fixed');
  }

  React.useEffect(() => {
    updateStatus?.('done', title);
  }, [title]);

  return (
    <div className={_classNames.join(' ')}>
      {!isFixed && (
        <h1 className="auto2-header-title">
          {title}
          {baseRefObject?.renderManager.render(titleHelp)}
        </h1>
      )}
      <div className="auto2-header-toolbar">{children}</div>
    </div>
  );
}

export default withAdapter(PageHeader, { name: 'PageHeader' });
