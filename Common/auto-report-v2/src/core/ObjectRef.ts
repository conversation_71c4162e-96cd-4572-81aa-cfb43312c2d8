import { findLast } from 'lodash';
import { <PERSON><PERSON>, WildcardHandler } from 'mitt';
import loglevel from 'loglevel';
import { StoreControlType } from '../contexts';
import Analytics from '../helper/analytics';
import {
  BaseObjectRefType,
  ObjectRefType,
  ObjectRefValueType,
  RenderManagerType,
} from '../types';
export class BaseObjectRef<ValueType> implements BaseObjectRefType<ValueType> {
  protected data: ObjectRefValueType<ValueType>;
  analytics: Analytics;
  renderManager: RenderManagerType;
  version?: number;
  constructor(
    data: ObjectRefValueType<ValueType>,
    analytics: Analytics,
    renderManager: RenderManagerType,
    version?: number
  ) {
    this.data = data;
    this.analytics = analytics;
    this.renderManager = renderManager;
    this.version = version;
  }

  get id() {
    return this.data.id;
  }
  get path() {
    return this.data.path;
  }
  get name() {
    return this.data.name;
  }
  get dataKey() {
    return this.data.dataKey;
  }
  get value() {
    return this.data.value;
  }
  get defaultValue() {
    return this.data.defaultValue;
  }
  get temporaryValue() {
    return this.data.temporaryValue;
  }
  get status() {
    return this.data.status;
  }
  get otherProps() {
    return this.data.otherProps;
  }
  get all() {
    return this.data.emitter$.all;
  }
  get logger() {
    return loglevel.getLogger(`Auto-${this.name}`);
  }

  on<T = any>(type: string, handler: Handler<T>): void;
  on(type: '*', handler: WildcardHandler) {
    this.data.emitter$.on(type, handler);
  }

  off<T = any>(type: string, handler: Handler<T>): void;
  off(type: '*', handler: WildcardHandler) {
    this.data.emitter$.off(type, handler);
  }

  emit<T = any>(type: string, event?: T) {
    this.data.emitter$.emit(type, event);
  }

  getObjectRef(control: StoreControlType<ValueType>) {
    return new ObjectRef(
      control,
      this.data,
      this.analytics,
      this.renderManager
    );
  }
}

export default class ObjectRef<ValueType> extends BaseObjectRef<ValueType>
  implements ObjectRefType<ValueType> {
  private control: StoreControlType<ValueType>;
  constructor(
    control: StoreControlType<ValueType>,
    data: ObjectRefValueType<ValueType>,
    analytics: Analytics,
    renderManager: RenderManagerType
  ) {
    super(data, analytics, renderManager);
    this.control = control;
  }

  get children() {
    const values = this.control.withByParentPath(this.path);
    return values.map(
      (v) => new ObjectRef(this.control, v, this.analytics, this.renderManager)
    );
  }

  on<T = any>(type: string, handler: Handler<T>): void;
  on(type: '*', handler: WildcardHandler) {
    this.data.emitter$.on(type, handler);
  }

  off<T = any>(type: string, handler: Handler<T>): void;
  off(type: '*', handler: WildcardHandler) {
    this.data.emitter$.off(type, handler);
  }

  emit<T = any>(type: string, event?: T) {
    const dataKeyStartIndex = type.indexOf('#');
    const parentNameStartIndex = type.indexOf('@');
    if (dataKeyStartIndex !== -1 || parentNameStartIndex !== -1) {
      setTimeout(() => {
        if (dataKeyStartIndex !== -1) {
          const refByDataKey = this.withDataKey(
            type.slice(dataKeyStartIndex + 1)
          );
          refByDataKey?.emit(type.slice(0, dataKeyStartIndex), event);
        } else if (parentNameStartIndex !== -1) {
          const refByParentName = this.withParentName(
            type.slice(parentNameStartIndex + 1)
          );
          refByParentName?.emit(type.slice(0, parentNameStartIndex), event);
        }
      });
    } else {
      this.data.emitter$.emit(type, event);
    }
  }

  getRefList(): ObjectRefValueType<any>[] {
    return this.control.getRefList();
  }

  withId(id: string): ObjectRefType<ValueType> | undefined {
    const value = this.control.withId(id);
    return value
      ? new ObjectRef(this.control, value, this.analytics, this.renderManager)
      : undefined;
  }

  withDataKey(dataKey: string): ObjectRefType<ValueType> | undefined {
    const value = this.control.withDataKey(dataKey);
    return value
      ? new ObjectRef(this.control, value, this.analytics, this.renderManager)
      : undefined;
  }

  withParentName(parentName: string): ObjectRefType<ValueType> | undefined {
    const id = findLast(
      this.path,
      (p) => p.split('@').indexOf(parentName) !== -1
    );
    return id ? this.withId(id) : undefined;
  }

  withChildrenName(childrenName: string): ObjectRefType<ValueType>[] {
    const values = this.control.withByChildrenName(this.path, childrenName);
    return values.map(
      (v) => new ObjectRef(this.control, v, this.analytics, this.renderManager)
    );
  }
}
