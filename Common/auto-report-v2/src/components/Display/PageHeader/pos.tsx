import React from 'react';
import { useStore } from '@mtfe/auto-base/Store';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import { ObjectRefType, ComponentProps, ReportValueType } from '../../../types';
import './pos.less';

interface Props extends ComponentProps<string> {
  title?: string;
  isFixed?: boolean;
  children?: any;
}

function PageHeader({
  children,
  title,
  isFixed,
  baseRefObject,
  updateStatus,
}: Props) {
  const refObject = useRefObject(baseRefObject);

  const reportRefObject =
    refObject?.version === 2
      ? useStore<any>((store) => {
          return store.getters.withParentName(refObject?.path, 'Report');
        }).value
      : ((refObject as ObjectRefType<any>)?.withParentName(
          'Report'
        ) as ObjectRefType<ReportValueType>);

  React.useEffect(() => {
    updateStatus?.('done', title);
  }, [title]);

  return React.useMemo(() => {
    const _classNames = ['auto2-page-header'];

    // 没有标题时，浮动显示到右上角
    if (isFixed) {
      _classNames.push('fixed');
    }
    if (reportRefObject?.status !== 'done') {
      return null;
    }
    return (
      <div className={_classNames.join(' ')}>
        <div className="auto2-header-toolbar">{children}</div>
      </div>
    );
  }, [isFixed, children, reportRefObject?.status]);
}

export default withAdapter(PageHeader, { name: 'PageHeader' });
