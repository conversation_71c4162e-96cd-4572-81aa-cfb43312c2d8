import { showModal, Modal } from '@mtfe/sjst-antdx-next';
import React from 'react';
// import { bizTableExplain, dishTableExplain } from '@rms-report/consts/tableExplain';
import axios from 'axios';

export interface ExplainProps {
  title: string;
  content: string;
}

const protocol = window.location.protocol;

export const getTableExplain = async () => {
  try {
    // http://portal-portm.meituan.com/horn/v1/modules/rms-report/prod  线上环境
    // http://portal-portm.meituan.com/horn/v1/modules/rms-report/test  测试环境
    const result = await axios.get(
      `${protocol}//portal-portm.meituan.com/horn/v1/modules/rms-report/${process.env.AWP_BUILD_ENV === 'production' ? 'prod' :'test'}`
    );
    return result.data;
  } catch (error) {
    Modal.error(error.message || '数据获取异常');
    console.error(error);
    return {};
  }
}

export async function tableExplain(type: 'biz' | 'dish') {
  const explainJson = await getTableExplain();
  const bizTableExplain = explainJson?.bizTableExplain;
  const dishTableExplain = explainJson?.dishTableExplain;
  return showModal({
    title: type === 'biz' ? bizTableExplain?.title : dishTableExplain?.title,
    width: 800,
    renderFooter: (cancelButton, okButton) => okButton.node,
    className: 'report-explain-modal',
    children: <>
      <div dangerouslySetInnerHTML={{__html: type === 'biz' ? bizTableExplain?.content : dishTableExplain?.content}} />
    </>,
  });
}
