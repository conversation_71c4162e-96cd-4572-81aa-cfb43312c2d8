/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable import/no-cycle */
import React from 'react';
import type { ReportInfo } from '@mtfe/next-biz/src/services/reports';
import type { ReportProps, Props } from './config';
import type { Filter, InputSet, InputConstructorSet } from './filter';
import type { DataNode } from './response';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';

export type SerializedQuery = {
  [k: string]: string,
};

/** 工具栏管理 */
export type ToolsManager = {
  tools: Tool[],
  updateTools: (tools: {[k: string]: Tool | null}) => void,
}

/** 图表/表格工具 */
export type Tool = {
  icon: string,
  label: string,
  onClick: () => void,
};

/** 书签管理 */
export type BookmarkManager = {
  reports: ReportInfo[],
  bookmarks: ReportInfo[],
  bookmarked: boolean,
  updateBookmarks: null | ((enable: boolean) => Promise<void>),
}

/** 埋点管理 */
export type AnalysisManager = {
  cid?: string,
  pageView: () => void,
  moduleView: (bid?: string) => void,
  moduleClick: (bid?: string) => void,
}

/**
 * 报表状态上下文
 */
export type RequestManager = {
  loading: boolean,
  updateDataByQuery: (_query: any, onSuccess?: () => void) => Promise<void>;
  rootNode: DataNode | null;
  query: any;
  clearTime: { startDate: number, endDate: number } | null;
  doRequest: (_query: any) => Promise<DataNode>;
  error: Error | null,
  noDailySettlement?: boolean,
}

/** 查询条件管理 */
export type FilterManager = {
  inited: boolean,
  filters: Filter[];
  query: any,
  originQuery: any,
  serializedQuery: any,
  defaultQuery: any,
  updateQueryBySerialized: (v: {[k: string]: string | string[] | undefined}) => void,
  updateQuery: React.Dispatch<React.SetStateAction<any>>,
  updateFilters: React.Dispatch<React.SetStateAction<Filter[]>>,
  updateDisplayQuery: React.Dispatch<React.SetStateAction<any>>,
  updateOriginQuery: React.Dispatch<React.SetStateAction<any>>,
  getQueryInfo: () => string;
  prepareQuery: (hasSave?: boolean) => any;
  inputs: InputSet;
  errors: Record<string, React.ReactNode>;
  isSubFilter: boolean,
  noDailySettlement?: boolean,
}

/** 导出管理 */
export type ExportManager = {
  exportable: boolean,
  extraExportParams: any,
  title: string,
  updateExportable: React.Dispatch<React.SetStateAction<boolean>>,
  getExportUrl: () => Promise<{
    isOffLineDownloading: boolean,
    totalCount: number,
  } | string | undefined>,
  updateExportParams: React.Dispatch<React.SetStateAction<any>>,
}

/** 路由管理 */
export type RouterManager = {
  updateSerializedQuery: (v: SerializedQuery) => void,
}

/** 价税管理 */
export type PriceTaxManager = {
  updateHavePriceTaxField: React.Dispatch<React.SetStateAction<boolean>>,
  priceTaxEnabled: boolean | null,
}

/** 环境数据 */
export type Envs = {
  os?: 'pc' | 'pos' | 'h5' | 'screen',
}

export type ReportContextValue = {
  inited: boolean,
  props: Props<ReportProps>,
  filterManager: FilterManager,
  requestManager: RequestManager,
  exportManager: ExportManager,
  priceTaxManager: PriceTaxManager,
  bookmarkManager: BookmarkManager,
  toolsManager: ToolsManager,
  analysisManager: AnalysisManager,
  isSubReport: boolean,
  parent?: ReportContextValue,
  firstRequestFinished?: boolean,
  businessModuleId?: BusinessModule,
  // routerManager: RouterManager,
};

export const ReportContext = React.createContext<ReportContextValue | null>(null);

/**
 * 布局上下文
 */
export type ScrollState = {
  isFixed?: boolean,
  domRef: React.MutableRefObject<HTMLDivElement | null>,
};

export const ScrollerContext = React.createContext<ScrollState | null>(null);

/** 容器上下文 */
export type ContainerContext = {
  visiable?: boolean,
  shrink?: boolean,
}

export type StateContextDispatchType = {
  type: 'update',
  payload: any,
};

export type StateContextType = {
  state?: any, // 这里使用 any 是想能够支持所有的自定义全局 state
  dispatch?: React.Dispatch<StateContextDispatchType>,
}

export const ContainerContext = React.createContext<ContainerContext | null>(null);

export type Components = {
  [k: string]: React.ComponentType,
};

/** 环境上下文 */
export const envsContext = React.createContext<Envs>({});

/** 组件库上下文 */
export const componentsContext = React.createContext<Components>({});

/** 输入组件上下文 */
export const inputSetContext = React.createContext<InputConstructorSet>({});

/** 状态上下文 */
export const stateContext = React.createContext<StateContextType>({});
