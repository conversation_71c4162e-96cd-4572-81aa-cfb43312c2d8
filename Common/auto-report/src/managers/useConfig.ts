/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import Net from '@mtfe/next-biz/src/lib/Net';
import { doAction } from '@mtfe/next-biz/src/lib/actions';

async function getConfigFromServer(view: string, version: string): Promise<any> {
  const resp = await Net.get(`/api/v1/wing/apps/Rms/views/${view}/versions/${version}`);
  return JSON.parse(resp);
}

export default function useConfig<T>(props: {version: string, view: string} | T): (T|null) {
  let version: string | undefined;
  let view: string | undefined;

  if ('view' in props) {
    view = props.view;
    version = props.version;
  }

  /** 配置 */
  const [config, updateConfig] = React.useState<T | null>(null);

  /**
   * 初始化配置
   */
  const init = React.useCallback(async () => {
    if (view && version) {
      const result = await doAction(getConfigFromServer(view, version));
      if (!result) return;
      updateConfig(result);
    } else {
      updateConfig(null);
    }
  }, [updateConfig, view, version]);

  React.useEffect(() => { init(); }, [init]);

  return view ? config : props as T;
}
