/**
 * 带管控、点位控制的门店选择器
 */
import React from 'react';
import { Org, PoiTreeSelectorV2 as PoiTree, Props as PoiTreeProps } from '@mtfe/next-biz/es/components/Org';
import { ValueControl } from '../types';

export type PoiTreeMaterialProps = {
  name: 'poi-tree-selector';
  props?: PoiTreeProps;
};

type Props = ValueControl<any> & PoiTreeMaterialProps;

export class PoiTreeMaterial extends React.Component<Props> {
  getValue = () => {
    const { value } = this.props;
    if (Array.isArray(value) && typeof value[0] === 'object') {
      return (value as Org[]).map?.(_ => _.poiId);
    }
    return value;
  };
  render() {
    const { props, onChange } = this.props;
    return <PoiTree {...props} multiple value={this.getValue()} onChange={onChange} />;
  }
}
