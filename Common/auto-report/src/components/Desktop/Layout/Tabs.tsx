import React from 'react';
import {
  Tabs,
} from 'antd';
import {
  TabsProps, TabProps, Props,
} from '../../../types';
import {
  ContainerContext,
} from '../../../types/context';
import ReportComponent from '../../ReportComponent';
import { registorComponent } from '../registor';
import './Tabs.less';

export function Tab(props: Props<TabProps>, activeKey: string | undefined) {
  const { key, title } = props;

  const [_title, _key] = React.useMemo(() => {
    if (typeof title === 'string') return [title, key || title];
    return [
      <ReportComponent>{title}</ReportComponent>,
      key,
    ];
  }, [key, title]);

  return (
    <Tabs.TabPane tab={_title} key={_key}>
      <ContainerContext.Provider value={{ visiable: activeKey === _key }}>
        <ReportComponent>{props.children}</ReportComponent>
      </ContainerContext.Provider>
    </Tabs.TabPane>
  );
}

export default function TabsLayout(props: Props<TabsProps>) {
  const { defaultActiveKey, tabs } = props;
  const [activeKey, updateActiveKey] = React.useState<string | undefined>(() => {
    if (defaultActiveKey) return defaultActiveKey;
    const firstTab = tabs && tabs[0];
    return firstTab?.props?.key || String(firstTab?.props?.title);
  });

  const onChange = React.useCallback((k: string) => {
    updateActiveKey(k);
  }, [updateActiveKey]);

  return (
    <Tabs onChange={onChange} activeKey={activeKey} className="auto-report-tabs">
      {tabs.map(tab => Tab(tab.props as Props<TabProps>, activeKey || defaultActiveKey))}
    </Tabs>
  );
}

registorComponent('Tabs', TabsLayout);
