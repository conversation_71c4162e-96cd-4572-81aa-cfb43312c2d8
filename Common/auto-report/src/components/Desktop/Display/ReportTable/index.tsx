/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Alert,
} from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import { TableY, ITableYColumnProps, Empty } from '@mtfe/sjst-antdx-next';
import {
  ReportTableProps,
  ReportContext,
  ContainerContext,
  Column,
} from '../../../../types';
import { REPORT_TABLE_PREFIX } from '../../../common/constant';
import useParseData from './useParseData';
import { registorComponent } from '../../registor';
import useFixedHeader from './useFixedHeader';
import useFullscreen from '../useFullscreen';
import usePagination from './usePagination';
import ControlTip, { isControlError } from '../../../ControlTip';
import './index.less';
import { Row } from './useParseData/types';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';

export default function ReportTable(props: ReportTableProps) {
  const id = React.useMemo(() => `${Math.round(Math.random() * 1000000)}`, []);
  const container = React.useContext(ContainerContext);
  const report = React.useContext(ReportContext);
  const [sortDirection, setSortDirection] = React.useState<'descend' | 'ascend' | undefined>(undefined);
  const {
    requestManager,
    filterManager,
    exportManager,
    businessModuleId = BusinessModule.收银报表
  } = report || {};
  const { updateDisplayQuery } = filterManager || {};
  const {
    query, error, rootNode, loading,
  } = requestManager || {};
  const { updateExportParams, updateExportable } = exportManager || {};
  const {
    sorterModal,
    tableColumns: columns,
    rows,
    // width,
    exportColumns,
    // isTreeTable,
  } = useParseData(props, report?.props.view || report?.props.title);
  const pagination = usePagination(loading, updateDisplayQuery, query, rootNode, props.pagination);
  useFixedHeader({ tableId: id });
  const fullscreenContainer = useFullscreen({ id, bid: props.fullscreenBid });

  /** 有数据的时候，才能显示导出 */
  React.useEffect(() => {
    if (!updateExportable) return;
    if (rows?.length) {
      updateExportable(true);
    } else {
      updateExportable(false);
    }
  }, [rows, updateExportable]);

  /** 更新导出的列配置 */
  React.useEffect(() => {
    if (!updateExportParams) return;
    const tableConfig = {
      type: 'ReportTable',
      props: {
        columns: exportColumns,
      },
    };
    updateExportParams({ tableConfig });
  }, [exportColumns, updateExportParams]);

  /** 表格列上的筛选项 */
  const getFilters = React.useCallback((filters: {[k: string]: Array<string | number>}) => {
    const result: {[k: string]: Array<string | number> | number | string } = {};
    Object.entries(filters).forEach(([k, v]) => {
      const column = props.columns.find(c => c.field === k);
      if (!column?.filterField) return;
      result[column?.filterField] = column.filterMultiple ? v : v[0];
    });
    return result;
  }, [props.columns]);

  /** 更新表格的分页/过滤/排序条件 */
  const onChange = React.useCallback((
    _: PaginationProps,
    filters,
    sorter?: {
      column: Column,
      order: 'descend' | 'ascend',
      field: string,
    },
  ) => {
    const tableQuery = getFilters(filters);
    const orderByMap = {
      descend: 'desc',
      ascend: 'asc',
    };

    setSortDirection(sorter?.order);

    updateDisplayQuery && updateDisplayQuery((lastQuery: any) => {
      const orderBy = sorter?.column?.alternate || sorter?.field;
      const orderByType = sorter ? orderByMap[sorter.order] : undefined;
      const newQuery = {
        pageSize: lastQuery?.pageSize || 20,
        pageNo: lastQuery?.pageNo || 1,
        orderBy,
        orderByType,
        ...tableQuery,
      };
      const needGoBackToPage1 = lastQuery && !!Object.keys(newQuery).find((key: keyof typeof newQuery) => {
        if (key === 'pageNo') return false;
        return newQuery[key] !== lastQuery[key];
      });

      if (needGoBackToPage1) {
        newQuery.pageNo = 1;
      }
      return newQuery;
    });
  }, [getFilters, updateDisplayQuery, exportColumns, setSortDirection]);

  const className = ['auto-report-table'];
  if (container?.shrink) {
    className.push('shrink');
  }
  const onRow = React.useCallback((row: Row) => {
    if (row && row.isTotalRow) {
      if (row.isTotalRow === 'tree-total-row') {
        return { className: 'tree-total-row' };
      } else {
        return { className: 'total-row' };
      }
    }
  }, []);

  // 自动为每个列加上 Key， 默认为 index
  const columnsWithKey = React.useMemo(() => columns.map((c, index) => ({ ...c, key: c.dataIndex || index })), [columns]);

  const expnadIndex = columns.findIndex(c => c.treeColumn);

  const table = (
    <div id={`${REPORT_TABLE_PREFIX}${id}`} className={className.join(' ')}>
      <TableY
        // @ts-ignore onRow 支持设置 className，但是类型没声明出来
        onRow={onRow}
        rowKey="id"
        expandRowByClick
        sortOrder={sortDirection}
        loading={loading ? {
          tip: '正在查询...',
        } : undefined}
        locale={{emptyText: !report?.firstRequestFinished ? <Empty description="请选择查询条件并查询" image={Empty.PRESENTED_IMAGE_SIMPLE}/> : undefined}}
        expandIconColumnIndex={expnadIndex === -1 ? undefined : expnadIndex}
        onChange={React.useCallback((p, s, f) => onChange(p, s, f), [])}
        pagination={false}
        scroll={{ x: 'max-content' }}
        columns={columnsWithKey as ITableYColumnProps<{}>[]}
        dataSource={rows?.length ? rows : undefined}
        size={(container?.shrink) ? 'small' : 'middle'}
        bordered={!(container?.shrink)}
      />
      {pagination}
    </div>
  );

  if (error) {
    if (isControlError(error.message)) {
      error.message = '';
    }
  }

  const alert = error && error.message ? <Alert message={error.message} type="error" style={{ marginBottom: 8 }} showIcon /> : null;

  return (
    <>
      {sorterModal}
      {alert}
      <ControlTip businessModuleId={businessModuleId || props.businessModuleId} isChainReport={props.isChainReport} tipStyle={{ marginBottom: '10px' }}>
        {fullscreenContainer(table)}
      </ControlTip>
    </>
  );
}

registorComponent('ReportTable', ReportTable);
