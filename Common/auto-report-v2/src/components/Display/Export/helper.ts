/* eslint-disable @typescript-eslint/no-explicit-any */
import moment from 'moment';
import { isArray } from 'lodash';
import Net from '@mtfe/next-biz/es/lib/Net';
import { buildPostApi } from '../../../services/buildApi';
import serviceManager from '../../../core/ServiceManager';
import {
  excelGetExport as excelGetExportApi,
  excelPostExport as excelPostExportApi,
} from '../../../services/download';
import { recursiveColumns } from '../Table/helper';
import { Config, ObjectRefType, ReportQuery } from '../../../types';
import { TableColumnProps } from '../Table/types';
import { ExportDataType } from './types';

const format = 'YYYY-MM-DD HH:mm';

export function filterS3path(name: string) {
  return name.match(/([\u4e00-\u9fa5]|[a-z]|\d|_|-|\.)+/gi)?.join('_') || ''; // 这里只匹配中文、字母、数字、_、-、.
}

export const getQueryInfo = (
  refObject: ObjectRefType,
  queryItems?: Config[],
  originalQuery?: ReportQuery,
) => queryItems
    ?.map((queryItem) => {
      const value = originalQuery?.[queryItem.props?.dataKey || ''];
      const component = refObject.renderManager.getComponent(
        queryItem.type || '',
      );
      const getDisplayValue = component?.prototype?.getDisplayValue;
      const prefix = queryItem.props?.label
        ? `${queryItem.props?.title || queryItem.props?.label}：`
        : '';
      const getExportValue = component?.prototype?.getExportValue;
      const exportValue = getExportValue?.(value, queryItem.props);
      if (exportValue) {
        return `${prefix}【${exportValue}】`;
      }
      const displayValue = getDisplayValue
        ? getDisplayValue(value, queryItem.props)
        : value;
      const displayValueArr = displayValue?.split?.('，') || [];
      if (!displayValue || !displayValueArr.length) {
        return null;
      }
      if (queryItem.type === 'CheckboxGroup') {
        return displayValue;
      }
      if (displayValue && displayValueArr.length > 1) {
        return `${prefix}【已选${displayValueArr.length}个】`;
      }
      return displayValue ? `${prefix}【${displayValue}】` : '';
    })
    .filter(Boolean)
    .join('；');

export const getColumns = (columns: TableColumnProps<any>[]) => {
  const _columns: TableColumnProps<any>[] = [];
  recursiveColumns(
    columns,
    (column) => {
      if (!column.children?.length) {
        _columns.push(column);
      }
      return column;
    },
    column => !column.hide,
  );
  return _columns;
};

export const autoExport = async (
  refObject: ObjectRefType,
  data: ExportDataType,
) => {
  const {
    user,
    title,
    originalQuery,
    query,
    queryItems,
    columns,
    options,
    exportPermissionCode
  } = data;
  const login = user?.login || '';
  const loginName = user?.loginName || '';
  const time = moment().format(`${format}:ss`);
  const reportName = filterS3path(
    `${loginName}_${title}_${time}_${login}.xlsx`,
  );
  const reportParams = {
    applyContent: `${title} (查询时间范围：${moment(
      query?.startDate || query?.startTime || query?.beginTime,
    ).format(format)}-${moment(query?.endDate || query?.endTime).format(
      format,
    )})`,
    headerDes: [title, getQueryInfo(refObject, queryItems, originalQuery)],
    loginAccount: login,
    poiName: loginName,
    reportName,
    reportParams: { ...query, queryKey: options?.queryKey },
    reportTitle: title,
    tableConfig: {
      type: 'ReportTable',
      props: {
        columns: getColumns(columns || []).map(v => ({
          ...v,
          field: v.dataIndex,
          label: v?.customExportTitle || v.title,
        })),
      },
    },
  };

  const authRes = await Net.post('/api/v1/excel/export/auth', { exportPermissionCode }) ?? true;
  if (!authRes) {
    return {
      isNotAuth: !authRes
    };
  } else {
    const res = (await Net.post(
      '/api/v1/excel/nest/report/download',
      {
        ...reportParams,
        ...options?.extraParams,
      },
      {
        timeout: 60 * 1000, // 超时时间设成60s
      },
    )) as {
      url: string;
      isOffLineDownloading: boolean;
      totalCount: number;
    };
  
    return {
      reportName,
      link: res.url,
      totalCount: res.totalCount,
    };
  }
};

export const excelGetExport = async (
  refObject: ObjectRefType,
  data: ExportDataType,
) => {
  const { exportConfig } = serviceManager;
  const {
    user,
    title,
    originalQuery,
    query,
    queryItems,
    columns,
    options,
    totalCount,
    queryInfo,
    exportPermissionCode
  } = data;
  const login = user?.login || '';
  const loginName = user?.loginName || '';
  const time = moment().format(`${format}:ss`);
  const reportName = filterS3path(
    `${loginName}_${title}_${time}_${login}.xlsx`,
  );
  // 将exportPermissionCode转为数组
  const _exportPermissionCode = !!exportPermissionCode ? JSON.stringify(isArray(exportPermissionCode) ? exportPermissionCode : [exportPermissionCode]) : undefined;
  const reportParams: any = {
    reportTitle: title,
    reportName,
    exportPermissionCode: _exportPermissionCode,
    applyContent: `${title} (查询时间范围：${moment(
      query?.startDate || query?.startTime || query?.beginTime,
    ).format(format)}-${moment(query?.endDate || query?.endTime).format(
      format,
    )})`,
    columns: getColumns(columns || [])
      .map((v) => {
        if (typeof v.dataIndex === 'object') {
          return v.dataIndex?.join('.');
        }
        return v.dataIndex;
      })
      .filter(Boolean)
      .join(','),
  };

  reportParams[exportConfig?.loginNameKey || 'loginName'] = loginName;
  if (exportConfig?.useQueryInfo !== false) {
    reportParams.queryInfo = getQueryInfo(refObject, queryItems, originalQuery);
  }

  if (queryInfo) {
    reportParams.queryInfo = queryInfo;
  }

  if (exportConfig?.useTotalCount === true) {
    reportParams.totalCount = totalCount;
  }

  // 历史数据格式有问题
  // @ts-ignore
  const res = await excelGetExportApi({
    ...query,
    ...reportParams,
    ...options,
  });
  return {
    reportName,
    link: res.url,
    totalCount: res.type === 3 ? -1 : res.totalCount,
  };
};

export const excelPostExport = async (
  refObject: ObjectRefType,
  data: ExportDataType,
) => {
  const { exportConfig, $post } = serviceManager;
  const {
    user,
    originalQuery,
    query,
    queryItems,
    columns,
    options,
    totalCount,
    queryInfo,
    exportPermissionCode
  } = data;
  const { reportTitle, version = 2, url, needQueryInfo = false, isCustomReportExport = false, getExportParams, ...otherOptions } = options || {};
  const title = reportTitle || data.title;

  const login = user?.login || '';
  const loginName = user?.loginName || '';
  const time = moment().format(`${format}:ss`);
  const reportName = filterS3path(
    `${loginName}_${title}_${time}_${login}.xlsx`,
  );
  const dataRange = query?.startDate || query?.startTime ? `(查询时间范围：${moment(
    Number(query?.startDate || query?.startTime),
  ).format(format)}-${moment(Number(query?.endDate || query?.endTime)).format(
    format,
  )})` : '';
  // 将exportPermissionCode转为数组
  const _exportPermissionCode = !!exportPermissionCode ? JSON.stringify(isArray(exportPermissionCode) ? exportPermissionCode : [exportPermissionCode]) : undefined;
  const reportParams: any = {
    reportTitle: title,
    reportName,
    exportPermissionCode: _exportPermissionCode,
    applyContent: `${title} ${dataRange}`,
    columns: getColumns(columns || [])
      .map((v) => {
        const key = v.key || v.dataIndex;
        if (typeof key === 'object') {
          return key?.join('.');
        }
        return key;
      })
      .filter(Boolean)
      .join(','),
  };

  reportParams[exportConfig?.loginNameKey || 'loginName'] = loginName;
  if (exportConfig?.useQueryInfo !== false) {
    reportParams.queryInfo = getQueryInfo(refObject, queryItems, originalQuery);
  }

  if (queryInfo) {
    reportParams.queryInfo = queryInfo;
  }

  if (exportConfig?.useTotalCount === true) {
    reportParams.totalCount = totalCount;
  }

  if (needQueryInfo) {
    reportParams.queryParams = JSON.stringify({
      queryField: {
        ...query,
        queryInfo: reportParams?.queryInfo || '',
      }
    });
  }

  let exportApi = excelPostExportApi;
  if (url) {
    if ($post) {
      exportApi = async (params: any) => {
        const d = await $post(url, params);
        return {
          ...d.data || {},
          code: d.code,
        }
      }
    } else {
      exportApi = buildPostApi(url, null, { baseURL: '/' });
    }
  }
  let newReportParams = version === 2 ? {
    queryField: JSON.stringify(query),
    ...reportParams,
    ...otherOptions,
  } : {
    ...query,
    ...reportParams,
    ...otherOptions,
  };
  if (isCustomReportExport) {
    delete newReportParams?.queryField;
    delete newReportParams?.columns;
  }

  if (getExportParams) {
    newReportParams = getExportParams(data, newReportParams) ?? getExportParams;
  }

  const res = await exportApi(newReportParams);

  return {
    reportName,
    code: res.code,
    link: res.url,
    totalCount: res.type === 3 ? -1 : res.totalCount,
    resTotalCount: res.totalCount,
  };
};
