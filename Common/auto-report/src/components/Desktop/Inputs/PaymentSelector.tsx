import React from 'react';
import ReportPaymentSelector, { PaymentOrDiscount, Type } from '@mtfe/next-biz/src/components/Payment/ReportPaymentSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  PaymentSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: PaymentSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as PaymentSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  getPaymentType() {
    return this.config.paymentType?.map((p) => {
      if (p === 'payment') {
        return Type.Payment;
      } else if (p === 'discount') {
        return Type.Discount;
      } else {
        return null;
      }
    }).filter(Boolean) as Type[];
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportPaymentSelector
        size={props.size}
        multiple={!!multiple}
        useMultiplePopSearch
        placeholder="全部"
        {...otherProps}
        customFilter={{ type: this.getPaymentType(), ...deps }}
      />
    );
  }

  getDisplayValue(paymentOrDiscount?: PaymentOrDiscount | PaymentOrDiscount[]) {
    return toArray(paymentOrDiscount)?.map(o => o.name).join('，') || '全部';
  }

  getValue(paymentOrDiscount: PaymentOrDiscount | PaymentOrDiscount[] | undefined) {
    return toArray(paymentOrDiscount)?.map(o => o.fullName);
  }
}
