/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Format, Cell, Row } from './types';
import {
  TableColumnConfig,
} from '../../../../../types';
import { formatMap } from '../../../../common/format';

export const defaultFormat: Format = (value: number | string | null) => {
  if (typeof value === 'undefined' || value === null) return '-';
  return value;
};

const getCell = (column: TableColumnConfig, cell: Cell | undefined, row: Row) => {
  const alternate = column.alternate;
  if (alternate) {
    cell = cell || row[alternate];
  }
  return cell;
};

export default () => {
  /** 单元格渲染 */
  const cellRender = React.useCallback((column: TableColumnConfig) => (cell: Cell | undefined, row: Row) => {
    const { isTotalRow } = row || {};

    cell = getCell(column, cell, row);
    let data = cell?.data;
    let dom: any;
    const tdClassName: string[] = [];
    if (isTotalRow && column.showTotal === false) {
      data = undefined; // 列上设置了不显示合计行，就不渲染合计数据
    } else {
      const { format } = column;
      let formatter: Format | null | undefined;
      if (typeof format === 'string') {
        formatter = format ? formatMap[format] : null;
      } else {
        formatter = format || null;
      }
      dom = formatter ? formatter(data, row) : data;
    }

    dom = defaultFormat(dom, row);
    const { width } = column;
    if (width) {
      dom = <div className="td-inner" style={{ maxWidth: column.width, width: 'max-content', display: 'inline-block' }}>{dom}</div>;
      tdClassName.push('fixed-width');
    }

    if (cell?.bold) {
      tdClassName.push('bold');
    }

    return {
      props: {
        className: tdClassName.join(' '),
        rowSpan: cell?.rowSpan,
        // 16 为单元格的 padding
        width: width ? width + 16 : width,
      },
      children: dom,
    };
  }, []);

  return { cellRender };
};
