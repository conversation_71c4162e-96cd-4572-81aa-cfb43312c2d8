import { divide } from 'number-precision';
import {
  FieldFormat,
  CustomFormat,
} from '../../types';

export type Format = (v: number | string) => (string | number | undefined | null);

export const isVoid = (v: number | string | null | undefined) => {
  if (typeof v === 'undefined' || v === null) return true;
  return false;
};

export const formatMoney = (money?: string | number | undefined): string => {
  if (!money) {
    return '0.00';
  }
  money = divide(Number(money), 100);
  if (isNaN(money)) {
    return '0.00';
  }
  money = money.toFixed(2);
  return money.replace(/(?=(\B\d{3})+\.\d{2}$)/g, ',');
};

export const formatMap: {
  [k: string]: Format,
} = {
  currency: (value: number) => formatMoney(value),
  percent: (value: number | string) => {
    if (value === 0 || isVoid(value) || !['string', 'number'].includes(typeof value)) {
      return '0.00%';
    } else if (value === 'null') {
      return '--';
    } else {
      return `${value}%`;
    }
  },
  number: (value: number) => (Number(value) || 0).toLocaleString(),
  'timestamp[date]': (value: number) => {
    const d = new Date(value);
    const year = d.getFullYear();
    const month = d.getMonth();
    const date = d.getDate();
    return `${year}/${String(100 + month + 1).slice(1)}/${String(100 + date).slice(1)}`;
  },
  'timestamp[time]': (value: number) => {
    const d = new Date(value);
    const year = d.getFullYear();
    const month = String(100 + d.getMonth() + 1).slice(1);
    const date = String(100 + d.getDate()).slice(1);
    const hour = String(100 + d.getHours()).slice(1);
    const min = String(100 + d.getMinutes()).slice(1);
    const sec = String(100 + d.getSeconds()).slice(1);
    return `${year}/${month}/${date} ${hour}:${min}:${sec}`;
  },
};

export default function format(fieldFormat: FieldFormat | CustomFormat | undefined, v: number| string) {
  if (!fieldFormat) return v;
  if (typeof fieldFormat === 'function') {
    return fieldFormat(v) as string | number;
  }
  const formatter = formatMap[fieldFormat];
  if (!formatter) return v;
  return formatter(v);
}
