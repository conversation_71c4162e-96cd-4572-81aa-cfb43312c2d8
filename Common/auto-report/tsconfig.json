{
  "extends": "../common-configs/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "typeRoots": ["./typings/*", "node_modules/@types"],
    "resolveJsonModule": true,
    "target": "es5",
    "rootDirs": ["src", "typings"],
    "module": "esnext",
    "lib": ["es2017", "dom", "es2017.object", "es2015"],
    "types": ["prop-types", "react", "node", "jest"],
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "@mtfe/next-biz/es/*": ["../next-biz/src/*"],
      "@mtfe/next-biz/src/*": ["../next-biz/src/*"],
      "@mtfe/next-router": ["../next-router/src/index"],
      "@mtfe/next-router-v2": ["../next-router-v2/src/index"],
      "@typings/*": ["../../typings/*"],
  
      "@rms-report/typings/*": ["../../typings"],
      "@rms-report/utils/*": ["../projects/rms-report/src/utils/*"],
      "@rms-report/services/*": ["../projects/rms-report/src/services/*"],
      "@rms-report/components/*": ["../projects/rms-report/src/components/*"],
      "@rms-report/consts/*": ["../projects/rms-report/src/consts/*"],
      "@rms-report/assets/*": ["../projects/rms-report/src/assets/*"],
      "@mtfe/auto-report-v2/*": ["../auto-report-v2/src/*"],
    }
  },
  "include": ["./src"],
  "exclude": ["*.test.js", "*.test.jsx"]
}
