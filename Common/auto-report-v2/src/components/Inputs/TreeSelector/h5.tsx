import { clone, cloneDeep } from 'lodash';
import React from 'react';
import { TreeSelector } from '@mtfe/sjst-ui-next';
import withAdapter from '../../../core/withAdapter';
import { TreeSelectorProps } from './types';

const _TreeSelector = (props: TreeSelectorProps) => {
  const {
    multiple = true,
    label,
    placeholder,
    options,
    updateStatus,
    value,
    onChange,
    min,
  } = props;
  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return React.useMemo(() => (
    <TreeSelector
      title={label}
      min={min}
      placeholder={placeholder}
      options={options}
      single={!multiple}
      value={cloneDeep(value)}
      onOk={(v) => {
        onChange?.(clone(v));
      }}
    />
  ), [label, min, placeholder, options, multiple, value, onChange]);
};

export default withAdapter(_TreeSelector, { name: 'TreeSelector' });
