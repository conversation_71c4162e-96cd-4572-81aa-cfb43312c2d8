import {
  buildDownloadApi, buildGetApi, buildPostApi,
} from './buildApi';

// 在线支付结算表导出
export const downloadPaySettle = buildDownloadApi(
  '/api/v1/admin/reports/pays/settle-report',
);

// 在线支付结算明细表导出
export const downloadSettleDetail = buildDownloadApi(
  '/api/v1/admin/reports/pays/settle-detail',
);

// 在线支付明细表导出
export const downloadPayBill = buildDownloadApi(
  '/api/v1/admin/reports/pays/pay-bill',
);

// 对账记录表导出
export const downloadBillingReconList = buildDownloadApi(
  '/api/v1/billing-reconciliation/list-export',
);

// 对账明细表导出
export const downloadBillingReconDetail = buildDownloadApi(
  '/api/v1/billing-reconciliation/detail-export',
);

// 美团团购券核销明细表导出
export const downloadCouponReport = buildDownloadApi(
  '/api/v1/admin/reports/pays/coupon-report',
);

// 结账方式明细表导出
export const downloadCheckoutList = buildDownloadApi(
  '/api/v1/admin/orders/checkout/list/download',
);

export const downloadwmSettleDetail = buildDownloadApi('/api/v1/billing-reconciliation/wm-settle-detail-list-export');
export const downloadwmSettleList = buildDownloadApi('/api/v1/billing-reconciliation/wm-settle-list-export');

export const downloadOrdersCheckoutList = buildDownloadApi('/api/v1/admin/orders/list/download');

export const excelTasks = buildGetApi('/api/v1/excel/taskes');
export const excelGetExport = buildGetApi('/api/v1/excel/export/get');
export const excelPostExport = buildPostApi('/api/v1/excel/export/post');
export const excelOldPostExport = buildPostApi('/api/v1/excel/export');

// 时段开台分析-导出接口，（导出特殊，单独开了个接口）
export const excelOpenTable = buildPostApi('/api/v1/reports/hourly-open-table-report/export');
export const excelExportTaskDelete = buildPostApi('/api/v1/excel/export/task/delete');
export const excelExportTaskRestart = buildPostApi('/api/v1/excel/export/task/restart');
