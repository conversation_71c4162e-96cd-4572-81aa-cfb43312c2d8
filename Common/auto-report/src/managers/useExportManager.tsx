/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import Net from '@mtfe/next-biz/src/lib/Net';
import moment from 'moment';
import {
  ReportProps,
  Props,
  RequestManager,
  FilterManager,
} from '../types';

export function filterS3path(name: string) {
  return name.match(/([\u4e00-\u9fa5]|[a-z]|\d|_|-|\.)+/ig)?.join('_') || ''; // 这里只匹配中文、字母、数字、_、-、.
}

export default function useExportManager(props: Props<ReportProps>, requestManager: RequestManager, filterManager: FilterManager) {
  const [extraExportParams, updateExportParams] = React.useState<any | null>(null);
  const [exportable, updateExportable] = React.useState(false);
  const { query, rootNode } = requestManager;
  const { getQueryInfo } = filterManager;
  let { title = '' } = props;
  // 过滤不支持的字符
  // @ts-ignore
  title = title.replace(/(\\|\/|\?|\*|\[|\])/g, '');

  const getExportUrl = React.useCallback(async () => {
    if (!rootNode?.items?.length) return;
    const { loginName, login } = query;
    const format = 'YYYY-MM-DD HH:mm';
    const time = moment().format(`${format}:ss`);
    let params = {
      poiName: loginName,
      reportName: filterS3path(`${loginName}_${title}_${time}_${login}.xlsx`),
      reportTitle: title,
      loginAccount: login,
      applyContent: `${title} (查询时间范围：${moment(query.startDate).format(format)}-${moment(query.endDate).format(format)})`,
      reportParams: {
        ...query,
      },
      headerDes: [
        title,
        getQueryInfo(),
      ],
      ...extraExportParams,
    };
    if (props.transformExportParams) {
      params = props.transformExportParams(params);
    }
    const r = await Net.post('/api/v1/excel/nest/report/download', params, {
      timeout: 60 * 1000, // 超时时间设成60s
    }) as {
      url: string,
      isOffLineDownloading: boolean,
      totalCount: number,
    };

    if (r && r.url) {
      return r.url;
    } else if (r && r.isOffLineDownloading) {
      return r;
    }
  }, [query, rootNode, title, extraExportParams, getQueryInfo]);

  return {
    extraExportParams,
    title,
    exportable,
    updateExportable,
    getExportUrl,
    updateExportParams,
  };
}
