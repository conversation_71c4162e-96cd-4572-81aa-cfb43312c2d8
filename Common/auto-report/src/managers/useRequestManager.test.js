
const React = require('react');
const { renderHook, act } = require('@testing-library/react-hooks');

describe('useRequestManager', () => {
  // let useRequestManager;
  // const getClearingTimeByRange = jest.fn();
  // const post = jest.fn();
  // beforeAll(() => {
  //   jest.mock('@mtfe/next-biz/src/lib/Net', {
  //     post,
  //   });
  //   jest.mock('@mtfe/next-biz/src/services/poi', {
  //     getClearingTimeByRange,
  //   });
  //   useRequestManager = require('./useRequestManager').default;
  // });

  // afterEach(() => {
  //   getClearingTimeByRange.mockClear();
  //   post.mockClear();
  // });

  it('在请求发起之前会换算结算时间', () => {
  });

  it('请求数据过程中会设置 loading 状态', () => {

  });

  it('rootNode 为返回的数据结果', () => {

  });

  it('请求异常时会设置 error', () => {

  });
});
