import React, { ReactNode } from 'react';
import { withAdapter } from '../../../core/withAdapter';
import { ComponentProps } from '../../../types';
import './pos.less';

interface Props extends ComponentProps {
  header: ReactNode;
  notice: ReactNode;
  tab: ReactNode;
  tooltip: ReactNode;
  filter: ReactNode;
  body: ReactNode;
}

const PageComponents: (keyof Props)[] = [
  'tab',
  'notice',
  'tooltip',
  'filter',
  'header',
  'body',
];

/**
 * 页面顶层容器
 * @param props
 * @returns
 */
function PageContainer(props: Props) {
  return (
    <div className="auto2-root-container">
      {PageComponents.map((name) => (
        <div key={name} className={`auto2-page-slot_${name}`}>
          {props.baseRefObject?.renderManager?.render?.(props[name])}
        </div>
      ))}
    </div>
  );
}

// @ts-ignore
PageContainer.auto = {
  childrenIsConfig: false,
};

export default withAdapter(PageContainer, {
  name: 'PageContainer',
  isComponentRender: false,
});
