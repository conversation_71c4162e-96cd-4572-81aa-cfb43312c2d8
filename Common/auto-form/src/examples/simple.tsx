/* eslint-disable */

import React from 'react';
import { createAutoForm } from '../../';
import {
  submitData, getData, queryOptions,
  queryCity, FormValues, gender, DataPool, Option,
} from './api';
import { Refresh } from '../templates/refresh';


const cc = createAutoForm({
  properties: {
    timeRange: {
      label: '测试时间范围选择器',
      required: true,
      material: {
        name: 'time-range-picker'
      },
    },
    name1: {
      label: '',
      required: true,
      extra: '补充说明文案',
      checkers: (v, cb) => {
        if (v?.length > 2) {
          cb('最长2个字');
          return;
        }
        cb();
      },
      material: {
        name: 'input',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
  },
  onSubmit: async () => {

  },
});

const handle = createAutoForm<DataPool, FormValues>({
  // @ts-ignore
  onInit: async (c) => {
    const [r, r2, r3] = await Promise.all([getData(), queryOptions(), queryCity()]);
    c.dataPool.options = r2 as Option[];
    c.dataPool.cities = r3 as DataPool['cities'];
    return r;
  },
  properties: {
    newFormRef: {
      label: '嵌套表123单',
      material: () => {
        const A = cc.AutoForm;
        return <A />
      }
    },
    name: {
      label: '姓名',
      required: true,
      extra: '补充说明文案',
      checkers: (v, cb) => {
        if (v?.length > 2) {
          cb('最长2个字');
          return;
        }
        cb();
      },
      material: {
        name: 'input',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    gender: {
      label: (ctx, options) => `${options?.getFieldValue('name')}的性别`,
      required: true,
      material: {
        name: 'radio',
        dataSource: gender,
      },
      ifUpdate: ['name'],
      wrapper: (i, dom) => dom,
    },
    customMatrial: {
      label: '自定义物料',
      help: '用问号解释这个字段的作用',
      material: () => <input />,
      slot: (dom) => (
        <div style={{ textAlign: 'center' }}>
          前面自定义插入内容
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>左边插入内容</div>
            {dom}
            <div>右边插入内容</div>
          </div>
          后面自定义插入内容
        </div>
      )
    },
    time: {
      label: '时间',
      required: true,
      material: {
        name: 'time-picker'
      },
      wrapper: (i, dom) => {
        return i.getFieldValue('name').length > 1 ? dom : null;
      },
      ifUpdate: ['name']
    },
    timeRange: {
      label: '时间范围',
      required: true,
      material: {
        name: 'time-range-picker'
      },
    },
    date: {
      label: '日期',
      required: true,
      material: {
        name: 'date-picker'
      },
    },
    dateRange: {
      label: '日期范围',
      required: true,
      material: {
        name: 'date-range-picker'
      },
    },
    week: {
      label: '周选择器',
      required: true,
      material: {
        name: 'week-picker'
      },
    },
    month: {
      label: '月份',
      required: true,
      material: {
        name: 'month-picker'
      },
    },
   
    cities: (ctx) => ({
      label: '城市',
      required: true,
      material: {
        name: 'cascader',
        dataSource: ctx.dataPool.cities,
      },
    }),
    status: {
      label: '状态',
      required: true,
      material: {
        name: 'switch',
      },
    },
    group: (ctx) => ({
      label: '分组',
      material: {
        name: 'checkbox',
        dataSource: ctx.dataPool.options,
      }
    }),
  },
  onSubmit: async (v) => {
    console.log('提交数据,', v);
    const r = await submitData();
    console.log('提交结果,', r);
  },
  template: (doms, { submit }) => {
    // 这里template返回的自定义内容在form内侧
    return (
      <>
        {Object.values(doms)}
        <Refresh ifUpdate={['name']}>
          {(r) => {
            return r.getFieldValue('name')?.length ? 12312321 : null;
          }}
        </Refresh>
        <button onClick={submit}>提交</button>
      </>
    );
  },
  container: (form) => {
    // 这里包裹的div在Form组件外侧
    return (
      <div>
        {form}
      </div>
    )
  }
});

export const Simple = handle.AutoForm;

