import {
  Filter,
  DefaultFilter,
  InputType,
} from '../../../types/filter';

export default class implements InputType {
  config: DefaultFilter;

  constructor(filter: Filter) {
    this.config = filter as DefaultFilter;
  }

  isVisiable() {
    return false;
  }

  render() {
    return null;
  }

  getDefaultValue() {
    return this.config.defaultValue;
  }

  getValue() {
    return this.getDefaultValue();
  }

  getDisplayValue() {
    return null;
  }
}
