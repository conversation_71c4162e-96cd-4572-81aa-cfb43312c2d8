import React from 'react';
import {
  TitleProps,
  Props,
} from '../../../types';
import ReportComponent from '../../ReportComponent';
import { registorComponent } from '../registor';
import './Title.less';

export default function Title(props: Props<TitleProps>) {
  const { children } = props;
  const { hr, decoration, size } = props;

  const dom = React.useMemo(() => {
    if (!children) return null;
    let firstChild: React.ReactNode;
    let resetChild: React.ReactNode;
    if (Array.isArray(children)) {
      [firstChild, ...resetChild] = children;
    } else {
      firstChild = children;
    }
    if (typeof firstChild === 'string') {
      const _children = [
        <span key={1}>{firstChild}</span>,
        <ReportComponent key={2}>{resetChild}</ReportComponent>,
      ];
      return <>{_children}</>;
    } else {
      return <ReportComponent>{children}</ReportComponent>;
    }
  }, [children]);

  const className = React.useMemo(() => {
    const cl = ['auto-report-title'];
    if (hr) cl.push('hr');
    if (decoration) cl.push('decoration');
    const _size = size || 'medium';
    cl.push(_size);
    return cl.join(' ');
  }, [hr, decoration, size]);

  return (
    <div className={className}>
      {dom}
    </div>
  );
}

registorComponent('Title', Title);
