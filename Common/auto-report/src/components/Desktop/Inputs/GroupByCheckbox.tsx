/* eslint-disable @typescript-eslint/no-explicit-any */
import { isEqual } from 'lodash';
import React from 'react';
import {
  Checkbox,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import {
  GroupByCheckboxFilter,
  Filter,
  InputType,
  FilterProps,
} from '../../../types/filter';

export type Value = number | string | Array<string | number>;

export default class implements InputType {
  config: GroupByCheckboxFilter;

  constructor(filter: Filter) {
    this.config = filter as GroupByCheckboxFilter;
  }

  showLabel() {
    return false;
  }

  getDefaultValue() {
    if ('defaultValue' in this.config) {
      return this.config.defaultValue;
    }
    return this.config.unCheckedValue;
  }

  render(props: FilterProps) {
    const { otherProps } = props;

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const onChange = React.useCallback((e: CheckboxChangeEvent) => {
      const _onChange = props.otherProps?.onChange;
      if (!_onChange) return;
      const { value, unCheckedValue } = this.config;
      if (e.target.checked) {
        _onChange(value);
      } else if (typeof unCheckedValue !== 'undefined') {
        _onChange(unCheckedValue);
      } else {
        _onChange(undefined);
      }
    }, [props.otherProps.onChange]);

    const checked = React.useMemo(() => {
      const value = otherProps.value || [];
      return isEqual(value, this.config.value);
    }, [otherProps.value, this.config.value]);

    return (
      <Checkbox size={props.size} {...otherProps} checked={checked} onChange={onChange}>
        {this.config.label}
      </Checkbox>
    );
  }

  getDisplayValue(value: Value) {
    const isTrue = () => {
      if (Array.isArray(this.config.value) && Array.isArray(value) && this.config.value.length === value.length) {
        return this.config.value.every(v => value.includes(v));
      }
      return value === this.config.value;
    };
    return isTrue() ? '是' : '否';
  }

  getQuery(value: Value | undefined, result: { groupBy: Value[]}) {
    if (!value) return;
    if (!Array.isArray(value)) {
      value = [value];
    }
    let groupBy = result.groupBy ? result.groupBy.slice() : [];
    groupBy = groupBy.concat(value);
    return { groupBy };
  }
}
