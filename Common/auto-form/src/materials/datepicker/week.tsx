import React from 'react';
import {
  DatePicker,
} from 'antd';
import { WeekPickerProps } from 'antd/es/date-picker/interface'
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type WeekPickerMaterialProps = {
  name: 'week-picker',
  props?: WeekPickerProps,
};

type Value = number | undefined;

type Props = WeekPickerMaterialProps & ValueControl<Value>;

const { WeekPicker } = DatePicker;

export const WeekPickerMaterial = (p: Props) => {
  const { value, onChange, name, props, ...otherProps } = p;
  return (
    <WeekPicker
      {...otherProps}
      {...props}
      value={number2moment(value)}
      onChange={(e) => {
        onChange?.(moment2number(e));
      }}
    />
  )
};