import { EnvType } from '../contexts';

export type PipelineFuncOptionsType = { env?: EnvType } | any;

export type PipelineFuncType<DataType, OptionsType> = (
  data: DataType,
  options?: OptionsType
) => Promise<DataType> | DataType;

export type PipelineType<DataType, OptionsType> =
  | string
  | { name: string; options?: PipelineFuncOptionsType }
  | PipelineFuncType<DataType, OptionsType>;

export class PipelineManager<DataType> {
  private _funcs = new Map<string, PipelineFuncType<DataType, any>>();

  add(name: string, func: PipelineFuncType<DataType, any>) {
    this._funcs.set(name, func);
  }

  async run(
    pipelines: Array<PipelineType<DataType, any>>,
    data: DataType,
    options: any
  ) {
    for (let pipeline of pipelines) {
      data = await this._run(pipeline, data, options);
    }
    return data;
  }

  private async _run(
    pipeline: PipelineType<DataType, any>,
    data: DataType,
    options: any
  ) {
    if (typeof pipeline === 'string') {
      return this._funcs.get(pipeline)?.(data, options) || data;
    }
    if (typeof pipeline === 'object') {
      return (
        this._funcs.get(pipeline.name)?.(data, {
          ...options,
          ...pipeline.options,
        }) || data
      );
    }
    if (typeof pipeline === 'function') {
      return pipeline(data, options);
    }
    throw new Error('pipeline type error!');
  }
}

export default PipelineManager;
