import React from 'react';
import {
  Cascader,
} from 'antd';
import { CascaderProps } from 'antd/es/cascader';
import { Omit } from 'utility-types';
import { ValueControl, DataSource } from '../types';

export type CascaderMaterialProps = {
  name: 'cascader',
  props?: Omit<CascaderProps, 'options'>,
  dataSource: DataSource<CascaderProps['options'], string | undefined>
};

type Props = CascaderMaterialProps & ValueControl<CascaderProps['value']>

export const CascaderMaterial = (p: Props) => {
  const {
    name, value, onChange,
    props, dataSource, ...otherProps
  } = p;
  return (
    <Cascader
      {...otherProps}
      {...props}
      options={dataSource}
      value={value}
      onChange={onChange}
    />
  );
};
