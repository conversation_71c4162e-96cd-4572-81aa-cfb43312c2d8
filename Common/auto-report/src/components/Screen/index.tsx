import React from 'react';
import './Display/Chart';
import '../Desktop/Layout';
import Inputs from './Inputs';
import './Block';
import './Header';
import './Widget';
import './ReportFilter';
import Report from '../Report';
import { registorComponent, components } from './registor';
import { components as desktopComponents } from '../Desktop/registor';
import {
  componentsContext, inputSetContext, stateContext, StateContextDispatchType,
} from '../../types/context';
import { Config } from '../../types/config';
import ReportComponent from '../ReportComponent';

registorComponent('Report', Report);

const reducer = (state: IV, action: StateContextDispatchType) => {
  switch (action.type) {
    case 'update':
      return {
        ...state,
        ...action.payload,
      };
    default:
      return state;
  }
};

export default function ScreenComponentsPreset(props: {
  state?: IV,
  children: Config[] | Config | React.ReactNode | React.ReactNode[],
}) {
  const InputSetContext = inputSetContext;
  const ComponentContext = componentsContext;
  const StateContext = stateContext;
  const [state, dispatch] = React.useReducer(reducer, {});

  React.useEffect(() => {
    if (props.state) {
      dispatch({ type: 'update', payload: props.state });
    }
  }, [props.state]);

  return (
    <InputSetContext.Provider value={Inputs}>
      <ComponentContext.Provider value={({ ...desktopComponents, ...components })}>
        <StateContext.Provider value={{ state, dispatch }}>
          <ReportComponent>{props.children}</ReportComponent>
        </StateContext.Provider>
      </ComponentContext.Provider>
    </InputSetContext.Provider>
  );
}
