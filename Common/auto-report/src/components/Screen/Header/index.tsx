import React from 'react';
import { registorComponent } from '../registor';
import { ScreenHeaderProps, Props } from '../../../types/config';
import Widget1 from '../Widget/Widget1';
import './index.less';

export default function ScreenHeader(props: Props<ScreenHeaderProps>) {
  return (
    <div className="auto-report-screen-header">
      <svg>
        <defs>
          <linearGradient x1="48.9314823%" y1="100%" x2="48.9314823%" y2="0%" id="screen-header-bg-liner1">
            <stop stopColor="#FFFFFF" stopOpacity="0.02" offset="0%" />
            <stop stopColor="#FFFFFF" stopOpacity="0.1" offset="100%" />
          </linearGradient>
          <path id="screen-header-widget-3" d="M1.21395185,0.5 L29.2679819,28.3310236 L148.792893,28.3310236 L139.964184,19.5023148 L71.8244879,19.5023148 L52.822173,0.5 L1.21395185,0.5 Z" strokeOpacity="0.2" stroke="#51BCFD" fillOpacity="0.06" fill="#FFFFFF" />
          <linearGradient id="screen-header-bg-liner2">
            <stop stopColor="#C7DFFF" stopOpacity="0.1" offset="0%" />
            <stop stopColor="#A3D4FE" stopOpacity="0.32" offset="100%" />
          </linearGradient>
          <polyline id="screen-header-widget-4" points="0.5 0.5 210.92 0.5 297.35 80.5" fill="none" strokeLinecap="round" strokeLinejoin="round" stroke="url(#screen-header-bg-liner2)" />
        </defs>
      </svg>
      <div className="highlight" />
      {/* <div className="bg2">
        <svg viewBox="0 0 10 10">
          <polyline points="0 0, 10 10, 10, 0" fill="url(#screen-header-bg-liner1)" />
        </svg>
        <div />
        <svg viewBox="0 0 10 10">
          <polyline points="10 0, 0 10, 0, 0" fill="url(#screen-header-bg-liner1)" />
        </svg>
      </div>
      <div className="bg1">
        <svg viewBox="0 0 10 10">
          <polyline points="0 0, 10 10, 10, 0" fill="url(#screen-header-bg-liner1)" />
        </svg>
        <div>{props.title}</div>
        <svg viewBox="0 0 10 10">
          <polyline points="10 0, 0 10, 0, 0" fill="url(#screen-header-bg-liner1)" />
        </svg>
      </div> */}
      <div className="bg">
        <div className="left1" />
        <div className="left2" />
        <div className="left3" />
        <div className="center">
          {props.title}
        </div>
        <div className="right3" />
        <div className="right2" />
        <div className="right1" />
      </div>
      <Widget1 className="widget-1" />
      <Widget1 className="widget-2" />
      <svg className="widget-3" viewBox="0 0 147 31">
        <use xlinkHref="#screen-header-widget-3" />
      </svg>
      <svg className="widget-4" viewBox="0 0 147 31">
        <use xlinkHref="#screen-header-widget-3" />
      </svg>
      <svg className="widget-5" viewBox="0 0 297.85 81">
        <use xlinkHref="#screen-header-widget-4" />
      </svg>
      <svg className="widget-6" viewBox="0 0 297.85 81">
        <use xlinkHref="#screen-header-widget-4" />
      </svg>
      <div className="widget-7" />
    </div>
  );
}

registorComponent('ScreenHeader', ScreenHeader);
