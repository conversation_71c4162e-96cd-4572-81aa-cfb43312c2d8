/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Input,
} from 'antd';
import {
  Filter,
  StringFilter,
  InputType,
  FilterProps,
} from '../../../types/filter';

export default class implements InputType {
  config: StringFilter;

  constructor(filter: Filter) {
    this.config = filter as StringFilter;
  }

  render(props: FilterProps) {
    const { config } = this;
    const { onChange } = props.otherProps;
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const _onChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target?.value);
    }, [onChange]);

    return <Input size={props.size} {...props.otherProps} {...config} onChange={_onChange} />;
  }
}
