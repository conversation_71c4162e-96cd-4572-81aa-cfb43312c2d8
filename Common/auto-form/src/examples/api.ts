/* eslint-disable */
import { CascaderProps } from 'antd/es/cascader';

export type FormValues = {
  name: string;
  gender: string;
  status: boolean;
  cities: string[];
}
export type DataPool = {
  options: Option[];
  dateOptions: Option[];
  cities: CascaderProps['options'];
}

export type Option = { name: number | string, value: number };

export const queryCity = () => new Promise(r => {
  setTimeout(() => {
    r([
      {
        value: 'zhejiang',
        label: 'Zhejiang',
        children: [
          {
            value: 'hangzhou',
            label: 'Hangzhou',
            children: [
              {
                value: 'xihu',
                label: 'West Lake',
              },
            ],
          },
        ],
      },
      {
        value: 'jiangsu',
        label: 'Jiangsu',
        children: [
          {
            value: 'nanjing',
            label: 'Nanjing',
            children: [
              {
                value: 'zhonghuamen',
                label: 'Zhong Hua Men',
              },
            ],
          },
        ],
      },
    ]);
  }, 2000)
})

// 拉去配置项数据
export const queryOptions = () => new Promise<Option[]>(r => {
  setTimeout(() => {
    r([
      { value: 11, name: 11 },
      { value: 12, name: 12 },
      { value: 13, name: 13 }
    ] as Option[])
  }, 2000)
});

// 初始化获取数据
export const getData = () => new Promise<FormValues>(r => {
  setTimeout(() => {
    r({
      name: '名称',
      gender: '男',
      cities: ['zhejiang', 'hangzhou', 'xihu'],
      status: true,
    })
  }, 2000)
});

// 提交数据接口
export const submitData = () => new Promise<true>(r => {
  setTimeout(() => {
    r(true)
  }, 2000)
});

export const gender = [
  { name: '男', value: '男' },
  { name: '女', value: '女' },
];