/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Navigator, confirm, platform } from '@mtfe/sjst-ui-next';
import { knbScreenOrientation } from '@mtfe/sjst-ui-next/components/common/knb';
import { withAdapter } from '../../../core/withAdapter';
import { ComponentProps } from '../../../types';
import { DescriptionProps } from '../Description/types';
import { getTableExplain, ExplainProps } from '../Description/helper';
import { pubFixedTableRef } from '../Table/h5';

interface Props extends ComponentProps {
  title?: string;
  children?: any;
}

function PageHeader(props: Props) {
  const { title, children, onChange, ...otherProps } = props;

  const [landscape, setLandscape] = React.useState(false);
  const supportLandscape = React.useMemo(() => {
    return children?.filter((tool: { type: string; props: any }) => tool.props.config.type === 'FullScreen').length > 0;
  }, [children]);

  const { tools, descriptionConfig } = React.useMemo(() => {
    let _descriptionConfig: { props?: DescriptionProps } = {};
    const _tools = children?.filter((tool: { type: string; props: any }) => {
      if (tool.props.config.type === 'Description') {
        _descriptionConfig = tool.props.config;
        return false;
      } else {
        return true;
      }
    });
    return {
      descriptionConfig: _descriptionConfig,
      tools: _tools,
    };
  }, [children]);

  const onTitleClick = React.useCallback(async() => {
    const tableExplainKey = descriptionConfig.props?.tableExplainKey;
    let tableExplain: ExplainProps = { title: '', content: '' };
    if(tableExplainKey) {
      const data = await getTableExplain();
      tableExplain = data?.[tableExplainKey];
    }
    confirm({
      content: (
        <div
          dangerouslySetInnerHTML={{
            __html: tableExplainKey ? tableExplain?.content : (descriptionConfig.props?.content || '') as string,
          }}
        />
      ),
      confirmText: '我知道了',
      cancelText: '',
    });
  }, [descriptionConfig]);

  const resetScreenLandscape = () => {
    requestAnimationFrame(() => {
      pubFixedTableRef?.autoRowHeight();
      pubFixedTableRef?.iscroll?.scrollTo(0, 0);
      pubFixedTableRef?.setState({
        top: 0,
        left: 0,
      });
    });
  }

  const toggleScreenOrientation = React.useCallback(() => {
    knbScreenOrientation(!landscape ? 'landscape' : 'portrait', () => {
      setLandscape(!landscape);
      onChange?.({ landscape: !landscape });
      resetScreenLandscape();
    });
    // 方便本地调试
    if (!platform.isSupportKNB) {
      setLandscape(!landscape);
      onChange?.({ landscape: !landscape });
      resetScreenLandscape();
    }
  }, [landscape])

  const tipsShow = Object.keys(descriptionConfig).length; //是否展示标题 提示

  let _title = title || '';
  if (tipsShow) {
    _title = `${_title} ⓘ`;
  }

  return (
    <div className="auto2-page-header">
      <Navigator
        title={_title}
        onTitleClick={tipsShow ? onTitleClick : undefined}
        rightButtonContent={
          supportLandscape
            ? { text: landscape ? '退出全屏' : '全屏' }
            : undefined
        }
        onRightButtonClick={
          supportLandscape ? toggleScreenOrientation : undefined
        }
        {...otherProps} 
        />
      <div className="auto2-header-toolbar">{tools}</div>
    </div>
  );
}

export default withAdapter(PageHeader, { name: 'PageHeader' });
