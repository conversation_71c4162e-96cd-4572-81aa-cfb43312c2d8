import React from 'react';
import {
  Button,
} from 'antd';
import QueryContainerContext from './context';
import './QueryContainer.less';
import { registorComponent } from '../registor';
import { QueryContainerProps } from './types';
import {
  Props,
} from '../../../types';
import ReportComponent from '../../ReportComponent';
import ReportHeader from '../ReportHeader';

const MAX_QUERY_ITEM = {
  Max: 4,
};
export default function QueryContainer(props: Props<QueryContainerProps>) {
  const [expand, updateExpand] = React.useState(false);
  const container = React.useRef<HTMLDivElement>(null);
  const overlayer = React.useRef<HTMLDivElement>(null);

  const {
    moduleClick, expandBid, hidden, errors, otherParams, maxQueryCount,
  } = props;

  if (maxQueryCount) {
    MAX_QUERY_ITEM.Max = maxQueryCount;
  }

  const containerContext = React.useMemo(() => ({
    visiable: (index: number) => {
      if (!expand) {
        return index < MAX_QUERY_ITEM.Max;
      } else {
        return true;
      }
    },
  }), [expand]);

  const toggleExpand = React.useCallback(() => {
    moduleClick && moduleClick(expandBid);
    updateExpand(!expand);
  }, [expand, updateExpand, moduleClick, expandBid]);

  if (hidden) return null;

  let expandButton: React.ReactNode = null;
  if (props.count > MAX_QUERY_ITEM.Max) {
    expandButton = <Button size={props.size} onClick={toggleExpand} className="auto-report-query-container-btn-toggle" type="link" icon={expand ? 'up' : 'down'}>{expand ? '收起筛选' : '更多筛选'}</Button>;
  }

  const btns = props.realTimeUpdate ? null : (
    <>
      {expandButton}
      <Button
        icon="search"
        size={props.size}
        onClick={props.onSubmit}
        loading={props.loading}
        type="primary"
        disabled={!!Object.keys(errors || {}).length}
        className="auto-report-query-container-btn"
      >
        查询
      </Button>

      <Button icon="redo" className="auto-report-query-container-btn" size={props.size} onClick={props.onReset}>重置</Button>
    </>
  );

  // 自定义按钮位置
  const customQueryButtons = props.realTimeUpdate ? null : (
    <div className="custom-query-buttons">
      <Button
        icon="search"
        size={props.size}
        onClick={props.onSubmit}
        loading={props.loading}
        type="primary"
        disabled={!!Object.keys(errors || {}).length}
        className="auto-report-query-container-btn"
      >
        查询
      </Button>
      <Button icon="redo" className="auto-report-query-container-btn" size={props.size} onClick={props.onReset}>重置</Button>
      {expandButton}
      <ReportHeader showExportButton={false} description={otherParams?.description} extraButtons={otherParams?.extraButtons} />
    </div>
  );
  return (
    <div className="auto-report-query-container" ref={container}>
      <QueryContainerContext.Provider value={containerContext}>
        <div className="auto-report-query-container-overlayer" ref={overlayer} tabIndex={-1}>
          <ReportComponent>{props.children}</ReportComponent>
          {otherParams?.isPos ? customQueryButtons : btns}
          {props.tip && (
            <div className="auto-report-query-container-tip">
              <span className="clearing-time-display">{props.tip}</span>
              { props.extraTips || null }
            </div>
          )}
        </div>
      </QueryContainerContext.Provider>
    </div>
  );
}

registorComponent('QueryContainer', QueryContainer);
