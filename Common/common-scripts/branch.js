const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const execSyncX = (str) => execSync(str, { cwd: path.resolve(__dirname, '../../'), }).toString();

const getBranches = () => {
  // const branchFromTalosEnv = process.env.AUTODEPLOY_TARGET_REF || execSyncX('echo $AUTODEPLOY_TARGET_REF');
  const branchFromTalosEnv = process.env.AUTODEPLOY_TARGET_REF;

  const branchNameIdx = process.argv.indexOf('--branch') + 1;
  let targetBranch
  if (branchFromTalosEnv) {
    targetBranch = branchFromTalosEnv;
  } else if (branchNameIdx) {
    // from cmd, e.g. --branch release/5.20.10
    targetBranch = process.argv[branchNameIdx];
  }

  const currBranch = process.env.AUTODEPLOY_BRANCH || execSyncX('git symbolic-ref --short HEAD').toString().replace(/\s+/, '');
  // const currBranch = execSyncX('git symbolic-ref --short HEAD').toString().replace(/\s+/, '');
  return { targetBranch, currBranch };
}

function isAHeadOfTargetBranch(tgtBrName, curBrName) {
  if (!tgtBrName) {
    // console.log(`target branch doesn't exist, skip target branch check.\n`);
    console.log(`目标分支不存在, 跳过目标分支检查.\n`);
    return true;
  };
  if (!curBrName) {
    // console.log(`current branch doesn't exist, skip target branch check.\n`);
    console.log(`源分支不存在, 跳过目标分支检查.\n`);
    return true;
  }

  // execSyncX('git remote update');
  // execSyncX('git remote -v');
  execSyncX('git fetch origin');

  const _currBrName = execSyncX('git symbolic-ref --short HEAD').trim();

  console.log(`目标分支: ${tgtBrName}`);
  console.log(`源分支: ${curBrName}\n`);
  
  // execSyncX(`git branch ${tgtBrName} origin/${tgtBrName} `)

  execSyncX(`git checkout ${tgtBrName}`);

  const tgtCmtHash = execSyncX('git rev-parse --short HEAD').trim();
  // console.log(`target branch ${tgtBrName} commit hash, ${tgtCmtHash}`);

  execSyncX(`git checkout ${curBrName}`);

  const cmtHash = execSyncX('git rev-parse --short HEAD').trim();
  // console.log(`commit hash ${cmtHash}`);

  // console.log(`执行命令：git diff ${curBrName}...origin/${tgtBrName} --shortstat\n`);
  // console.log(`执行命令：git diff ${curBrName}...${tgtBrName} --shortstat\n`);
  console.log(`执行命令：git diff ${cmtHash}...${tgtCmtHash} --shortstat\n`);
  
  const diff = execSyncX(`git diff ${cmtHash}...${tgtCmtHash} --shortstat`);
  // const diff = execSyncX(`git diff ${curBrName}...${tgtBrName} --shorstat`);
  diff && console.log(`diff ${diff}\n`);
  if (diff) {
    // const msg = `Fatal: Cannot merge current branch「${curBrName}」to branch「${tgtBrName}」, cause current branch is behind by some commits.\nMerge target branch「${tgtBrName}」and retry.\n`;
    const msg = `错误：不能合并当前分支「${curBrName}」到目标分支「${tgtBrName}」, 因为当前分支落后于目标分支.\n合并目标分支「${tgtBrName}」后重试.\n`;
    console.error(msg);
  } else {
    // const msg = `Current branch is ahead of target branch「${tgtBrName}」.\n`;
    const msg = `当前分支领先于目标分支「${tgtBrName}」.\n`;
    console.log(msg);
  }

  execSyncX(`git checkout ${_currBrName}`);

  return !diff;
}

function isBranchLocked(tgtBrName) {
  if (!tgtBrName) {
    // console.log(`target branch doesn't exist, skip Common module Branch check.`);
    console.log(`目标分支不存在，跳过 Common 模块分支检查.`);
    return true;
  };
  if (tgtBrName !== 'master' && !tgtBrName.includes('release/')) return true;

  const gitModulesFile = fs.readFileSync(path.resolve(__dirname, '../../.gitmodules'), { encoding: 'utf8', });
  // console.log(gitModulesFile);
  let commonBranch = gitModulesFile.slice(gitModulesFile.indexOf('branch =')).split(' = ')[1];
  if (commonBranch) commonBranch = commonBranch.trim();
  console.log(`当前 Common 分支: ${commonBranch}`);

  if (tgtBrName === 'master') {
    const ifPass = commonBranch === 'master';
    // !ifPass && console.error('Fatal: Lock Common module branch to「master」first.\n');
    !ifPass && console.error('错误: 请先将 Common 模块分支指定为「master」.\n');
    return ifPass;
  }
  if (tgtBrName.includes('release/')) {
    const ifPass = commonBranch === 'master' || commonBranch.includes('release/');
    // !ifPass && console.error('Fatal: Lock Common module branch to 「master」or「release/」first.\n');
    !ifPass && console.error('错误: 请先将 Common 模块分支指定为「master」或「release/」.\n');
    return ifPass;
  }
  return true;
}


module.exports = function () {
  const { targetBranch, currBranch } = getBranches();
  
  // try {
  //   if (!isAHeadOfTargetBranch(targetBranch, currBranch)) {
  //     return false;
  //   };
  
  //   if (!isBranchLocked(targetBranch)) {
  //     return false;
  //   }
  // } catch (error) {
  //   console.log(error);
  // }
  
  if (!isAHeadOfTargetBranch(targetBranch, currBranch)) {
    return false;
  };

  if (!isBranchLocked(targetBranch)) {
    return false;
  }
  console.log('分支检查通过');
  return true;
}