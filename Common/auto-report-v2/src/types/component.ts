/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Emitter } from 'mitt';
import { Logger } from 'loglevel';
import { ReportQuery } from './report';
import Analytics from '../helper/analytics';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';

export type ID = string | number;

export type OsType = 'pc' | 'pos' | 'h5' | 'screen';

export interface UserType {
  login: string | number;
  loginName: string;
  isChain: boolean;
}

export type ChildrenComponent<State = {}> =
  | Config<State>
  | React.ReactNode
  | React.ReactElement;

export interface ConfigProps<State = {}> extends ComponentProps {
  [id: string]: any;
  children?: Config<State>;
}

export type ConditionType<State = {}> = {
  os?: OsType; // 系统
  isTaxOpen?: boolean; // 税价开关
  isChain?: boolean; // 是否是总部视角
  query?: ReportQuery; // 关联 query，比如 { businessTypes: 1 }
} & State;

// auto report 配置编译器
export type RuleCompiler<State = {}> = {
  /**
   * 判断条件，这里是取且条件， 如果要实现或条件请使用多个 RuleCompiler
   */
  conditions: ConditionType<State>;
  /**
   * 满足条件，需要合并处理的配置
   */
  merges?: {};
  /**
   * 满足条件，需要移除的配置字段
   */
  excludes?: string[];
  /**
   * 满足条件，删除当配置
   */
  delete?: boolean;
};

export type RuleConfig<State = {}> = {
  autoRules?: RuleCompiler<State>[];
};

export type Config<State = {}> = {
  /** 对应的组件 name */
  type?: string;
  props?: ConfigProps<State> & RuleConfig<State>;
} & RuleConfig<State>;

/** pending 加载中， done 加载完成 */
export type ComponentStatus = 'pending' | 'done';

export interface ObjectRefValueType<ValueType> {
  id: string;
  name: string;
  path: string[];
  dataKey?: string;
  value?: ValueType;
  defaultValue?: ValueType;
  status?: ComponentStatus;
  emitter$: Emitter;
  temporaryValue?: ValueType; // 临时 value
  isDelete?: boolean; // 是否是删除状态，unRegister 不会立即删除对象，而是再其它任务完成之后统一清理
  otherProps?: ConfigProps; // 组件其他 props 的值
}

export interface BaseObjectRefType<ValueType = {}> extends Emitter {
  version?: number;
  id: string;
  name: string;
  path: string[];
  /** 组件唯一标识，主要用于快速定位操作性组件，Report 也会通过 dataKey 来判断子组件是否已经初始化完成, 全局唯一，不能重复 */
  dataKey?: string;
  value?: ValueType;
  defaultValue?: ValueType;
  temporaryValue?: ValueType; // 临时 value
  status?: ComponentStatus;
  otherProps?: ConfigProps; // 组件其他 props 的值
  logger: Logger;
  analytics: Analytics;
  renderManager: RenderManagerType;
  getObjectRef: (control: StoreControlType<ValueType>) => ObjectRefType<ValueType>;
}

export interface ObjectRefType<ValueType = {}> extends BaseObjectRefType<ValueType> {
  children?: ObjectRefType<ValueType>[];
  getRefList: () => ObjectRefValueType<any>[];
  /** 事件触发，type 格式：{事件名称} 或 '{事件名称}{#dataKey}' 或 '{事件名称}{@parentName}' */
  emit<T = any>(type: string, event?: T): void;
  /** 获取 id 对应的 ObjectRef */
  withId(id: string): ObjectRefType<any> | undefined;
  /** 获取 dataKey 对应的 ObjectRef */
  withDataKey(dataKey: string): ObjectRefType<any> | undefined;
  /** 获取 parentName 对应的 ObjectRef */
  withParentName(parentName: string): ObjectRefType<any> | undefined;
  /** 获取所有的子 childrenName 对应的 ObjectRef */
  withChildrenName(childrenName: string): ObjectRefType<any>[];
}

export interface BaseComponentProps<State = {}> {
  children?: ChildrenComponent<State>;
}

export type UpdateStatusValueType<ValueType> =
  | ValueType
  | ((v?: ValueType) => ValueType);

export interface ComponentProps<ValueType = {}> extends BaseComponentProps {
  queryName?: string;
  label?: string;
  isTemporary?: boolean; // 是否是临时组件，临时组件的 value 值变化会有一个临时值来存储
  baseRefObject?: BaseObjectRefType<ValueType>;
  /**
   * 组件内部用来更新状态和值，不会触发 onChange
   */
  updateStatus?: (
    status?: ComponentStatus,
    value?: UpdateStatusValueType<ValueType>
  ) => void;
  dataKey?: string;
  value?: ValueType;
  defaultValue?: ValueType;
  onChange?: (value: ValueType, ref?: ObjectRefType<ValueType>) => void;
  onChangeTemporary?: (value: ValueType, ref?: ObjectRefType<ValueType>) => void;
  /**
   * 联动
   *  - change->{EventType} 内容变更触发 EventType, EventType 参考 ObjectRefType.emit
   */
  relations?: string[];
  /**
   * 不能重置，在重置的时候，如果有不需要重置的则设置该属性为 true
   */
  isCantReset?: boolean;
  // 兼容 auto-base 改造
  $$auto?: any;
  businessModuleId?: BusinessModule
}

export type ComponentMapType = { [name: string]: React.ComponentType & any };

export type RenderManagerComponentType = {
  os?: OsType;
  components?: ComponentMapType,
  lazy?: () => Promise<{ default: ComponentMapType }>;
};

export interface RenderManagerType {
  getComponent(name: string): React.ComponentType & any;
  getLazyComponent(name: string): Promise<React.ComponentType & any>;
  render(
    config?: ChildrenComponent | ChildrenComponent[],
    options?: ComponentProps | any,
    otherProps?: { key: string }
  ): React.ReactNode | React.ReactNode[];
}

/// 环境数据
export type EnvType = {
  os?: OsType;
  user?: UserType | null; // 用户信息
  isTaxOpen?: boolean; // 税价开关
  isChain?: boolean; // 是否是总部视角
};

export type DataControlType<ValueType> = {
  /** 注册组件 */
  register: (objectRef: ObjectRefValueType<ValueType>) => void;
  /** 卸载组件 */
  unRegister: (objectRef: ObjectRefValueType<ValueType>) => void;
  /** 更新组件 */
  update: (objectRef: ObjectRefValueType<ValueType>) => void;
};

/// 组件渲染完之后，不再变化的数据
export type DataType<ValueType = {}> = {
  env?: EnvType;
  control?: DataControlType<ValueType>;
  analytics?: Analytics;
  renderManager?: RenderManagerType;
  businessModuleId?: BusinessModule;
};

/// store 数据处理方法
export type StoreControlType<ValueType> = {
  getRefList: () => ObjectRefValueType<any>[];
  /** 获取 id 对应的 ObjectRef */
  withId: (id: string) => ObjectRefValueType<ValueType> | undefined;
  /** 获取 dataKey 对应的 ObjectRef */
  withDataKey: (dataKey: string) => ObjectRefValueType<ValueType> | undefined;
  /** 获取 parentPath 对应的 Children ObjectRef */
  withByParentPath: (path: string[]) => ObjectRefValueType<ValueType>[];
  /** 获取 parentPath 对应的所有 childrenName 的 Children ObjectRef */
  withByChildrenName: (
    path: string[],
    childrenName: string
  ) => ObjectRefValueType<ValueType>[];
};

export type StoreType = {
  state: ObjectRefValueType<any>[];
  control?: StoreControlType<any>;
};
