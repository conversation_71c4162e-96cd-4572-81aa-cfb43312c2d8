/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
  // Event,
} from '@antv/g2';
import { PieChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart, { Data } from './useBasicChart';
import { Color } from '../../../common/chartConst';
import formatter from '../../../common/format';
import { ReportContext } from '../../../../types';

export default function PieChart(props: Props<PieChartProps>) {
  const {
    height,
    width,
    dim,
    value,
    percent,
    useAbs,
  } = props;
  
  const report = React.useContext(ReportContext);
  const rootNode = report?.requestManager.rootNode;

  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    chart.coordinate('theta', {
      radius: 0.8,
      innerRadius: 0.8,
    });
    chart.axis(false);
    chart.scale({
      [dim.field]: {
        type: dim.format ? undefined : 'category',
        alias: dim.label,
        formatter: v => formatter(dim.format, v),
      },
      [value.field]: {
        alias: value.label,
        formatter: v => formatter(value.format, v),
      },
    });
    chart.tooltip({
      showTitle: false,
      showMarkers: false,
      itemTpl: '<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}</li>',
    });
    chart
      .interval()
      .adjust('stack')
      .position(`${value.field}_abs`)
      .color(dim.field, Color)
      // .label(value.field, v => ({
      //   content: (item) => {
      //     let newValue = formatter(value.format, v);
      //     if (percent) {
      //       newValue = `${newValue}（${formatter(percent.format, item[percent.field])}）`;
      //     }
      //     return `${item[dim.field]}: ${newValue}`;
      //   },
      // }))
      .tooltip(`${dim.field}*${value.field}`, (item, v) => ({
        name: item,
        value: formatter(value.format, v),
      }));
    chart.filter(dim.field, null);

    chart.removeInteraction('legend-filter');
    chart.removeInteraction('legend-active');
    chart.interaction('element-single-selected');
    return chart;
  }, [dim, value, percent]);

  const updateCenterKPI = React.useCallback((chart: Chart) => {
    chart.annotation().clear(true);
    const aggr = rootNode?.aggr || {};
    chart
      .annotation()
      .text({
        position: ['50%', '50%'],
        content: value.label,
        style: {
          fontSize: 14,
          fill: '#000000',
          fillOpacity: 0.45,
          textAlign: 'center',
          stroke: '0',
        },
        offsetY: -20,
      })
      .text({
        position: ['50%', '50%'],
        content: formatter(value.format, aggr && aggr[value.field]) || '',
        style: {
          fontSize: 30,
          fill: '#000000',
          fillOpacity: 0.85,
          textAlign: 'center',
          stroke: '0',
        },
        offsetY: 10,
      });
  }, [rootNode?.aggr, value]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    data.forEach(item => {
      // 存一个绝对值
      const absField = `${value.field}_abs`;
      item[absField] = useAbs ? Math.abs(item[value.field] as number) : item[value.field];
    })
    chart.data(data);
    chart.legend({
      position: 'right',
      itemValue: {
        formatter: (legend: string) => {
          const item = data.find(d => d[dim.field] === legend);
          return item ? formatter(value.format, item[value.field]) : '';
        },
      },
    });
    updateCenterKPI(chart);
  }, [value, updateCenterKPI, dim, useAbs]);

  const dims = React.useMemo(() => [dim.field], [dim]);
  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
    className: 'auto-report-pie-chart',
  });

  return dom;
}

registorComponent('PieChart', PieChart);
