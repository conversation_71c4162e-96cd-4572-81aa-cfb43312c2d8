const React = require('react');
const ReactDom = require('react-dom');
const { act } = require('react-dom/test-utils');
const { renderHook } = require('@testing-library/react-hooks');
const renderer = require('react-test-renderer');
const { default: useFixedHeader } = require('./useFixedHeader');

describe('useFixedHeader', () => {
  let id;
  const Component = () => {
    id = useFixedHeader({ tableId: 12345 });
    return (
      <div id={id}>
        <div className="ant-table" style={{ width: 100, height: 200 }}>
          <div className="ant-table-content" style={{ width: 100, height: 400 }}>
            <table className="table-body">
              <thead></thead>
              <tbody>
                <tr className="total-row"></tr>
              </tbody>
            </table>
            <table className="table-header ant-table-fixed-left">
              <thead></thead>
              <tbody>
                <tr className="total-row"></tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  let container;
  beforeEach(() => {
    container = document.createElement('div');
    document.body.appendChild(container);
    act(() => {
      ReactDom.render(<Component />, container);
    });
  });

  afterEach(() => {
    document.body.removeChild(container);
  });
  it('表格滚动时，会动态设置 footers 的transform', () => {
    // jest 里的 DOM 都是模拟出来的，没法做测试. 
  });
});
