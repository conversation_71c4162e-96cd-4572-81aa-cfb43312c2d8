/**
 * 隔离开发， lint
 */
const { execFile } = require('child_process');
const {resolveNodeModulesBin} = require('../mpack/bin/util');
const path = require('path');
const fs = require('fs');

const checkBranch = require('./branch');

const { tsErrorFilter, dtsDownlevel } = require('./utils');

const projectRoot = path.join(__dirname, '../../');

// Linux 大小写是敏感的，但是 mac 却不是
const getPagesPath = () => {
  if (fs.existsSync('./Pages')) {
    return 'Pages';
  } else {
    return 'pages';
  }
}

 /**
  * 全量lint
  */
async function eslint(startTime) {
  const eslintPath = resolveNodeModulesBin('eslint');
  const eslintRc = path.join(__dirname, '../mpack/.eslintrc.js');
  const start = Date.now();
  const eslintExec =  execFile(eslintPath, [
    '--config',
    eslintRc,
    // '--fix',
    `./${getPagesPath()}/**/*.{ts,tsx}`,
  ], {
    cwd: projectRoot,
  });
  eslintExec.stdout.on('data', (data) => {
    console.log(data);
  });

  eslintExec.stderr.on('data', (data) => {
    console.log(data);
  });

  eslintExec.on('exit', (code) => {
    const end = Date.now();
    let status = '失败';
    if (code === 0) {
      status = '成功';
    }
    console.log('ESLint 完成，耗时：', `${(end - start) / 1000}s`, `${status}`);
    console.log(`================================= Lint完成，共耗时：${(end - startTime) / 1000}s =================================`);
    process.exit(code);
  });
}

/**
  * tsc 检查
  */
function tsc(resolve) {
  const start = Date.now();
  const cwd = path.join(projectRoot, `./${getPagesPath()}`);
  const tscPath = resolveNodeModulesBin('typescript', 'tsc');
  const tsconfigPath = path.resolve(projectRoot, 'tsconfig.json');

  let option = [
    '--noEmit',
    '--skipLibCheck',
    '--project',
    tsconfigPath,
  ]
  if(process.argv.length !== 0){
    if(process.argv.indexOf('--incremental') >= 0){
      option.push('--incremental')
    }
  }

  // 存储错误的日志对象
  let errorObj;

  const tscExe = execFile(tscPath, option, {
    cwd: cwd,
  });

  tscExe.stdout.on('data', (data) => {
    // 错误进程处理
    errorObj = tsErrorFilter(data);

    console.log(errorObj.error);
  });

  tscExe.stderr.on('data', (data) => {
    console.log(data)
  });

  tscExe.on('exit', (code) => {
    const end = Date.now();
    let status = '失败';

    // 【成功】或者【错误状态为成功且无错误日志】均视为成功
    if (code === 0  || (errorObj && errorObj.status && !errorObj.error)) {
      status = '成功';
    }

    console.log('TSC 完成，耗时：', `${(end - start) / 1000}s`, `${status}`);

    // 【成功】或者【错误状态为成功且无错误日志】均视为成功
    if (code === 0 || (errorObj && errorObj.status && !errorObj.error)) {
      resolve(start);
      return;
    }
    process.exit(code);
  });
}

async function lint() {
  // if (!checkBranch()) {
  //   process.exit(-1);
  // };
  // 暂时用环境变量控制，避免出现大量lint问题来不及修复
  if (process.env.ENABLE_DTS_DOWNLEVEL === 'true') {
    dtsDownlevel();    
  }
  new Promise((resolve, reject) => {
  tsc(resolve)
  }).then(startTime => {
  eslint(startTime)
  }).catch(e => {
    console.error(e);
  });
}
 
lint();
 