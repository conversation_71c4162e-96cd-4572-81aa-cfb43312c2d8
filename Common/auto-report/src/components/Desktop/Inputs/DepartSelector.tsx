import React from 'react';
import ReportDepartSelector, { SimplifiedDepart, DepartType } from '@mtfe/next-biz/src/components/Org/ReportDepartSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  DepartSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: DepartSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as DepartSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  getDepartTypes(): DepartType[] {
    return this.config.mode?.map((mode) => {
      switch (mode) {
        case 'production':
          return DepartType.Production;
        case 'administration':
          return DepartType.Administration;
        case 'sales':
          return DepartType.Sales;
        default:
          return null;
      }
    }).filter(Boolean) as DepartType[] || [DepartType.Production];
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    const departTypes = this.getDepartTypes();
    return (
      <ReportDepartSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        options={{ departTypes }}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(depart?: SimplifiedDepart | SimplifiedDepart[]) {
    return toArray(depart)?.map(o => o.name).join('，') || '全部';
  }

  getValue(depart: SimplifiedDepart | SimplifiedDepart[] | undefined) {
    return toArray(depart)?.map(o => o.id);
  }
}
