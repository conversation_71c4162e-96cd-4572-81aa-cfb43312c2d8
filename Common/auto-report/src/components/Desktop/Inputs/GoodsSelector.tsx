import React from 'react';
import ReportGoodsSelector, { SimplifiedGoods, GoodsType } from '@mtfe/next-biz/src/components/Goods/ReportGoodsSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  GoodsSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: GoodsSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as GoodsSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getGoodsType() {
    switch (this.config.goodsType) {
      case 'normal':
        return GoodsType.NormalGoods;
      case 'side':
        return GoodsType.SideGoods;
      case 'box':
        return GoodsType.BoxGoods;
      default:
        return GoodsType.NormalGoods;
    }
  }

  getShowBanquet() {
    return this.config.showBanquet;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    const type = this.getGoodsType();
    const showBanquet = this.getShowBanquet();
    return (
      <ReportGoodsSelector
        size={props.size}
        multiple={!!multiple}
        useMultiplePopSearch
        selectAllAsNull={false}
        placeholder="全部"
        {...otherProps}
        options={{ type, showBanquet }}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(goods?: SimplifiedGoods | SimplifiedGoods[]) {
    return toArray(goods)?.map(o => o.name).join('，') || '全部';
  }

  getValue(goods: SimplifiedGoods | SimplifiedGoods[] | undefined) {
    return toArray(goods)?.map(o => o.id);
  }
}
