/* eslint-disable @typescript-eslint/no-explicit-any */
import { FormItemProps, FormProps, FormInstance } from '@mtfe/rmsform-antd';
import { TooltipProps } from 'antd/es/tooltip';
import { Omit } from 'utility-types';
import { Materials } from './materials';
import { DataContextType } from './cores/dataContext';

export type IV = {
  [i: string]: any;
};

export type FormValues = {
  [i: string]: any;
};

// 配置化上下文，用于通信
export type Context<T extends object> = {
  // 物料渲染需要的数据池，比如select是异步拉取的
  dataPool: T;
};

export type ReadOnlyContext<T extends object> = {
  // 物料渲染需要的数据池，比如select是异步拉取的
  readonly dataPool: T;
};

export type Wrapper = (instance: FormInstance, material: JSX.Element | null) => JSX.Element | null;
export type MaterialOptions = {
  getFieldValue: FormInstance['getFieldValue'];
};

export type FunctionMaterial<D extends IV> = (
  ctx: DataContextType<D>,
  options?: MaterialOptions,
) => JSX.Element | Materials;

export type Material<D extends IV> = FunctionMaterial<D> | Materials;

export type Property<D> = {
  label?: FormItemProps['label'] | FunctionMaterial<D>;
  help?: string | TooltipProps;
  hidden?: boolean | ((ctx: Context<D & {}>, formInstance: FormInstance | null) => boolean);
  required?: FormItemProps['required'];
  extra?: FormItemProps['info'];
  checkers?: FormItemProps['checkers'];
  fieldProps?: Omit<FormItemProps, 'children' | 'name' | 'label' | 'info'>;
  material: Material<D>;
  // 自定义插槽，用于补充自定义节点
  slot?: (dom: JSX.Element | null) => JSX.Element;
  // 包裹函数，用于获取form上的状态做自定义操作
  wrapper?: Wrapper;
  // 用于控制wrapper函数的执行
  ifUpdate?: FormItemProps['ifUpdate'];
};

export type CommonAutoFormProps = AutoFormProps<IV, FormValues>;
type Options<D extends object, F extends object = {}> = {
  ctx: DataContextType<D>;
  initialValue?: F | null;
  submit: () => Promise<void | boolean>;
};

export type AutoFormProps<D extends IV, F extends FormValues> = {
  formProps?: FormProps;
  // 数据初始化时等待文案
  loadingText?: string;
  // 提交时等待文案
  submitText?: string;
  // 表单配置项
  properties: {
    [i: string]: ((ctx: DataContextType<D>) => Property<D>) | Property<D>;
  };
  // 模板引擎 - 用于决定UI的渲染
  template?: (
    doms: {
      [key in keyof F]: JSX.Element;
    },
    options: Options<D, F>,
  ) => JSX.Element;
  // 数据初始化
  onInit?: ((ctx: DataContextType<D>) => Promise<F | undefined>) | ((ctx: DataContextType<D>) => Promise<void>);
  // 数据分发函数
  onDistribute?: (
    v: F,
    options: {
      changedKeys: string[];
      getFieldValue: FormInstance['getFieldValue'];
      setFieldValue: FormInstance['setFieldValue'];
    },
  ) => void;
  // 数据提交
  onSubmit?: (props: F) => Promise<void | boolean>;
  // 容器，自定义组件外部行为
  container?: (form: JSX.Element, options: Options<D>) => JSX.Element;
};

export type AutoFormCompoentProps<D extends IV, F extends FormValues> = {
  value?: F;
  onChange?: (v: F) => void;
  onInitBefore?: (ctx: Context<D>) => Promise<void>;
  forwardedRef?: React.Ref<{}>;
};
