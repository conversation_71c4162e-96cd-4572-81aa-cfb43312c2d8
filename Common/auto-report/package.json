{"name": "@mtfe/auto-report", "version": "1.0.0", "description": "", "main": "src", "scripts": {"test": "jest", "coverage": "jest --coverage && test \"$(uname)\" = \"Darwin\" && open ./coverage/lcov-report/index.html", "lint": "mpack lint 'src/**/*.{ts,tsx}' --fix"}, "author": "", "license": "ISC", "dependencies": {"@antv/data-set": "0.11.8", "@antv/g2": "4.1.6", "@mtfe/next-router-v2": "^2.0.0"}, "devDependencies": {"@testing-library/react-hooks": "^3.4.1", "@types/jest": "^26.0.10", "@types/react-test-renderer": "^16.9.3", "jest": "^26.4.2", "react-test-renderer": "^16.13.1", "@mtfe/mpack": "^1.0.55"}}