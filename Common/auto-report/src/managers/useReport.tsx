import React from 'react';

/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ReportProps,
  ReportContextValue,
  ReportContext,
  Props,
} from '../types';
import useFilterManager from './useFilterManager';
import useRequestManager from './useRequestManager';
import useExportmanager from './useExportManager';
import useBookmarkManager from './useBookmark';
import usePriceTax from './usePriceTax';
import useToolsManager from './useTools';
import useAnalysisManager from './useAnalysis';

// import useRouter from './useRouter';

export default function useReport(props: Props<ReportProps>, haveFilter: boolean): ReportContextValue {
  const parentReport = React.useContext(ReportContext);
  props = {
    title: parentReport?.props.title,
    view: parentReport?.props.view,
    ...props,
  };

  /** 过滤器管理 */
  const filterManager = useFilterManager(props, parentReport?.filterManager, haveFilter);

  /** 请求管理 */
  const requestManager = useRequestManager(props);

  /** 导出管理 */
  const exportManager = useExportmanager(props, requestManager, filterManager);

  // 参数序列化到 URL 的能力暂时有 Bug，暂不开放
  // const routerManager = useRouter(filterManager, parentReport?.routerManager);

  const priceTaxManager = usePriceTax();

  const bookmarkManager = useBookmarkManager(!!parentReport?.bookmarkManager);

  const toolsManager = useToolsManager();

  const analysisManager = useAnalysisManager(
    !!requestManager.rootNode,
    props.cid,
    parentReport?.analysisManager,
    props?.preventPV
  );

  const isSubReport = !!parentReport;

  return React.useMemo(() => ({
    // routerManager,
    inited: filterManager.inited,
    props,
    priceTaxManager,
    exportManager,
    filterManager,
    requestManager,
    bookmarkManager,
    toolsManager,
    analysisManager,
    isSubReport,
    businessModuleId: props.businessModuleId,
    parent: parentReport || undefined,
  }), [
    // routerManager,
    analysisManager,
    props,
    priceTaxManager,
    exportManager,
    filterManager,
    requestManager,
    bookmarkManager,
    toolsManager,
    isSubReport,
    parentReport,
  ]);
}
