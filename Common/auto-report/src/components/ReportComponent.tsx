/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Config,
  ComponentConfig,
  ViewConfig,
  componentsContext,
} from '../types';
import useConfig from '../managers/useConfig';

function isBasicType(element: any): element is (string | number | boolean) {
  switch (typeof element) {
    case 'string':
    case 'number':
    case 'boolean':
      return true;
    default:
      return false;
  }
}

function isReactNode(element: any): element is (React.ReactElement | string | number | boolean) {
  if (!element) return true;
  if ('$$typeof' in element) return true;
  return isBasicType(element);
}

export function ReportChild(child: Config) {
  const props = useConfig<ComponentConfig>(child as { version: string, view: string });
  const components = React.useContext(componentsContext);
  if (!props) {
    const Spin = components.Spin;
    if (!Spin) return null;
    return <Spin />;
  }
  const { type, props: _props } = props;
  const ChildComponent = components[type];
  if (!ChildComponent) {
    console.warn(`配置化报表未找到组件：${type}，组件配置：`, props);
    return null;
  }
  return <ChildComponent {..._props as any} />;
}

export default function ReportComponent(props: {
  children?: Config[] | Config | React.ReactNode | React.ReactNode[],
}): React.ReactElement | null {
  const renderReportComponent = React.useCallback((config: Config | React.ReactNode, index: number) => {
    let key: string;
    if (isBasicType(config) || isReactNode(config)) return config || null;
    if ('view' in (config as Config)) {
      key = `${(config as ViewConfig).view}-${index}`;
    } else {
      key = `${index}`;
    }
    return <ReportChild key={key} {...(config as ComponentConfig)} />;
  }, []);

  let { children } = props;

  if (isBasicType(children)) {
    return <>{children}</>;
  } else if (isReactNode(children)) {
    return children || null;
  }
  if (!Array.isArray(children) && children) {
    children = [children];
  }

  if (!children) return null;

  return (
    <>
      {(children as Array<React.ReactNode | Config>).map(renderReportComponent).filter(Boolean)}
    </>
  );
}
