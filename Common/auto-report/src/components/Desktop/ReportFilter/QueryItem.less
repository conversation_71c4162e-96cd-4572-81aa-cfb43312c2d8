@import '~antd/es/style/themes/default.less';

:root {
  .auto-report-query-item{
    display: flex;
    align-items: center;
    margin-right: 16px;
    margin-bottom: 12px;
    line-height: 37px;

    .auto-report-query-item-label{
      &::after{
        content: '：';
      }
      &.small{
        font-size: 12px;
      }
    }

    .auto-report-query-item-content{
      position: relative;
      // 时间组件
      .ant-RangePickerX, .datepicker {
        line-height: 16px;
        height: 24px;

        .ant-calendar-picker-input{
          height: 16px;
          line-height: 16px;
          padding: 0 4px;
        }
      }
      // 门店组件布局调整
      .ant-select-selector,.saas-select-selector {
        min-height: 24px;
        height: 24px;
        flex-wrap: nowrap;
  
        .ant-select-selection-search {
          margin-top: 0;
        }
        .ant-select-selection-item,.saas-select-selection-item {
          height: 18px;
          line-height: 16px;
        }
      }

      // 选择组件布局调整
      .ant-select-selection {
        min-height: 24px;
        height: 24px;
        
        .ant-select-selection__choice {
          height: 18px;
          line-height: 16px;
        }
      }

      .ant-input, .ant-input-number-input {
        height: 24px;
      }
  
      .ant-select-selection__rendered, .ant-input-number {
        line-height: 24px;
        height: 24px;
      }

      .ant-select-selection--multiple .ant-select-arrow, .ant-select-selection--multiple .ant-select-selection__clear {
        top: 14px;
      }
  
      .ant-select-selection--multiple .ant-select-selection__rendered>ul>li, .ant-select-selection--multiple>ul>li {
        height: 18px;
        line-height: 16px;
      }
  
      .ant-select-search__field__placeholder, .ant-select-selection__placeholder {
        line-height: 20px;
      }

      // tag 布局调整
      .ant-tags {
        display: flex;
        padding-top: 3px;

        .ant-btn {
          height: 24px;
          line-height: 16px;
          padding: 0 5px;
          flex: 1;
        }
      }

      // buttons 布局
      .ant-btn {
        height: 24px;
      }
      // 单选按钮
      .ant-radio-group {
        .ant-radio-button-wrapper {
          height: 24px;
          line-height: 22px;
          padding: 0 5px;
          &-checked {
            border-color: #ff6000 !important;
            color: #ff6000 !important;
          }
        }
      }
    }

    .auto-report-query-item-error{
      position: absolute;
      color: @error-color;
      font-size: 12px;
      white-space: nowrap;
    }
  }

}
