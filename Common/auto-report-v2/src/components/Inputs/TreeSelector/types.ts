import React from 'react';
import { ComponentProps, ID } from '../../../types';

export interface TreeOption {
  title: string;
  value: ID;
  disabled?: boolean;
  children?: TreeOption[];
}

export interface TreeSelectorProps extends ComponentProps<ID[]> {
  className?: string;
  dropdownClassName?: string;
  placeholder?: string;
  multiple?: boolean;
  options: TreeOption[];
  min?: number;
  header?: React.ReactNode;
}
