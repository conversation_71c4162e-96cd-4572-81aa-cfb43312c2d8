import React from 'react';
import {
  Radio,
} from 'antd';
import { RadioProps, RadioGroupProps } from 'antd/es/radio';
import { DataSource, ValueControl } from '../types';

export type RadioMaterialProps = {
  name: 'radio',
  props?: RadioGroupProps,
  itemProps?: RadioProps,
  dataSource: DataSource<RadioGroupProps, RadioGroupProps['value']>
}

type Props = ValueControl<any> & RadioMaterialProps;

export const RadioMaterial = (p: Props) => {
  const {
    dataSource, props = {}, itemProps = {}, name,
    value, onChange, ...otherProps
  } = p;
  return (
    <Radio.Group
      {...otherProps}
      {...props}
      value={value}
      onChange={onChange}
    >
      {
        dataSource.map((_) => {
          const { name: text, value: v, props: currentProps } = _;
          const newProps = { ...itemProps, ...currentProps };
          return (
            <Radio value={v} {...newProps} key={v}>{text}</Radio>
          );
        })
      }
    </Radio.Group>
  );
};
