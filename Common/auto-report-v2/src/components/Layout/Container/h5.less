:root .auto-report-page {
  .auto2-root-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    &-landscape {
      .auto2-page-slot_tab, .auto2-page-slot_filter {
        display: none;
      } 
    }
  }
  .auto2-page-data {
    flex: 1;
    overflow: hidden;
  }
  .auto2-page-slot_body {
    height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .auto-query-display {
    background-color: #eeeeee;
    padding: 10px 0 10px 15px;
    color: #999;
    font-size: 10px;
    line-height: 14px;
    margin-bottom: 0;
  }
}
