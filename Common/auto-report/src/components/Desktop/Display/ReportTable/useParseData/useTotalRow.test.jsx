const React = require('react');
const { default: useTotalRow } = require('./useTotalRow');
const { renderHook, act } = require('@testing-library/react-hooks');
const renderer = require('react-test-renderer');

const sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
describe('useTotalRow', () => {
  let getResult, rows, columns, isCrossTable, requestManager, showIndex, update;
  function reset() {
    getResult = () => null;
    rows = [];
    columns = [];
    isCrossTable = true;
    showIndex = false;
    requestManager = {
      query: {},
      doRequest: () => null,
      rootNode: {},
      totalQueryFilter: {},
    };
  }
  reset();
  beforeEach(() => {
    let result, testInstance, waitForNextUpdate;
    getResult = async () => {
      const resp = renderHook(() => useTotalRow(rows, columns, isCrossTable, requestManager, showIndex));
      result = resp.result;
      waitForNextUpdate = resp.waitForNextUpdate;
      await resp.waitForNextUpdate();
      return result.current;
    };
    update = async () => {
      await waitForNextUpdate();
      return result.current;
    };
  });

  afterEach(() => {
    reset();
  });

  it('如果不是交叉表，则不会发起请求', async () => {
    isCrossTable = false;
    let requested = false;
    requestManager.doRequest = () => requested = true;
    const { result } = renderHook(() => useTotalRow(rows, columns, isCrossTable, requestManager, showIndex));
    expect(result.current[0].length).toBe(0);
    expect(result.current[1]).toBeNull();
    await sleep(100);
    expect(requested).toBe(false);
  });

  describe('request', () => {
    let doRequest;
    beforeEach(() => {
      doRequest = jest.fn(() => ({}));
      requestManager.doRequest = doRequest;
      requestManager.query = {
        groupBy: [],
        pageSize: 20,
        pageNo: 2,
      };
      requestManager.rootNode.items = [{}];
      requestManager.rootNode.groupDims = {
        parentKey: null,
      };
      columns = [{
        field: 'parentKey',
        children: [{
          field: 'childKey1',
        }, {
          field: 'childKey2',
        }],
      }];
    });

    it('请求时，groupBy 只有一个值，且和query中的groupBy无关', async () => {
      requestManager.query = {
        groupBy: ['asdfg'],
      };
      await getResult();
      const req = doRequest.mock.calls[0][0];
      expect(req.groupBy).toStrictEqual(['parentKey']);
    });

    it('请求时会重置pageSize,pageNo字段', async () => {
      await getResult();
      expect(doRequest.mock.calls.length).toBe(1); 
      const req = doRequest.mock.calls[0][0];
      expect(req.pageNo).toBe(1); 
      expect(req.pageSize).toBe(100); 
    });

    it('doRequest返回的值，即为totalRowNode', async () => {
      const root = {};
      requestManager.doRequest = jest.fn(() => root);
      const [_, totalRoot] = await getResult();
      expect(totalRoot).toEqual(root);
    });

    it('rows 会新增一条合计行的记录', async () => {
      const root = {};
      requestManager.doRequest = jest.fn(() => root);
      const [rows] = await getResult();
      expect(rows.length).toBe(1);
      expect(rows[0].isTotalRow).toBeTruthy();
    });

    it('合计行会在第一列设置「合计」两字', async () => {
      columns.unshift({
        field: 'first',
      });
      const [rows] = await getResult();
      expect(rows[0].first.data).toBe('合计');
    });

    it('如果设置了序号列，「合计」两字会出现在序号列上', async () => {
      showIndex = true;
      const [rows] = await getResult();
      expect(rows[0].rootIndex.data).toBe('合计');
    });

    it('合计返回的数据，能正确填充表格', async () => {
      const root = {
        groupDims: { parentKey: null },
        items: [{
          groupDims: { parentKey: 'a' },
          aggr: { childKey1: 123, childKey2: 345 },
        }, {
          groupDims: { parentKey: 'b' },
          aggr: { childKey1: 678, childKey2: 190 },
        }]
      };
      requestManager.doRequest = jest.fn(() => root);
      const [rows] = await getResult();
      const row = rows[0];
      expect(row['parentKey[a]-childKey1'].data).toEqual(123);
      expect(row['parentKey[a]-childKey2'].data).toEqual(345);
      expect(row['parentKey[b]-childKey1'].data).toEqual(678);
      expect(row['parentKey[b]-childKey2'].data).toEqual(190);
    });

    it('没有数据时，销毁totalRow', async () => {
      const { result, rerender } = renderHook(() => useTotalRow(rows, columns, isCrossTable, requestManager, showIndex));
      expect(result.current[0].length).toBe(0);
      requestManager.rootNode = {};
      rerender();
      expect(result.current[0].length).toBe(0);
      expect(result.current[1]).toBeNull(); 
    });
  });
});
