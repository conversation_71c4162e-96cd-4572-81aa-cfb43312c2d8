/* eslint-disable react/no-danger */
import React from 'react';
import {
  Tooltip,
  Icon,
} from 'antd';
import { useTableColumnSorter, Item as TableColumnSorterItem } from '@mtfe/next-biz/src/components/TableColumnSorter';
import {
  TableColumnConfig,
  Column,
} from '../../../../../types';
import useCell from './useCell';

type Props = {
  reportName?: string,
  dims: Map<string, {
    [k: string]: boolean;
  }>,
  isCrossTable: boolean,
  columns: TableColumnConfig[],
  showIndex?: boolean,
}

export const indexColumn: TableColumnConfig = {
  label: '序号',
  field: 'rootIndex',
  fixed: true,
  width: 70,
  isDim: false,
  children: undefined,
};

/* eslint-disable @typescript-eslint/no-explicit-any */
export default (props: Props) => {
  const {
    dims,
    showIndex,
    reportName,
  } = props;

  const { cellRender } = useCell();
  /** 获取单个列配置 */
  const getTableColumnConfig = React.useCallback((
    /** 列配置 */
    column: TableColumnConfig,
    /** 列所在的索引，从左边第一个为 0 算起 */
    index: number,
    /** 父子列的情况下，父列的名称 */
    parentField?: string,
    /** 如果是动态列，则会传入动态参数，一般为维度的某一个值 */
    namePostFix?: string,
  ): Column | null => {
    const {
      format,
      label,
      width,
      fixed,
    } = column;
    let { field } = column;
    const render = cellRender(column);
    let title: React.ReactNode = label;
    let align: 'left' | 'right' | 'center' | undefined;
    if (label && format === 'currency' && !label.includes('(元)')) {
      title = `${label}(元)`;
    }
    if (column.help) {
      title = (
        <Tooltip title={<div dangerouslySetInnerHTML={{ __html: column.help }} />}>
          {title} <Icon type="question-circle" />
        </Tooltip>
      );
    }

    if (namePostFix) {
      field = `${field}${namePostFix}`;
    }

    if (column.width && column.fixed) {
      title = (
        <div style={{ minWidth: column.width }}>
          {title}
        </div>
      );
    }
    // 序号列居中显示
    if (label && label === '序号' && field === 'rootIndex') {
      align = 'center';
    } else {
      align = (typeof format === 'string' && ['currency', 'number', 'percent'].includes(format)) ? 'right' : undefined;
    }

    const columnProps: Column = {
      align,
      alternate: column.alternate,
      width,
      culcWidth: width,
      fixed,
      title,
      treeColumn: !!column.subField,
      dataIndex: parentField ? `${parentField}-${field}` : field,
      key: parentField ? `${parentField}-${field}` : field,
      sorter: column.sortable,
      render,
      filters: column.filters ? column.filters.map(f => ({ text: f.label, value: f.value })) : undefined,
      filterMultiple: column.filterMultiple,
      field: column.field,
    };

    if (column.transform) {
      return column.transform(columnProps);
    } else {
      return columnProps;
    }
  }, [cellRender]);

  /** 递归计算表格列配置 */
  const getTableColumn = React.useCallback((
    /**
     * 所有列计算的结果
     */
    result: { index: number, configs: Column[] },
    /** 列配置 */
    column: TableColumnConfig,
    /** 父子列的情况下，父列的名称 */
    parentName?: string,
  ) => {
    const { children } = column;
    const makeChildren = (config: Column, parentDim?: string) => {
      if (children && children.length) {
        const r = children.reduce((value: {
          index: number,
          configs: Column[],
        }, child: TableColumnConfig) => {
          if (parentDim && child.showWithParentDims) {
            if (!child.showWithParentDims.includes(parentDim)) return value;
          }
          if (parentDim && child.hideWithParentDims) {
            if (child.hideWithParentDims.includes(parentDim)) return value;
          }
          return getTableColumn(value, child, config.dataIndex);
        }, {
          index: result.index,
          configs: [],
        });
        result.index = r.index;
        config.children = r.configs;
        return r.configs;
      }
    };
    const set = dims.get(column.field || '');
    const setKeys = Object.keys(set || {});
    if (setKeys.length && column.children && column.children.length) {
      // 如果列是一个维度，则这个列就是「动态列」，要按照维度的值横向扩展开
      setKeys.forEach((group) => {
        const newColumn = { ...column, label: group };
        const config = getTableColumnConfig(newColumn, result.index, parentName, `[${group}]`);
        if (config) {
          makeChildren(config, group);
          result.configs.push(config);
        }
        result.index++;
      });
    } else {
      const config = getTableColumnConfig(column, result.index, parentName);
      if (config) {
        makeChildren(config);
        result.configs.push(config);
      }
      result.index++;
    }

    return result;
  }, [getTableColumnConfig, dims]);

  /** 添加序号列 */
  const columnsWithIndex = React.useMemo(() => {
    if (showIndex) {
      return [indexColumn, ...props.columns];
    } else {
      return props.columns;
    }
  }, [props.columns, showIndex]);

  /** 准备自定义列排序数据 */
  const initSortingValues = React.useMemo(() => props.columns.map((c) => {
    let sortable = true;
    let selectable = true;
    if (c.isDim) {
      sortable = false;
      selectable = c.configurable === true;
    } else {
      sortable = c.configurable !== false;
      selectable = c.configurable !== false;
    }
    const result: TableColumnSorterItem = {
      field: c.field || '',
      label: c.label,
      sortable,
      selectable,
    };
    if ('defaultEnabled' in c) {
      result.defaultEnabled = c.defaultEnabled;
    }
    if (c === indexColumn) {
      result.sortable = false;
      result.selectable = false;
    }
    return result;
  }), [props.columns]);

  /** 获取排序结果 */
  const {
    sortingResult,
    showColumnSortor,
    sorterModal,
  } = useTableColumnSorter({
    initValue: initSortingValues,
    reportName,
  });

  /** 开始计算表格列配置 */
  const tableColumns = React.useMemo(() => {
    const result = columnsWithIndex.reduce((r, c) => getTableColumn(r, c), { index: 0, configs: [] });
    return result.configs.filter(Boolean) as Column[];
  }, [columnsWithIndex, getTableColumn, showIndex]);

  /** 过滤隐藏、横排序后的列 */
  const tableColumnsWithSorting = React.useMemo(() => {
    const map: {[k: string]: Column[] } = {};
    tableColumns.forEach((c) => {
      const children = map[c.field || ''] || [];
      children.push(c);
      map[c.field || ''] = children;
    });
    return [{
      field: indexColumn.field,
      enabled: true,
    }].concat(sortingResult).reduce((r, s) => {
      const children = map[s.field || ''];
      if (children && s.enabled !== false) {
        r = r.concat(children);
      }
      return r;
    }, [] as Column[]);
  }, [sortingResult, tableColumns]);

  // /** 计算表格总宽度 */
  // const width = React.useMemo(() => {
  //   // 将最后一列的宽度设置为 0
  //   const setLastConfigWidthZero = (_configs: Column[]) => {
  //     const lastConfig = _configs[_configs.length - 1];
  //     if (lastConfig?.children?.length) {
  //       lastConfig.width = undefined;
  //       setLastConfigWidthZero(lastConfig.children);
  //     } else if (lastConfig) {
  //       lastConfig.width = undefined;
  //     }
  //   };
  //   setLastConfigWidthZero(tableColumnsWithSorting);
  //   const next = (r: number, column: any) => {
  //     if (column.culcWidth) {
  //       // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
  //       r += (column.culcWidth as number || 0);
  //     }
  //     if (column.children) {
  //       r = column.children.reduce(next, r);
  //     }
  //     return r;
  //   };
  //   return tableColumnsWithSorting.reduce(next, 70);
  // }, [tableColumnsWithSorting]);

  /** 导出报表时候的列配置 */
  const exportColumns = React.useMemo(() => props.columns.map((column) => {
    const index = sortingResult.findIndex(v => v.field === (column.field || ''));
    if (index === -1) return column;
    const sorting = sortingResult[index];
    if (sorting.enabled === false) return null;
    return { ...column, rank: sorting.rank };
  }, []).filter(Boolean), [sortingResult, props.columns]);

  return {
    showColumnSortor,
    // width,
    tableColumns: tableColumnsWithSorting,
    sorterModal,
    exportColumns,
  };
};
