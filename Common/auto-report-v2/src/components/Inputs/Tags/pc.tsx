import React from 'react';
import { Tags } from '@mtfe/sjst-antdx-next';
import { ID } from '@mtfe/next-biz/es/services/types';
import withAdapter from '../../../core/withAdapter';
import { TagsProps } from './types';
import './index.less';

const _Tags = (props: TagsProps) => {
  const {
    options,
    multiple,
    baseRefObject,
    updateStatus,
    value,
    onChange,
  } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const _options = React.useMemo(() => options.map(v => ({ name: v.title, value: v.value })), [options]);

  const _onchange = React.useCallback((v) => {
    const item = options.find(o => o.value === v || (!v && !o.value));
    const bid = item?.bid;
    if (bid) {
      baseRefObject?.analytics.moduleClick(bid);
    }
    onChange?.(item?.value as ID);
  }, [options, onChange])

  return React.useMemo(() => {
    return (
      <Tags
        options={_options}
        value={value}
        min={multiple ? undefined : 1}
        max={multiple ? undefined : 1}
        onChange={_onchange}
        className={props?.className}
      />
    )
  }, [value, _options, multiple, onChange]);
};

_Tags.prototype.getDisplayValue = (value: ID, props?: TagsProps) => props?.options.find(v => v.value === value)?.title;

export default withAdapter(_Tags, { name: 'Tags' });
