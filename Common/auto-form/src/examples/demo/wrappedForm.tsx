/**
 * AutoForm
 * @title  AutoForm嵌套
 * @order 2
 */
import React from 'react';
import { Input, Button } from '@mtfe/sjst-antdx';
import { createAutoForm } from '../..';
import { Context } from '../../type';
import { submitData, FormValues, DataPool } from '../api';
import { Refresh } from '../../templates/refresh';

const timeForm = createAutoForm({
  properties: {
    timeRange: {
      label: '可用时间段',
      required: true,
      material: {
        name: 'time-range-picker',
      },
    },
    unavailableDate: {
      label: '不可用日期',
      required: true,
      extra: '补充说明文案',
      material: {
        name: 'date-range-picker-group',
        props: {
        },
      },
    },
  },
  onSubmit: async () => {},
});

const ruleForm = createAutoForm({
  formProps: {
    // 设置默认值
    initialValues: {
      repeatable: 1,
    },
  },
  properties: {
    repeatable: ctx => ({
      label: '优惠方式',
      required: true,
      material: {
        name: 'radio',
        dataSource: ctx.dataPool.options,
      },
    }),
  },
});

const onInitBefore = async (ctx: Context<DataPool>) => {
  ctx.dataPool.options = [
    {
      name: '满减',
      value: 0,
    },
    {
      name: '每满减',
      value: 1,
    },
  ];
};

const handle = createAutoForm<DataPool, FormValues>({
  properties: {
    tForm: {
      label: '嵌套表单',
      material: () => {
        const A = timeForm.AutoForm;
        return <A />;
      },
    },
    rForm: {
      label: '',
      material: () => {
        const Form = ruleForm.AutoForm;
        return <Form onInitBefore={onInitBefore} />;
      },
    },

    name: {
      label: '名称',
      help: '用问号解释这个字段的作用',
      material: () => <Input />,
      slot: (dom: JSX.Element) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {dom}
          </div>
          后面自定义插入内容
        </div>
      ),
    },
    time: {
      label: '时间',
      required: true,
      material: {
        name: 'time-picker',
      },
      wrapper: (i, dom) => {
        return i.getFieldValue('name')?.length > 2 ? dom : null;
      },
      ifUpdate: ['name'],
    },
    timeRange: {
      label: '时间范围',
      required: true,
      material: {
        name: 'time-range-picker',
      },
    },
  },
  onSubmit: async v => {
    console.log('提交数据,', v);
    const r = await submitData();
    console.log('提交结果,', r);
  },
  template: (doms, { submit }) => {
    // 这里template返回的自定义内容在form内侧
    return (
      <>
        {Object.values(doms)}
        <Refresh ifUpdate={['name']}>
          {r => {
            return r.getFieldValue('name')?.length ? 12312321 : null;
          }}
        </Refresh>
        <Button type="primary" onClick={submit}>提交</Button>
      </>
    );
  },
  container: form => {
    // 这里包裹的div在Form组件外侧
    return <div>{form}</div>;
  },
});

export default handle.AutoForm;
