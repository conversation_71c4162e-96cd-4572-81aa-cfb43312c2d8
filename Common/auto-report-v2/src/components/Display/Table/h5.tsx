/* eslint-disable @typescript-eslint/no-explicit-any */
import { isNil } from 'lodash';
import React, { useEffect } from 'react';
import {
  Loading,
  NoData,
  FixedTable,
  IFixedTableColumn,
} from '@mtfe/sjst-ui-next';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import { dataContext } from '../../../contexts';
import PipelineManager from '../../../core/PipelineManager';
import formatManager from '../../../core/FormatManager';
import { pipelineManager } from './pipelines';
import { recursiveColumns } from './helper';
import { defaultFormat } from '../../../helper/format';
import { ObjectRefType, ReportPage, ReportValueType } from '../../../types';
import { TableProps, TablePopelineValueType } from './types';

import './h5.less';

export let pubFixedTableRef: FixedTable | null = null;

function _Table<ListRowData>(props: TableProps<ListRowData>) {
  const {
    directlyTotal,
    columns,
    pipelines,
    updateStatus,
    value,
    baseRefObject,
  } = props;

  const { env } = React.useContext(dataContext);

  const fixedTableRef = React.useRef<any>();
  const sortRef = React.useRef<any>({});

  const refObject = useRefObject(baseRefObject);
  const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<
    ReportValueType<ListRowData[]>
  >;
  const valueColumns = value?.columns || [];

  const [state, setState] = React.useState<{
    dataSource: ListRowData[];
    // eslint-disable-next-line
    sumData?: any;
    reportPage: ReportPage;
  }>({
    dataSource: [],
    reportPage: {},
  });

  const { value: reportValue, status: reportStatus } = reportRefObject || {};

  React.useEffect(() => {
    (async () => {
      const _columns =
        typeof columns === 'function'
          ? columns(reportValue || {}, env)
          : columns;

      // 管道用以处理原始数据
      const viewData = await (pipelineManager as PipelineManager<
        TablePopelineValueType<ListRowData> & ReportValueType<ListRowData[]>
      >).run(
        pipelines || [],
        {
          ...reportValue,
          columns: _columns,
        },
        { env }
      );

      setState((preState) => {
        const notPageNoOne =
          viewData.query?.pageNo && viewData.query?.pageNo > 1;
        const newDataSource = viewData.data || [];
        const dataSource = notPageNoOne
          ? preState.dataSource.concat(newDataSource)
          : newDataSource;

        if (!notPageNoOne) {
          requestAnimationFrame(() => {
            fixedTableRef.current?.iscroll?.scrollTo(0, 0, 60);
          });
        }

        return {
          dataSource,
          sumData: viewData.others?.sumData,
          reportPage: {
            total: viewData.others?.totalCount,
            pageNo: viewData.query?.pageNo,
            pageSize: viewData.query?.pageSize,
          },
        };
      });

      const dataColumns = recursiveColumns(
        viewData.columns || [],
        (v) => ({ ...v, selectableInChildren: v.expandSelectable }),
        (v) => !v.hide
      );
      if (dataColumns.length) {
        updateStatus?.(undefined, {
          columns: dataColumns,
          data: viewData.data || [],
        });
      }
    })();
  }, [columns, pipelines, reportValue]);

  const tableColumns = React.useMemo(() => {
    return valueColumns.map((column) => {
      // render 处理
      // eslint-disable-next-line
      const render = (
        value: any,
        record: any,
        index: number,
        isTotal?: boolean
      ) => {
        // eslint-disable-next-line
        const _format = (_value: any, _record: any, _index: number) => {
          const { format } = column;
          if (typeof format === 'object') {
            return refObject?.renderManager?.render(format, {
              data: {
                value: _value,
                record: _record,
                index: _index,
              },
            });
          }
          if (typeof format === 'function') {
            return format ? format(_value, _record, index, isTotal) : _value;
          }
          if (isNil(_value) && typeof _value !== 'number') {
            return _value;
          }
          return format ? formatManager.run(format, _value) : _value;
        };

        const formatValue = defaultFormat(
          directlyTotal ? value : _format(value, record, index)
        );
        return formatValue;
      };
      return {
        ...column,
        sortWays: column.sorter ? [1, -1] : undefined,
        render,
        footRender: (v: any, r: any, i: number) => render(v, r, i, true),
      } as IFixedTableColumn;
    });
  }, [valueColumns]);

  const noMore = React.useMemo(
    () =>
      state.reportPage.total &&
      state.dataSource &&
      state.reportPage.total <= state.dataSource.length,
    [state]
  );

  // 分页
  const onBottom = React.useCallback(() => {
    if (noMore) {
      return;
    }
    refObject?.emit('submit@Report', {
      query: {
        ...sortRef.current,
        pageNo: (state.reportPage.pageNo || 0) + 1,
        pageSize: state.reportPage.pageSize || 20,
      },
    });
  }, [noMore, state]);

  // 排序
  const onSort = React.useCallback((_, sort: number, dataIndex: string) => {
    const sortQuery = sort
      ? {
          orderBy: dataIndex,
          orderByType: sort === 1 ? 'asc' : 'desc',
        }
      : {};

    sortRef.current = sortQuery;
    refObject?.emit('submit@Report', {
      query: {
        ...sortQuery,
        pageNo: 1,
        pageSize: state.reportPage.pageSize,
      },
    });
  }, []);

  useEffect(() => {
    let close: () => void;
    if (reportStatus === 'pending') {
      close = Loading.loading();
    }
    
    return () => {
      close?.();
    }
  }, [reportStatus])

  return (
    <div className="auto-report-page-table">
      {/* {reportStatus === 'pending' ? <Loading.ToastLoading /> : null} */}
      {refObject?.renderManager.render({ type: 'ReportAlert' })}
      {state.dataSource?.length > 0 ? (
        <FixedTable
          ref={(p) => {
            fixedTableRef.current = p;
            pubFixedTableRef = p;
          }}
          onBottom={onBottom}
          columns={tableColumns}
          data={state.dataSource}
          style={{ marginBottom: 12 }}
          footData={state.sumData}
          fixedHeader
          fixedLeft
          onSort={onSort}
        />
      ) : (
        <NoData absolute={false} />
      )}
    </div>
  );
}

export default withAdapter(_Table, { name: 'Table' });
