import { divide } from 'number-precision';
import { FieldFormat } from '../types';

export type Format = (
  v: string | number | boolean | undefined | null
) => string | number | boolean;

export const defaultFormat: Format = (
  value: string | number | boolean | undefined | null,
) => {
  if (typeof value === 'undefined' || value === null || value === '') return '--';
  return value;
};

export const isVoid = (v: number | string | null | undefined) => {
  if (typeof v === 'undefined' || v === null) return true;
  return false;
};

export const formatMoney = (money?: string | number | undefined): string => {
  if (!money) {
    return '0.00';
  }
  money = divide(Number(money), 100);
  if (isNaN(money)) {
    return '0.00';
  }
  money = money.toFixed(2);
  return money.replace(/(?=(\B\d{3})+\.\d{2}$)/g, ',');
};

export const formatMap = {
  currency: (value: number) => formatMoney(value),
  percent: (value: number | string) => {
    if (
      value === 0
      || isVoid(value)
      || !['string', 'number'].includes(typeof value)
    ) {
      return '0.00%';
    } else {
      return `${value}%`;
    }
  },
  number: (value: number) => (Number(value) || 0).toLocaleString(),
  'timestamp[date]': (value: number) => {
    if (!value) {
      return '';
    }
    const d = new Date(value);
    const year = d.getFullYear();
    const month = d.getMonth();
    const date = d.getDate();
    return `${year}/${String(100 + month + 1).slice(1)}/${String(
      100 + date,
    ).slice(1)}`;
  },
  'timestamp[time]': (value: number) => {
    if (!value) {
      return '';
    }
    const d = new Date(value);
    const year = d.getFullYear();
    const month = String(100 + d.getMonth() + 1).slice(1);
    const date = String(100 + d.getDate()).slice(1);
    const hour = String(100 + d.getHours()).slice(1);
    const min = String(100 + d.getMinutes()).slice(1);
    const sec = String(100 + d.getSeconds()).slice(1);
    return `${year}/${month}/${date} ${hour}:${min}:${sec}`;
  },
  'timestamp[min]': (value: number) => {
    if (!value) {
      return '';
    }
    const d = new Date(value);
    const year = d.getFullYear();
    const month = String(100 + d.getMonth() + 1).slice(1);
    const date = String(100 + d.getDate()).slice(1);
    const hour = String(100 + d.getHours()).slice(1);
    const min = String(100 + d.getMinutes()).slice(1);
    return `${year}/${month}/${date} ${hour}:${min}`;
  },
} as {
  [k: string]: Format;
};

export default function format(
  fieldFormat: FieldFormat | Format | undefined,
  v: number | string,
) {
  if (!fieldFormat) return v;
  if (typeof fieldFormat === 'function') {
    return fieldFormat(v) as string | number;
  }
  const formatter = formatMap[fieldFormat];
  if (!formatter) return v;
  return formatter(v);
}
