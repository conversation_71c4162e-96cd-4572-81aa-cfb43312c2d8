import React from 'react';
import { Input } from '@mtfe/sjst-ui-next';
import withAdapter from '../../../core/withAdapter';
import { getQuery } from './helper';
import { TextInputProps } from './types';

const _TextInput = (props: TextInputProps) => {
  const {
    dataKey,
    baseRefObject,
    updateStatus,
    value,
    onChange,
    isTemporary,
    allowClear,
    ...otherProps
  } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return <Input {...otherProps} value={value || ''} onChange={onChange} />;
};

_TextInput.prototype.getQuery = getQuery;

export default withAdapter(_TextInput, { name: 'TextInput' });
