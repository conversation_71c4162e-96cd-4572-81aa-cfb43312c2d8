import React from 'react';
import PoiService from '@mtfe/next-biz/src/services/poi';
import { PriceTaxManager } from '../types/context';

export default function usePriceTax(): PriceTaxManager {
  const [havePriceTaxField, updateHavePriceTaxField] = React.useState<boolean>(false);
  const [priceTaxEnabled, updatePriceTaxEnable] = React.useState<boolean | null>(null);

  const poiService = React.useMemo(() => new PoiService(), []);

  const queryPriceTaxEnabled = React.useCallback(async () => {
    const enabled = await poiService.queryTaxSwitch();
    updatePriceTaxEnable(enabled);
  }, [updatePriceTaxEnable, poiService]);

  React.useEffect(() => {
    if (havePriceTaxField && priceTaxEnabled === null) {
      queryPriceTaxEnabled();
    }
  }, [havePriceTaxField, queryPriceTaxEnabled, priceTaxEnabled]);

  return React.useMemo(() => ({
    priceTaxEnabled,
    updateHavePriceTaxField,
  }), [
    priceTaxEnabled,
    updateHavePriceTaxField,
  ]);
}
