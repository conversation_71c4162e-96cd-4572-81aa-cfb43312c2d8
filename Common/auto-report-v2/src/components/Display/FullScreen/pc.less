:root .auto2-report-page {
  &-fullscreen {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    display: flex;
    flex-direction: column;
    z-index: 100;
    padding: 24px;

    &-title {
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
      padding-bottom: 24px;
    }

    .auto-report-page-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .ant-tablex {
      flex: initial;
      margin-top: 0!important;
      padding-top: 0!important;
      margin-bottom: 0!important;
      padding-bottom: 0!important;
      white-space: nowrap;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .ant-table-wrapper {
      display: block;
      overflow: hidden;
      flex: 1;
    }

    .ant-spin-nested-loading {
      width: 100%;
      max-height: 100%;
      display: flex;
      flex: 1;
    }

    .ant-spin-container {
      width: 100%;
      display: flex;
      flex: 1;
    }

    .ant-pagination {
      text-align: right;
      margin: 16px 0;
      position: static;
    }

    .ant-table {
      display: block;
      width: 100%;
      max-height: 100%;
      overflow-y: auto;
    }
  }
}
