:root {
  .auto-report-query-container{
    z-index: 10;
    position: relative;
    .auto-report-query-container-tip{
      font-size: 12px;
      color: #999;
      line-height: 1;
      margin-top: -4px;
      margin-bottom: 11px;
      width: 100%;
      height: 12px;
      overflow: hidden;
      .clearing-time-display {
        float: left;
      }
    }
    .auto-report-query-container-overlayer{
      &:focus {
        outline: 0px solid transparent;
      }
      &.active::after {
        content: ' ';
        display: block;
        position: absolute;
        bottom: -10px;
        left: -16px;
        right: 0;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAAKCAYAAACe5Y9JAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAAqADAAQAAAABAAAACgAAAADdaPdrAAAAP0lEQVQIHWN8+vTpfAYgYAHi12AGExMThAEXYWRkfAuW+vv37zswg5mZ+SOY8e/fv89gBlDNNzCDhYXlB4gBAEyREfT1UIGSAAAAAElFTkSuQmCC);
        height: 10px;
      }
      background: white;
      display: flex;
      flex-wrap: wrap;
      margin-right: -16px;

      .ant-btn.auto-report-query-container-btn {
        margin-top: 6.5px;
      }
    }

    .auto-report-query-container-btn{
      margin-bottom: 12px;
      margin-right: 8px;
      height: 24px;
      padding: 2.4px 7px;
      font-size: 12px;
      &:last-child{
        margin-right: 0;
      }

      &-toggle {
        color: #FF6000;
        font-size: 12px;
        height: 24px;
        padding: 2.4px 7px;
        padding-left: 0;
        margin-top: 6.5px;
        font-weight: 500;
      }
    }

    .custom-query-buttons{
      width: 100%;
      .ant-btn{
        margin-right: 8px;
      }
      .auto-report-query-container-btn-toggle{
        border: 1px solid #e5e5e5;
        color: #333;
        padding-left: 10px;
      }
      .auto-report-header{
        float: right;
        .auto-report-header-toolbar{
          .ant-btn.ant-btn-link{
            border: 1px solid #e5e5e5;
            padding: 0 12px;
            color: #333;
            margin-left: 0;
            &:last-child{
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}
