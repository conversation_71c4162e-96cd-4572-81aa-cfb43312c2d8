const React = require('react');
const renderer = require('react-test-renderer');
const {
  default: useRows,
  isEmptyGroupDims,
  isEmptyObject,
  flattenNode,
  cleanTree,
  transformNodeForCrossTable,
} = require('./useRows');

describe('useRows file', ( ) => {
  describe('isEmptyGroupDims', () => {
    it('传入空返回true', () => {
      expect(isEmptyGroupDims(null)).toBeTruthy();
    });
  
    it('空对象返回true', () => {
      expect(isEmptyGroupDims({})).toBeTruthy();
    });
  
    it('有key的对象返回false', () => {
      expect(isEmptyGroupDims({ a: 1 })).toBeFalsy();
    });
  
    it('所有 key 都为null返回true', () => {
      expect(isEmptyGroupDims({ a: null, b: null })).toBeTruthy();
    });
  });

  describe('isEmptyObject', () => {
    it('传入空返回true', () => {
      expect(isEmptyObject(null)).toBeTruthy();
    });
    it('空对象返回true', () => {
      expect(isEmptyObject({})).toBeTruthy();
    });
  
    it('对象有key即返回false', () => {
      expect(isEmptyObject({ a: 1 })).toBeFalsy();
      expect(isEmptyObject({ a: null })).toBeFalsy();
    });
  });
  
  describe('flattenNode', () => {
    it('返回的对象的key拼装正确', () => {
      const node = {
        groupDims: { parentKey: 'a' },
        aggr: {
          value1: 1234,
          value2: 4354,
          value3: 5431,
        },
      };
      const column = { field: 'parentKey' };
      const result = flattenNode(node, column);
      expect(result['parentKey[a]-value1']).toBe(1234);
      expect(result['parentKey[a]-value2']).toBe(4354);
      expect(result['parentKey[a]-value3']).toBe(5431);
    });
  
    it('返回的objectkey包含parentKey', () => {
      const node = {
        groupDims: { parentKey: 'a' },
        aggr: {
          value1: 1234,
          value2: 4354,
          value3: 5431,
        },
      };
      const column = { field: 'parentKey' };
      const result = flattenNode(node, column, 'root');
      expect(result['root-parentKey[a]-value1']).toBe(1234);
      expect(result['root-parentKey[a]-value2']).toBe(4354);
      expect(result['root-parentKey[a]-value3']).toBe(5431);
    });
  
    it('包含子元素时会递归的处理', () => {
      const node = {
        groupDims: { parentKey: 'a' },
        aggr: {
          value1: 1234,
          value2: 4354,
          value3: 5431,
        },
        items: [{
          groupDims: { parentKey2: 'b' },
          aggr: {
            value1: 545,
            value2: 542,
            value3: 543,
          }
        }]
      };
      const column = { field: 'parentKey', children: [{ field: 'parentKey2' }] };
      const result = flattenNode(node, column);
      expect(result['parentKey[a]-parentKey2[b]-value1']).toBe(545);
      expect(result['parentKey[a]-parentKey2[b]-value2']).toBe(542);
      expect(result['parentKey[a]-parentKey2[b]-value3']).toBe(543);
    });
  });
  
  describe('cleanTree', () => {
    it('删除node上多余的groupDims', () => {
      const node = {
        groupDims: { a: 1 },
        items: [{
          groupDims: { a: 1, b: 2 },
          items: [{
            groupDims: { a: 1, b: 2, c: 3 },
          }],
        }],
      };
      const result = cleanTree(node);
      expect(result.groupDims.a).toBe(1);
      expect(result.items[0].groupDims.a).toBeUndefined();
      expect(result.items[0].groupDims.b).toBe(2);
      expect(result.items[0].items[0].groupDims.a).toBeUndefined();
      expect(result.items[0].items[0].groupDims.b).toBeUndefined();
      expect(result.items[0].items[0].groupDims.c).toBe(3);
    });
  });
  
  describe('transformNodeForCrossTable', () => {
    it('正确返回转换结果', () => {
      const root = {
        groupDims: { parentKey: null },
        items: [{
          groupDims: { parentKey: 'a' },
          aggr: { childKey1: 123, childKey2: 345 },
        }, {
          groupDims: { parentKey: 'b' },
          aggr: { childKey1: 678, childKey2: 190 },
        }]
      };
      const columns = [{
        field: 'parentKey',
        children: [{
          field: 'childKey1',
        }, {
          field: 'childKey2',
        }],
      }];
      const [result] = transformNodeForCrossTable(root, columns);
      expect(result.values['parentKey[a]-childKey1']).toEqual(123);
      expect(result.values['parentKey[a]-childKey2']).toEqual(345);
      expect(result.values['parentKey[b]-childKey1']).toEqual(678);
      expect(result.values['parentKey[b]-childKey2']).toEqual(190);
    });
  
    it('对于深层结构，从columns出现children时再开始转换', () => {
      const root = {
        groupDims: { parentKey: null },
        items: [{
          groupDims: { someKey1: 234 },
          items: [{
            groupDims: { someKey2: 43 },
            items: [{
              groupDims: { parentKey: 'a' },
              aggr: { childKey1: 123, childKey2: 345 },
            }, {
              groupDims: { parentKey: 'b' },
              aggr: { childKey1: 678, childKey2: 190 },
            }],
          }],
        }],
      };
      const columns = [{
        field: 'someKey1',
      }, {
        field: 'someKey2',
      }, {
        field: 'parentKey',
        children: [{
          field: 'childKey1',
        }, {
          field: 'childKey2',
        }],
      }];
      const [result] = transformNodeForCrossTable(root, columns);
      expect(result.items[0].items[0].values['parentKey[a]-childKey1']).toEqual(123);
      expect(result.items[0].items[0].values['parentKey[a]-childKey2']).toEqual(345);
      expect(result.items[0].items[0].values['parentKey[b]-childKey1']).toEqual(678);
      expect(result.items[0].items[0].values['parentKey[b]-childKey2']).toEqual(190);
    });
  });
  
  describe('useRows', () => {
    let rows, rootNode, columns, render, update, isCrossTable, isTreeTable, showIndex;
    const reset = () => {
      rows = [];
      rootNode = {
        groupDims: { parentKey1: null, parentKey1Alias: null, parentKey2: null, parentKey3: null },
        aggr: { childKey1: 7636, childKey2: 4, childKey3: 736 },
        items: [{
          groupDims: { parentKey1: 234, parentKey1Alias: 123 },
          aggr: { childKey1: 7636, childKey2: 4, childKey3: 736 },
          items: [{
            groupDims: { parentKey1: 234, parentKey1Alias: 123, parentKey2: 43 },
            aggr: { childKey1: 923, childKey2: 12903, childKey3: 89754 },
            items: [{
              groupDims: { parentKey1: 234, parentKey1Alias: 123, parentKey2: 43 , parentKey3: 'a' },
              aggr: { childKey1: 123, childKey2: 345, childKey3: 4903 },
            }, {
              groupDims: { parentKey1: 234, parentKey1Alias: 123, parentKey2: 43 , parentKey3: 'b' },
              aggr: { childKey1: 678, childKey2: 190, childKey3: 4398 },
            }],
          }],
        }, {
          groupDims: { parentKey1: 4356, parentKey1Alias: 234 },
          aggr: { childKey1: 7463, childKey2: 38382, childKey3: 483873 },
          items: [{
            groupDims: { parentKey1: 4356, parentKey1Alias: 234, parentKey2: 32 },
            aggr: { childKey1: 7638, childKey2: 9373, childKey3: 4736 },
            items: [{
              groupDims: { parentKey1: 4356, parentKey1Alias: 234, parentKey2: 32 , parentKey3: 'a' },
              aggr: { childKey1: 4930, childKey2: 6535, childKey3: 45498 },
            }, {
              groupDims: { parentKey1: 4356, parentKey1Alias: 234, parentKey2: 32 , parentKey3: 'b' },
              aggr: { childKey1: 4397, childKey2: 897982, childKey3: 89721 },
            }],
          }],
        }],
      };
      columns = [];
      isCrossTable = false;
      isTreeTable = false;
      showIndex = false;
    };
  
    beforeEach(() => {
      let result, testInstance;
      const Component = () => {
        rows = useRows({
          rootNode,
          columns,
          isCrossTable,
          isTreeTable,
          showIndex,
        }).rows;
        return null;
      };
    
      render = () => {
        renderer.act(() => {
          testInstance = renderer.create(<Component />);
        });
        return result;
      };
      update = () => {
        renderer.act(() => {
          testInstance.update(<Component />);
        });
        return result;
      };
    });
  
    reset();
    afterEach(reset);
  
    const commomCase = () => {
      it('每个字段对应一个 Cell', () => {
        render();
        rows.forEach(row => {
          Object.keys(row).forEach((key) => {
            if (['isTotalRow', 'children', 'id'].includes(key)) return;
            if (!row[key]) return;
            expect(row[key]).toHaveProperty('data');
            expect(row[key]).toHaveProperty('rowSpan');
            expect(row[key]).toHaveProperty('colSpan');
          });
        });
      });
  
      it('每一行都有独一无二的 ID', () => {
        const ids = [];
        render();
        const iter = (row) => {
          expect(ids).not.toContain(row.id);
          ids.push(row.id);
          row.children?.map(iter);
        };
        rows.forEach(iter);
      });
    };
  
    describe('分组表', () => {
      beforeEach(() => {
        columns = [{
          field: 'parentKey1',
        }, {
          field: 'parentKey2',
        }, {
          field: 'parentKey3',
        }, {
          field: 'childKey1',
        }, {
          field: 'childKey2',
        }];
      });
  
      commomCase();
  
      it('汇总行的rowSpan设置正确', () => {
        render();
        [
          [4,3,1,1,1],
          [0,0,1,1,1],
          [0,0,1,1,1], // 分组合计
          [0,1,1,1,1], // 分组合计
          [4,3,1,1,1],
          [0,0,1,1,1],
          [0,0,1,1,1], // 分组合计
          [0,1,1,1,1], // 分组合计
        ].forEach((spans, i) => {
          const row = rows[i];
          spans.forEach((span, j) => {
            expect(row[columns[j].field].rowSpan).toBe(span);
          });
        });
      });

      it('汇总行「合计」标题设置正确', () => {
        render();
        expect(rows[2].parentKey3.data).toBe('合计');
        expect(rows[3].parentKey2.data).toBe('合计');
        expect(rows[6].parentKey3.data).toBe('合计');
        expect(rows[7].parentKey2.data).toBe('合计');
        expect(rows[8].parentKey1.data).toBe('合计');
        showIndex = true;
        render();
        expect(rows[8].rootIndex.data).toBe('合计');
      });

      it('alias将会替换列', () => {
        columns = [{
          field: 'parentKey1',
          alias: 'parentKey2',
        }, {
          field: 'parentKey3',
        }, {
          field: 'childKey1',
        }, {
          field: 'childKey2',
        }];
        render();
        expect(rows[0].parentKey1.data).toBe(43);
      });
    });
  
    describe('交叉表', () => {
      beforeEach(() => {
        isCrossTable = true;
        columns = [{
          field: 'parentKey1',
        }, {
          field: 'parentKey2',
        }, {
          field: 'parentKey3',
          children: [{
            field: 'childKey1',
          }, {
            field: 'childKey2',
          }],
        }];
      });

      commomCase();
      it('列自动扩展', () => {
        render();
        expect(rows).toHaveLength(2);
        expect(rows[0]['parentKey3[a]-childKey1'].data).toBe(123);
        expect(rows[0]['parentKey3[a]-childKey2'].data).toBe(345);
        expect(rows[0]['parentKey3[b]-childKey1'].data).toBe(678);
        expect(rows[0]['parentKey3[b]-childKey2'].data).toBe(190);

        expect(rows[1]['parentKey3[a]-childKey1'].data).toBe(4930);
        expect(rows[1]['parentKey3[a]-childKey2'].data).toBe(6535);
        expect(rows[1]['parentKey3[b]-childKey1'].data).toBe(4397);
        expect(rows[1]['parentKey3[b]-childKey2'].data).toBe(897982);
      });

      it('alias将会替换列', () => {
        columns = [{
          field: 'parentKey1',
          alias: 'parentKey2',
        }, {
          field: 'parentKey3',
          children: [{
            field: 'childKey1',
          }, {
            field: 'childKey2',
          }],
        }];
        render();
        expect(rows[0].parentKey1.data).toBe(43);
      });
    });
  
    describe('树表', () => {
      beforeEach(() => {
        isCrossTable = true;
        isTreeTable = true;
        columns = [{
          field: 'parentKey1',
          subField: 'parentKey2',
        }, {
          field: 'parentKey3',
        }, {
          filed: 'childKey1',
        }, {
          field: 'childKey2',
        }];
      }); 

      commomCase();
      it('自动添加 children 字段', () => {
        render();
        expect(rows).toHaveLength(2);
        rows.forEach((row) => {
          expect(row).toHaveProperty('children');
        });
      });
      
      it('children 中用 subFiled 替换 field', () => {
        render();
        expect(rows[0].children[0].parentKey1.data).toBe(43);
        expect(rows[1].children[0].parentKey1.data).toBe(32);
      });

      it('alias将会替换列', () => {
        columns = [{
          field: 'parentKey1',
          alias: 'parentKey1Alias',
          subField: 'parentKey3',
        }, {
          filed: 'childKey1',
        }, {
          field: 'childKey2',
        }];
        render();
        expect(rows[0].parentKey1.data).toBe(123);
        expect(rows[1].parentKey1.data).toBe(234);
      });
    });
  });
});

