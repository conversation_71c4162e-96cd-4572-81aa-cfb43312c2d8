import { toPairs } from 'lodash';
import { Format, formatMap } from '../helper/format';

export class FormatManager {
  private _funcs = new Map<string, Format>();

  constructor() {
    const formats = toPairs(formatMap);
    formats.forEach((v) => {
      this.add(v[0], v[1]);
    });
  }

  add(name: string, func: Format) {
    this._funcs.set(name, func);
  }

  run(name: string, value: string | number | boolean | undefined | null) {
    return this._funcs.get(name)?.(value);
  }
}

export const manager = new FormatManager();
export default manager;
