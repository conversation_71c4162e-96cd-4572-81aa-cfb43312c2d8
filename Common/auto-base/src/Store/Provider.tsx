import React from 'react';
import { Store } from './types';

export const storeContext = React.createContext<Store | null>(null);

export interface ProviderProps {
  store: Store;
}

export class Provider extends React.Component<ProviderProps> {
  render() {
    const StoreContext = storeContext;
    StoreContext.displayName = 'AT.Store.Context';
    return (
      <StoreContext.Provider value={this.props.store}>
        {this.props.children}
      </StoreContext.Provider>
    );
  }
}
