import React from 'react';
import { Radio } from '@mtfe/sjst-antdx-next';
import { ID } from '@mtfe/next-biz/es/services/types';
import withAdapter from '../../../core/withAdapter';
import { RadioGroupProps } from './types';

const RadioGroup = (props: RadioGroupProps) => {
  const {
    className, options, updateStatus, value, onChange,
  } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return React.useMemo(() => {
    return (
      <Radio.Group
        className={className}
        value={value}
        options={options}
        onChange={e => onChange?.(e.target.value)}
      />
    );
  }, [className, value, options, onChange])
};

RadioGroup.prototype.getDisplayValue = (
  value: ID[],
  props?: RadioGroupProps,
) => {
  const display = value
    ?.map(o => props?.options?.find?.(v => v.value === o)?.label)
    .join('，');
  return display;
};

export default withAdapter(RadioGroup, { name: 'RadioGroup' });
