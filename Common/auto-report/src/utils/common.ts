import { Modal } from 'antd';
import { debounce } from 'lodash';
import moment from 'moment';
import { timeRangeLimitRecord } from '@mtfe/next-biz/es/utils/reportQueryRenderLog';
import { getTimeRangeByPermission } from '../services/common';

/** 避免重复提示 */
const timeConfirm = debounce((content: string) => {
  Modal.confirm({
    title: '查询时间提示',
    content,
  });
}, 200);
interface TimePermissionType {
  /** 页面权限code */
  permissionCode: number;
  /** 接口查询参数 */
  params: IV;
  /** 开始时间的key, 没传就默认startDate */
  startKey?: string;
  /** 是否需要弹框提示，默认提示 */
  showConfirmTip?: boolean
}
/** 验证查询时间时间是否在范围内 */
export const checkTimePermission = async (props: TimePermissionType): Promise<boolean> => {
  const {permissionCode, params, startKey = 'startDate', showConfirmTip = true} = props;
  if (permissionCode && params && params[startKey]) {
    const timeRange = await getTimeRangeByPermission(permissionCode);
    if (timeRange?.beginTime !== -1) {
      const beginTime = moment(timeRange?.beginTime);
      const startDate = moment(params[startKey]);
      if (beginTime.isAfter(startDate, 'day')) {
        const diff = moment().diff(beginTime, 'day') + 1;
        const message = `您的报表查询范围是近${diff}天，不满足查询要求，请重新选择查询时间或联系管理员修改权限`;
        if (showConfirmTip) {
          timeConfirm(message);
          throw new Error(message);
        }
        return false;
      }
    }
    return true
  }
  return true;
}

export const valueIsTwoYearsAgo = (start: number, needLimitTime?: boolean) => {
  if (needLimitTime && start && moment(start).isBefore(moment().subtract(2, 'year'), 'day')) {
    const content = {
      title: '报表查询时间范围提醒',
      content: '为了保证系统运行性能，系统上仅支持查询近2年以内的数据，如果你需要查询2年之前的数据，可联系4006260106，我们将竭诚为你服务',
      confirmText: '知道了',
    }
    Modal.confirm({
      ...content,
      okText: content.confirmText,
      cancelButtonProps: { style: { display: 'none' } }
    });
    timeRangeLimitRecord();
    return true;
  }
  return false;
}