/* eslint-disable react/no-danger */
import React from 'react';
import {
  Button, Modal, Icon, message,
} from '@mtfe/sjst-antdx-next';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import { PermissionContext } from '@mtfe/next-biz/src/contexts/permission';
import { DownloadListIntro } from '@mtfe/next-biz/src/components/DownloadListIntro';
import { getTableExplain } from '@rms-report/components/common/TableExplain';
import Net from '@mtfe/next-biz/es/lib/Net';
import { ReportContext, ReportHeaderProps } from '../../types';
import { registorComponent } from './registor';
import './ReportHeader.less';

interface ExplainProps {
  title: string;
  content: string;
}

export default function ReportHeader(props: ReportHeaderProps) {
  const report = React.useContext(ReportContext);
  const permissionService = React.useContext(PermissionContext);
  const [downloading, updateDownloading] = React.useState(false);
  const [bookmarking, updateBookmarking] = React.useState(false);
  const {
    description,
    downloadPermissionCode,
    showExportButton = true,
    isSubTitle,
    explainTitle,
    extraButtons,
    tableExplainKey = '',
    hideStar
  } = props;
  const {
    requestManager, exportManager, bookmarkManager, toolsManager, analysisManager,
  } = report || {};
  const {
    exportable, getExportUrl, extraExportParams, title,
  } = exportManager || {};

  const { bookmarked, updateBookmarks } = bookmarkManager || {};
  const { tools } = toolsManager || {};
  const { moduleClick } = analysisManager || {};
  const [descriptionVisiable, setDescriptionVisiable] = React.useState(false);
  const [tableExplain, setTableExplain] = React.useState<ExplainProps>({title: '', content: ''});

  const getDownloadUrl = React.useCallback(async () => {
    if (!getExportUrl) return;
    const result = await doAction(getExportUrl());
    if (!result) return;
    if (typeof result === 'string') {
      return result;
    } else if (result && result.isOffLineDownloading) {
      Modal.confirm({
        title: result.totalCount
          ? <div>{title}<br />本次导出数据共{result.totalCount}条，请前往通用顶栏「下载清单」查看下载状态</div>
          : '导出内容请前往下载清单查看',
        okText: '前往下载清单',
        cancelText: '我知道了',
        maskClosable: false,
        onOk: () => {
          window.open('/web/fe.rms-portal/rms-report.html#/rms-report/promiseDownload');
        },
        onCancel: () => {
          const startDom = document.querySelector('.ant-modal-confirm-btns .ant-btn');
          const cancelBtnPos = startDom?.getBoundingClientRect();
          DownloadListIntro({ startDomRect: cancelBtnPos });
        },
      });
    }
  }, [getExportUrl]);

  const download = React.useCallback(async () => {
    moduleClick && moduleClick(props.downloadBid);
    if (downloading) return;
    if (downloadPermissionCode) {
      if (!permissionService?.checkhasPermissionSync(downloadPermissionCode)) {
        Modal.confirm({
          title: '您的账号没有【导出】权限，请联系管理员分配权限再操作',
          okText: '我知道了',
          okCancel: false,
        });
        return;
      }
    }
    if (!exportable) {
      Modal.confirm({
        title: '查询结果为空，没有可导出的数据',
        okText: '我知道了',
        okCancel: false,
      });
      return;
    }
    if ((requestManager?.rootNode?.page?.totalCount || 0) > 20 * 10000) {
      Modal.warning({
        title: '不支持导出',
        content: '数据超过20万条，不支持导出，建议缩短查询时间跨度再导出',
        okText: '我知道了',
      });
      return;
    }
    try {
      const authRes = await Net.post('/api/v1/excel/export/auth', { exportPermissionCode: downloadPermissionCode });
      console.log(authRes, '=====');
    } catch (error) {
      if (error?.code === 98015) {
        Modal.warning({
          title: '操作失败',
          content: (
            <div>
              <span>当前免密登录角色暂无导出/下载权限，申请权限请查看</span>
              <a target='_blank' href='https://km.sankuai.com/collabpage/1849901095#b-aa65141c21364c7c90600428131c2bd6'>餐饮SaaS-免密登录角色权限申请流程</a>
              <span>，或联系商家自行处理。</span>
            </div>
          ),
          okText: '知道了'
        });
        return;
      }
    }
    updateDownloading(true);
    const url = await getDownloadUrl();
    updateDownloading(false);
    if (!url) return;
    message.success('导出成功');
    const link = document.createElement('a');
    link.download = '';
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [
    getDownloadUrl,
    updateDownloading,
    moduleClick,
    props.downloadBid,
    permissionService,
  ]);

  
  const _getTableExplain = React.useCallback(async() => {
    const data = await getTableExplain();
    setTableExplain(data?.[tableExplainKey]);
  }, []);

  const descriptionNode: React.ReactNode = React.useMemo(() => {
    if (tableExplainKey) {
      return <div dangerouslySetInnerHTML={{ __html: tableExplain?.content }} />;
    } else {
      if (!description) return null;
      let content: React.ReactNode = null;
      if (typeof description === 'string') {
        content = <div dangerouslySetInnerHTML={{ __html: description || '' }} />;
      } else {
        content = description;
      }
      return content;
    }
  }, [description, tableExplain]);

  const toggleDescription = React.useCallback((isClick: boolean) => {
    if(isClick && tableExplainKey) {
      _getTableExplain();
    }
    setDescriptionVisiable((v) => {
      v = !v;
      if (v) {
        moduleClick && moduleClick(props.explainBid);
      }
      return v;
    });
  }, [setDescriptionVisiable, moduleClick, props.explainBid]);

  const onBookmarkClick = React.useCallback(async () => {
    moduleClick && moduleClick(props.bookmarkBid);
    if (!updateBookmarks) return;
    updateBookmarking(true);
    await updateBookmarks(!bookmarked);
    updateBookmarking(false);
  }, [
    updateBookmarking,
    bookmarked,
    updateBookmarks,
    props.bookmarkBid,
    moduleClick,
  ]);

  const downloadButton = showExportButton && extraExportParams && <Button type="link" icon="download" loading={downloading} onClick={download}>导出</Button>;

  let bookmarkButton: React.ReactNode = null;

  if (updateBookmarks) {
    const label = bookmarked ? '取消常用' : '设为常用';
    bookmarkButton = (
      <Button type="link" onClick={onBookmarkClick} loading={bookmarking}>
        <Icon
          type="star"
          theme={bookmarked ? 'filled' : undefined}
          style={{ color: bookmarked ? '#FF6000' : undefined }}
        />
        {label}
      </Button>
    );
  }

  const extraTools = [...tools || []]?.reverse()?.map(t => (
    <Button type="link" onClick={t.onClick} key={t.label}>
      <Icon type={t.icon} />
      {t.label}
    </Button>
  ));

  return (
    <div className="auto-report-header">
      <h1 className="auto-report-header-title">
        {typeof props.title !== 'undefined' ? props.title : report?.props.title}
      </h1>
      <div className="auto-report-header-toolbar">
        {extraButtons}
        {extraTools}
        {downloadButton}
        {!isSubTitle && !hideStar && bookmarkButton}
        {descriptionNode && !isSubTitle && (
          <Button
            type="link"
            icon="question-circle"
            onClick={() => toggleDescription(true)}
          >
            报表说明
          </Button>
        )}
      </div>
      <Modal
        title={explainTitle || tableExplain?.title || '报表说明'}
        cancelButtonProps={{ hidden: true }}
        visible={descriptionVisiable}
        onCancel={() => toggleDescription(false)}
        onOk={() => toggleDescription(false)}
        maskClosable={false}
        destroyOnClose
        className="report-explain-modal"
      >
        {descriptionNode}
      </Modal>
    </div>
  );
}

registorComponent('ReportHeader', ReportHeader);
