/* eslint-disable @typescript-eslint/no-explicit-any */
import Net from '@mtfe/next-biz/es/lib/Net';
import { ReportValueType } from '../../../../types';
import manager from './manager';

/**
 * Auto-Report 的接口请求
 *
 * @param data
 * @returns
 */
export const auto = async (
  value: ReportValueType<any>,
  options: { queryKey: string },
) => {
  const { queryKey } = options;
  const api = `/api/v2/reports/common-api/query/${queryKey}`;
  const req = { reqJson: JSON.stringify({ ...value.query, queryKey }) };
  const resp = await Net.post(api, req);
  return {
    ...value,
    data: JSON.parse(resp),
  };
};

manager.add('auto', auto);

export default auto;
