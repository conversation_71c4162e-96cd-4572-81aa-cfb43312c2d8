import net from '@mtfe/next-biz/es/lib/Net';
import { getService } from '@mtfe/next-biz/es/services/user';
import PoiService from '@mtfe/next-biz/es/services/poi';
import moment from 'moment';
import { min, isNil } from 'lodash';
import { isInBatchGetClearingTimeAb, isInLimitTimeListAb } from '../../../auto-report-v2/src/services/common';
import { IV } from '@mtfe/next-biz/es/utils/type';
import { getSwitchConfigInfoList } from '@mtfe/next-biz/es/services/commonSwitch/index';
import { buildPostApiCustom } from '../../../auto-report-v2/src/services/buildApi';

export const timePermissionsCachesMap = new Map<number, TimeRangeType>();
export interface TimeRangeType {beginTime: number, endTime?: number}

const poiService = new PoiService();

// 查询门店手动日结状态
export const getPoiDailyClearingSetting = async (): Promise<boolean> => {
  const configKeys = ["manualDailyClearingSetting"];
  let { configData: configList } = await getSwitchConfigInfoList(configKeys);
  const manualDailyClearingSetting = configList
    ?.find((i) => i.configKey === "manualDailyClearingSetting")
    ?.configItemInfoList?.find((i: IV) => i.itemKey === "manualDailyClearing");
  const isOpen = manualDailyClearingSetting?.value === "1";
  return isOpen;
};

const chainGetBusinessConfigByPoiIdsNew = buildPostApiCustom<IV, IV[]>("/api/v2/configs/poi/business/multi/daily/clearing/query");

// 批量查询门店手动日结状态
let dailyClearingStatusCache: undefined | boolean;
export const getBatchDailyClearingSetting = async (): Promise<boolean> => {
  if  (!isNil(dailyClearingStatusCache)) {
    return dailyClearingStatusCache;
  }
  const userService = await getService();
  const isChain = userService.isHeadOffice();
  let poiIds = [];
  if (isChain) {
    poiIds = userService.account.managedPoiIds;
  } else {
    poiIds = [userService.org.poiId];
  }
  
  const configKeys = {
    poiIds,
    types: [1728], // 1728 手动日结
    tenantId: userService.account.tenantId,
  };
  const configList = await chainGetBusinessConfigByPoiIdsNew(configKeys);
  dailyClearingStatusCache = !!Object.values(configList)?.some(item => item.manualDailyClearingSetting?.manualDailyClearing === 1);
  return dailyClearingStatusCache;
};

/** 通过权限查询时间范围 */
export const getTimeRangeByPermission = async (code: number): Promise<TimeRangeType> => {
  // return new Promise((resolve) => {
  //   setTimeout(() => resolve({ beginTime: ************* }), 100);
  // });
  const range = timePermissionsCachesMap.get(code);
  if (range) {
    return range;
  }
  try {
    let timeRange = await net.post<string, {code: number}, TimeRangeType>('/api/v1/admin/time/range', {code});
    if (timeRange?.beginTime === -1) { // -1代表不限制
      timeRange.beginTime = 0;
    }
    timePermissionsCachesMap.set(code, timeRange);
    return timeRange;
  } catch (error) {
    return { beginTime: 0 };
  }
}

export enum DefaultDateType {
  YESTERDAY = 'YESTERDAY',
  TODAY = 'TODAY',
  TOMORROW = 'TOMORROW',
}

export const getDefaultDateType = async (): Promise<DefaultDateType> => {
  try {
    const userService = await getService();
    if (userService.isHeadOffice()) {
      const isTll = await isInBatchGetClearingTimeAb();
      if (isTll) {
        return DefaultDateType.TODAY;
      }
      const isOpen = await getBatchDailyClearingSetting();
      if (!isOpen) {
        const poiIds = userService.account.managedPoiIds;
        const items = await poiService.clearingtimeBatchQuery(poiIds);
        // const allIsCurDay = items.every(item => item?.effectiveDay === 1);
        const allIsNextDay = items.every(item => item?.effectiveDay === 0 || isNil(item?.effectiveDay));
        const curDiffSeconds = moment().diff(moment().startOf('day'), 'seconds');
        const times = items.map(item => Number(moment.duration(item.clearTimeStr || '00:00:00').as('seconds')));
        const minTime = min(times) || 0;
        // const maxTime = max(times) || 0;
        // 营业结算时间是次日
        if (allIsNextDay && (minTime > 0 && curDiffSeconds > 0 && curDiffSeconds <= minTime)) {
          return DefaultDateType.YESTERDAY;
        }
        // 营业结算时间是当日
        // if (allIsCurDay && (maxTime > 0 && curDiffSeconds > 0 && curDiffSeconds >= maxTime)) {
        //   return DefaultDateType.TOMORROW;
        // }
        return DefaultDateType.TODAY;
      }
      return DefaultDateType.TODAY;
    }
    return DefaultDateType.TODAY;
  } catch (error) {
    return DefaultDateType.TODAY;
  }
}

export {
  isInLimitTimeListAb
};