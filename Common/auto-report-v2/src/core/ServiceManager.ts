/* eslint-disable @typescript-eslint/no-explicit-any */
import { getService, UserService } from '@mtfe/next-biz/es/services/user';
import PoiService from '@mtfe/next-biz/es/services/poi';
import { checkhasPermission } from '@mtfe/next-biz/es/services/permission';
// @ts-ignore
import { MonitorReport } from '@mtfe/next-biz/es/utils/rmsMonitor';
import { UserType } from '../types';

export class ServiceManager {
  // 导出相关配置
  exportConfig?: {
    loginNameKey: string; // 默认 loginName
    useQueryInfo: boolean; // 默认 true
    useTotalCount: boolean; // 默认 false
  };

  // 指定每页可以显示多少条
  pageSizeOptions?: string[];

  get prerenderData() {
    const {__$isTaxOpen, userInfo } = window as any;
    if (__$isTaxOpen && userInfo) {
      const userService = new UserService(userInfo);
      return {
        user: {
          tenantId: userService?.org?.tenantId,
          poiId: userService?.org?.poiId,
          loginName: userService?.org?.name,
          login: userService?.account?.login,
          isChain: userService?.isHeadOffice(),
        },
        isTaxOpen: __$isTaxOpen || false,
      }
    }
    return null;
  }

  // 获取用户信息
  getUserService: () => Promise<UserType>;

  // 获取价税开关
  queryTaxSwitch: () => Promise<boolean>;

  /**
   * 判断是否有权限
   *
   * @param permissionCode 需要验证的权限code
   * @param passWhileSome 是否至少有一个权限code就算鉴权通过
   */
  checkhasPermission: (permissionCode: any, passWhileSome?: boolean) => Promise<boolean>;

  // 获取默认分页大小
  getDefaultPageSize?: (pageKey: string) => Promise<number>;

  // 设置默认分页大小
  setDefaultPageSize?: (pageKey: string, pageSize: number) => void;

  $get?: (url: string, params: any) => Promise<any>;
  $post?: (url: string, params: any) => Promise<any>;

  message?: any;

  monitor?: MonitorReport;

  constructor() {
    this.getUserService = async () => {
      const userService = await getService();
      return {
        tenantId: userService?.org?.tenantId,
        poiId: userService?.org?.poiId,
        loginName: userService?.org?.name,
        login: userService?.account?.login,
        isChain: userService?.isHeadOffice(),
      };
    };

    this.queryTaxSwitch = async () => {
      const poiService = new PoiService();
      const isTaxOpen = await poiService.queryTaxSwitch();
      return isTaxOpen || false;
    };

    this.checkhasPermission = checkhasPermission;
  }
}

export const manager = new ServiceManager();
export default manager;
