import { StateObject } from '../Contexts';
import { getValueByPath } from '../helper';
import { Config, LinkageType } from '../types';

type ConfigProps = Config & {
  linkages: LinkageType
}

export default function (config: ConfigProps, stateObject: StateObject<any>) {
  const { linkages, ...otherConfig } = config;
  const path = linkages?.condition?.split('.');
  if (path?.length && linkages?.assertion) {
    const v = getValueByPath(path, stateObject);
    const assertion = new Function('value', `return (${linkages?.assertion});`);
    if(!assertion(v)) {
      return {
        type: 'empty'
      };
    }
  }
  return otherConfig;
}