import React from 'react';
import { Notice, toast } from '@mtfe/sjst-ui-next';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import { ObjectRefType, ReportValueType } from '../../../types';
import { AlertProps, ReportAlertProps } from './types';

function Alert(props: AlertProps) {
  return (
    <Notice style={{ borderRadius: 5 }}>
      {props.baseRefObject?.renderManager.render(props.message)}
    </Notice>
  );
}

// 该组件用于展示 report 的消息
const _ReportAlert = (props: ReportAlertProps) => {
  const { baseRefObject, updateStatus } = props;

  const refObject = useRefObject(baseRefObject);
  const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<
    ReportValueType<any>
  >;
  const alert = reportRefObject?.value?.alert;

  React.useEffect(() => {
    if (alert) {
      toast(alert.message, 2);
    }
  }, [alert]);

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return <></>;
};

export const ReportAlert = withAdapter(_ReportAlert, { name: 'ReportAlert' });

export default withAdapter(Alert, { name: 'Alert' });
