import React from 'react';
import ReportService, { ReportInfo } from '@mtfe/next-biz/src/services/reports';
import { useCurrentLocation } from '@mtfe/next-router-v2';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import { BookmarkManager } from '../types/context';

export default function useBookmark(hasParent?: boolean): BookmarkManager {
  const [reports, updateReports] = React.useState<ReportInfo[]>([]);
  const [bookmarks, _updateBookmarks] = React.useState<ReportInfo[]>([]);
  const location = useCurrentLocation();
  const service = React.useMemo(() => new ReportService(), []);

  const init = React.useCallback(async () => {
    const [_reports, _bookmarks] = await Promise.all([
      service.getAll(),
      service.getBookmarks(),
    ]);
    _updateBookmarks(_bookmarks);
    updateReports(_reports);
  }, [service, updateReports, _updateBookmarks]);

  React.useEffect(() => {
    if (hasParent) {
      // 避免一个页面出现多个「常用报表」
      // updateReports(parent.reports);
      // _updateBookmarks(parent.bookmarks);
    } else {
      init();
    }
  }, [hasParent, init, _updateBookmarks, updateReports]);

  /** 当前页面是否已收藏 */
  const bookmarked = React.useMemo(() => !!bookmarks.find(b => b.url.split('#')[1] === location.pathname), [reports, bookmarks, location]);

  /** 更新收藏状态 */
  const updateBookmarks = React.useMemo(() => {
    const report = reports.find(b => b.url.split('#')[1] === location.pathname);
    if (!report) return null;

    return async (enable: boolean) => {
      if (enable) {
        const success = await doAction(service.addBookmark(report.id));
        if (success) {
          _updateBookmarks(b => b.concat(report));
        }
      } else {
        const success = await doAction(service.removeBookmark(report.id));
        if (success) {
          _updateBookmarks(b => b.filter(r => r.id !== report.id));
        }
      }
    };
  }, [reports, location, service]);

  return React.useMemo(() => ({
    reports,
    bookmarks,
    bookmarked,
    updateBookmarks,
  }), [
    reports,
    bookmarks,
    bookmarked,
    updateBookmarks,
  ]);
}
