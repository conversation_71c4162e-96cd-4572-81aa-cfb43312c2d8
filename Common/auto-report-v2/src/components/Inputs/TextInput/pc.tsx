import { values } from 'lodash';
import React, { ChangeEvent } from 'react';
import { Input } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { getQuery } from './helper';
import { TextInputProps } from './types';

const _TextInput = (props: TextInputProps) => {
  const {
    baseRefObject,
    updateStatus,
    value,
    onChange,
    label,
    dataKey,
    ...otherProps
  } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const _onChange = React.useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.value);
    },
    [onChange]
  );

  return React.useMemo(() => {
    return (
      <Input
        allowClear
        placeholder={`请输入${label}`}
        {...otherProps}
        value={value}
        onChange={_onChange}
      />
    );
  }, [value, _onChange, ...values(otherProps)]);
};

_TextInput.prototype.getQuery = getQuery;

_TextInput.prototype.getDisplayValue = (
  value: string,
) => {
  return value?.trim() || '全部';
};

export default withAdapter(_TextInput, { name: 'TextInput' });
