import React from 'react';
import ReactDom from 'react-dom';
import { Button, Icon } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { FullScreenProps } from './types';

import './pc.less';

function FullScreen(props: FullScreenProps) {
  const {
    baseRefObject, updateStatus, enterBid, exitBid,
  } = props;

  const fullscreenDom = React.useRef<HTMLDivElement | null>(null);
  const [isFullScreen, updateIsFullScreen] = React.useState(false);

  const enterFullscreen = React.useCallback(() => {
    const div = document.createElement('div');
    document.body.appendChild(div);
    fullscreenDom.current = div;
    updateIsFullScreen(true);
    baseRefObject?.analytics.moduleClick(enterBid);
  }, [fullscreenDom, updateIsFullScreen, enterBid]);

  const exitFullscreen = React.useCallback(() => {
    const { current } = fullscreenDom;
    if (current) {
      document.body.removeChild(current);
      fullscreenDom.current = null;
    }
    updateIsFullScreen(false);
    baseRefObject?.analytics.moduleClick(exitBid);
  }, [updateIsFullScreen, exitBid]);

  const renderComponent = React.useCallback(
    (element: React.ReactElement) => {
      if (isFullScreen && fullscreenDom.current) {
        return ReactDom.createPortal(
          <div className="auto2-report-page-fullscreen">
            <div
              className="auto2-report-page-fullscreen-title"
              onClick={exitFullscreen}
            >
              <Icon type="arrow-left" /> 返回
            </div>
            {element}
          </div>,
          fullscreenDom.current,
        );
      }
      return element;
    },
    [isFullScreen, fullscreenDom.current, exitFullscreen],
  );

  React.useEffect(() => {
    updateStatus?.('done', {
      renderComponent,
    });
  }, [renderComponent]);

  React.useEffect(() => {
    fullscreenDom.current?.addEventListener('click', (e) => {
      const a = `${window.location.pathname}#${window.location.hash}`;
      // 页面如果 url 发生了变化，取消全屏
      setTimeout(() => {
        if (a !== `${window.location.pathname}#${window.location.hash}`) {
          updateIsFullScreen(false);
        }
      });
    });
  }, [fullscreenDom.current]);

  return (
    <Button type="link" key="FullScreen" onClick={enterFullscreen}>
      {props.children || (
        <>
          <Icon type="fullscreen" />
          <span>全屏展示</span>
        </>
      )}
    </Button>
  );
}

export default withAdapter(FullScreen, { name: 'FullScreen' });
