import React from 'react';
import {
  Row,
} from 'antd';
import {
  RowProps, Props,
} from '../../../types';
import ReportComponent from '../../ReportComponent';
import { registorComponent } from '../registor';
import './Row.less';

export default function RowLayout(props: Props<RowProps>) {
  const { children, ...rest } = props;
  return (
    <Row gutter={24} {...rest} className="auto-report-row">
      <ReportComponent>{children}</ReportComponent>
    </Row>
  );
}

registorComponent('Row', RowLayout);
