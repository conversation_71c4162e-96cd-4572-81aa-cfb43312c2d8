const downlevelDts = require('./downlevel-dts');

// TS错误-node_modules中的需要忽略的npm包
const ignoreNodeModuleList = [
  'node_modules/@mtfe/sjst-antdx-saas',
  'node_modules/@ant-design',
  'node_modules/@saas-pro',
  'node_modules/@rc-component',
  'node_modules/@saas',
];

// 同行错误标识符
const SAME_LINE_MARK = '.\n  ';

// 同行占位符
const SAME_LINE_PLACE_MARK = '#_#';

// 分割标识符
const SPLIT_MARK = '.\n';

/**
 * ts错误过滤器
 * @param {*} error 
 * @returns 
 */
const tsErrorFilter = (error) => {
  // 未命中需要忽略的npm包，不作处理
  if (ignoreNodeModuleList.every(module => !error.includes(module))) {
    return {
      status: false, // 错误状态
      error, // 错误日志
    }
  }

  // 合并错误日志【合并同一个文件的多个ts报错】
  const mergeErrorLog = error.replace(new RegExp(`${SAME_LINE_MARK}`, 'g'), SAME_LINE_PLACE_MARK);

  // 格式化日志为数组
  const mergeErrorLogList = mergeErrorLog.split(SPLIT_MARK).filter(Boolean);

  // 数据异常
  if (!mergeErrorLogList || !mergeErrorLogList.length) {
    return {
      status: false,
      error,
    }
  }

  // 新的日志
  const newLog = mergeErrorLogList
    .filter(errorItem => ignoreNodeModuleList.every(module => !errorItem.includes(module)))
    .map(errorItem => errorItem.replaceAll(new RegExp(`${SAME_LINE_PLACE_MARK}`, 'g'), SAME_LINE_MARK))
    .join(SPLIT_MARK); // 换行展示错误日志

  return {
    status: true, // 标识过滤过TS错误的白名单
    error: newLog ? `${newLog}${SPLIT_MARK}` : '', // 需要增加末尾符号，还原日志
  }
}

// 需要*.dts文件ts版本降级的包。最好目录精确一些，以提高执行效率
const downlevelList = [
  'node_modules/@ant-design/cssinjs',
  'node_modules/@saas-pro/saas-ui-pc/node_modules',
  'node_modules/@rc-component',
  'node_modules/@saas-standard/pc-import-excel',
];

// 将具有高版本语法的*.dts降级为低版本可识别的语法
const dtsDownlevel =() => {
  const start = Date.now();
  for(const item of downlevelList) {
    downlevelDts.main(item, item, '4.1.6');
  }
  const end = Date.now();
  console.log('downLeveldts 完成，耗时：', `${(end - start) / 1000}s`);
};

module.exports = {
  tsErrorFilter,
  dtsDownlevel,
}
