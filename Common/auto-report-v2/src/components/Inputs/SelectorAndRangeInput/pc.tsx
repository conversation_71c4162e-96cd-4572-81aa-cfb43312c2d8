import React from 'react';
import { find, isNil } from 'lodash';
import classNames from 'classnames';
import { Input, Select, InputNumber } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { getQuery } from './helper';
import {
  SelectorAndRangeInputProps,
  SelectorAndRangeInputValue,
} from './types';

import './pc.less';

const SelectorAndRangeInput = (props: SelectorAndRangeInputProps) => {
  const {
    className,
    options,
    fromPlaceholder,
    toPlaceholder,
    updateStatus,
    value,
    onChange,
    noLabelSelect
  } = props;

  const [cache, setCache] = React.useState(value);

  const [onSelected, onFromChange, onToChange] = React.useMemo(() => [
    (v: string) => onChange?.({ ...value, selected: v }),
    (v: number) => onChange?.({ ...value, from: v }),
    (v: number) => onChange?.({ ...value, to: v }),
  ], [value, onChange]);

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);
  return (
    <Input.Group
      className={classNames('selector-and-range-input-group', className)}
      compact
    >
      {noLabelSelect ? '' : <Select value={value?.selected} onChange={onSelected}>
        {options?.map(k => (
          <Select.Option key={k.value} value={k.value}>
            {k.title}
          </Select.Option>
        ))}
      </Select>}
      <InputNumber
        max={isNil(cache?.to) ? undefined : cache?.to}
        value={value?.from}
        placeholder={fromPlaceholder}
        onChange={onFromChange}
        maxLength={15}
        onBlur={() => {
          setCache(value);
        }}
      />
      <Input
        style={{
          width: 30,
          borderLeft: 0,
          borderRight: 0,
          pointerEvents: 'none',
          backgroundColor: '#fff',
        }}
        placeholder="~"
        disabled
      />
      <InputNumber
        min={cache?.from}
        value={value?.to}
        placeholder={toPlaceholder}
        onChange={onToChange}
        maxLength={15}
        onBlur={() => {
          setCache(value);
        }}
      />
    </Input.Group>
  );
};

SelectorAndRangeInput.prototype.getQuery = getQuery;

SelectorAndRangeInput.prototype.getDisplayValue = (
  value: SelectorAndRangeInputValue,
  props?: SelectorAndRangeInputProps,
) => {
  const selectedLabel = `${props?.noLabelSelect ? '' : (`${find(props?.options, o => o.value === value.selected)?.title || ''}：`)}`;
  if (!isNil(value.from) && !isNil(value.to)) {
    return `${selectedLabel}${value.from}~${value.to}`;
  }

  if (!isNil(value.from) && isNil(value.to)) {
    return `${selectedLabel}大于${value.from}`;
  }

  if (isNil(value.from) && !isNil(value.to)) {
    return `${selectedLabel}小于${value.to}`;
  }

  return `${selectedLabel}全部`;
};

export default withAdapter(SelectorAndRangeInput, {
  name: 'SelectorAndRangeInput',
});
