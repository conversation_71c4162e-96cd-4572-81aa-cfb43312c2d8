:root .auto-report-screen-header{
  position: relative;
  height: 95px;
  margin-bottom: 24px;

  .highlight{
    position: absolute;
    top: -2px;
    height: 4px;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, rgba(255,255,255, 0) 0%, rgba(166, 223, 255, 0.88) 50%, rgba(255,255,255,0));
    width: 560px;
    left: 50%;
    transform: translate(-50%);
  }

  .widget-1{
    left: 12px;
  }
  .widget-2{
    right: 12px;
    transform: rotate(180deg) scaleY(-1);;
  }
  .widget-1, .widget-2{
    position: absolute;
    top: 35px;
  }
  .widget-3, .widget-4{
    width: 147px;
    position: absolute;
    top: 0;
  }
  .widget-3{
    left: 0;
  }
  .widget-4{
    right: 0;
    transform: rotate(180deg) scaleY(-1);
  }
  .widget-5, .widget-6{
    position: absolute;
    top: 15px;
    height: 80px;
  }
  .widget-5{
    left: 90px;
  }
  .widget-6{
    right: 90px;
    transform: rotate(180deg) scaleY(-1);
  }
  .widget-7{
    height: 1px;
    position: absolute;
    top: 94px;
    left: 383px;
    right: 383px;
    background: linear-gradient(90deg,rgba(163, 212, 254, 0.32) 0%, rgba(81, 187, 253, 0.8) 50%, rgba(163, 212, 254, 0.32) 100% );
  }
  .bg{
    position: absolute;
    top: 0;
    left: 64px;
    right: 64px;
    display: flex;
    .left1{
      background-image: url('./Slice.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-repeat: repeat-x;
      height: 12px;
      width: 13px;
      transform: scaleX(-1);
    }
    .left2{
      background-image: url('./Slice1.png');
      background-size: contain;
      background-repeat: repeat-x;
      height: 12px;
      flex: 1;
    }
    .left3{
      background-image: url('./Slice3.png');
      background-size: contain;
      background-repeat: no-repeat;
      height: 82px;
      width: 73px;
      transform: scaleX(-1);
    }
    .center{
      background-image: url('./Slice4.png');
      background-size: contain;
      background-repeat: repeat-x;
      height: 82px;
      width: 600px;
      text-align: center;;
      font-size: 36px;
      color: white;
      text-shadow: rgba(113, 76, 217, 0.8) 0 1px 6px;
      line-height: 82px;
    }
    .right3{
      background-image: url('./Slice3.png');
      background-size: contain;
      background-repeat: no-repeat;
      height: 82px;
      width: 73px;
    }
    .right2{
      background-image: url('./Slice1.png');
      background-size: contain;
      background-repeat: repeat-x;
      height: 12px;
      flex: 1;
    }
    .right1{
      background-image: url('./Slice.png');
      background-size: contain;
      background-repeat: no-repeat;
      height: 12px;
      width: 13px;
    }

  }
}