const path = require('path');
const { execSync } = require('child_process');

/**
 * 在根目录执行命令并返回对应的输出
 * @param {string} cmd 命令
 * @returns {string} 执行命令的输出
 */
const execSyncX = (cmd) => execSync(cmd, { cwd: path.resolve(__dirname, '../../') }).toString();

/**
 * 获取暂存区文件列表
 * @returns {Array<string>} 暂存区文件列表
 */
const getCachedFileList = () => {
  const output = execSyncX(`git diff --name-only --cached`);
  return output.split(`\n`).filter(Boolean);
};

/**
 * 检查是否存在 rms-goods 相关的改动
 * @returns {boolean} 是否存在 rms-goods 相关改动
 */
const hasRmsGoodsChanges = () => {
  const cachedFiles = getCachedFileList();
  return cachedFiles.some((file) => file.startsWith('Pages/rms-goods/'));
};

/**
 * 检查Pages/rms-goods/src目录下是否存在非pages目录的改动
 * @returns {Array<string>} 非pages目录的改动文件列表
 */
const checkRmsGoodsNonPagesChanges = () => {
  const cachedFiles = getCachedFileList();
  const rmsGoodsChanges = cachedFiles.filter(
    (file) => file.startsWith('Pages/rms-goods/src/') && !file.startsWith('Pages/rms-goods/src/pages/')
  );

  return rmsGoodsChanges;
};

/**
 * 用户确认函数 - 使用 /dev/tty 直接访问终端
 * @param {string} question 确认问题
 * @returns {boolean} 用户是否确认
 */
const askForConfirmation = (question) => {
  console.log(question);

  try {
    // 先显示提示信息
    process.stdout.write('请输入 (y/Y 确认，其他取消): ');

    // 直接从 /dev/tty 读取用户输入
    const input = execSync('read -r answer < /dev/tty && echo "$answer"', {
      stdio: ['pipe', 'pipe', 'inherit'],
      encoding: 'utf8',
      shell: '/bin/bash',
    }).trim();

    const confirmed = ['y', 'Y'].includes(input);

    if (confirmed) {
      console.log('✅ 确认完成，继续提交流程...');
      return true;
    } else {
      console.log('❌ 提交已取消，请先梳理影响范围后再提交。');
      return false;
    }
  } catch (error) {
    console.error('❌ 读取输入失败:', error.message);
    return false;
  }
};

/**
 * 执行RMS商品系统特定的检查
 * 检查Pages/rms-goods/src目录下非pages目录的改动，需要用户确认影响范围
 * @returns {boolean} 是否通过检查（true: 通过，false: 需要终止提交）
 */
const performRmsGoodsCheck = () => {
  // 首先检查是否存在 rms-goods 相关的改动
  if (!hasRmsGoodsChanges()) {
    return true;
  }

  console.log('🔍 进行rms-goods定制检查...');

  const rmsGoodsNonPagesChanges = checkRmsGoodsNonPagesChanges();

  if (rmsGoodsNonPagesChanges.length === 0) {
    console.log('✅ 没有检测到需要确认的改动，检查通过');
    return true;
  }

  console.warn('\n🔍 检测到以下文件在 Pages/rms-goods/src 目录下（非pages目录）发生了改动：');
  rmsGoodsNonPagesChanges.forEach((file) => console.warn(`  - ${file}`));

  console.warn('\n⚠️  这些改动可能会影响到其他页面或历史功能，请确保已经梳理并确认了影响范围。');

  const confirmed = askForConfirmation('\n请确认您是否已经梳理了影响范围？');

  return confirmed;
};

module.exports = {
  hasRmsGoodsChanges,
  checkRmsGoodsNonPagesChanges,
  askForConfirmation,
  performRmsGoodsCheck,
};
