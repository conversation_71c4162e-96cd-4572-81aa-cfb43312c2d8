/* eslint-disable @typescript-eslint/no-explicit-any */
import { Assign } from 'utility-types';
import React, { HTMLAttributes } from 'react';
import {
  ITableYColumnProps,
  ITableYProps,
} from '@mtfe/sjst-antdx-next/es/table-y';
import {
  PipelineFuncOptionsType,
  PipelineType,
} from '../../../core/PipelineManager';
import { EnvType } from '../../../contexts';
import { ReportValueType, ComponentProps, FieldFormat } from '../../../types';

export type TableValueType<ListRowData> = {
  data?: ListRowData[];
  columns: Array<TableColumnProps<ListRowData>>;
};

export type TableColumnMoreProps<ListRowData> =
  | HTMLAttributes<any>
  | ((
    v: any,
    row?: ListRowData,
    index?: number
  ) => HTMLAttributes<any> | undefined);

export type TableColumnFormatComponentProps<ListRowData> = {
  data: {
    value: any;
    record?: ListRowData;
    index?: number;
  };
};

export interface TableColumnProps<ListRowData>
  extends Pick<
  ITableYColumnProps<ListRowData>,
  | 'key'
  | 'title'
  | 'dataIndex'
  | 'children'
  | 'align'
  | 'hide'
  | 'expandable'
  | 'defaultExpanded'
  | 'help'
  | 'configTitle'
  | 'autoMergeRows'
  | 'fixed'
  | 'className'
  | 'render'
  | 'width'
  | 'sortOrder'
  > {
  /* 处理合并列 */
  customRender?: any;
  /** 是否是维度 auto-report 的数据格式需要该字段来支持动态列 */
  isDim?: boolean;
  /** 子列字段 auto-report 的数据格式需要该字段来判断子列的取值 */
  subField?: string;
  // ---------------------------------------------
  showSorterTooltip?: boolean;
  /** 是否不折行 */
  nowrap?: boolean;
  /** 该字段是否支持排序，默认为 false */
  sorter?: boolean;
  /** 字段设置设置是否可以排序位置，默认为 true */
  sortable?: boolean;
  /** 字段设置是否可以显示/隐藏，默认为 true */
  selectable?: boolean;
  /** 列是否可以拖动，默认为 true，只有在 width 存在的情况下才生效 */
  resizeable?: boolean;
  /** 默认是否显示，默认为 true */
  defaultEnabled?: boolean;
  /** 展开子项显示/隐藏功能，默认为 false */
  expandSelectable?: boolean;
  /** HTMLAttributes 配置和 render 配置 */
  props?: TableColumnMoreProps<ListRowData>;
  format?:
  | FieldFormat
  | { type: string }
  | ((
    v: any,
    row?: ListRowData,
    index?: number,
    isTotal?: boolean,
  ) => React.ReactNode | string | number | boolean | undefined | null);
  children?: Array<TableColumnProps<ListRowData>>;
  field?: string,
  // 自定义导出标题，用于auto导出
  customExportTitle?: string;
}

export interface TablePopelineValueType<ListRowData>
  extends ReportValueType<ListRowData[]> {
  columns: Array<TableColumnProps<ListRowData>>;
}

export interface TableProps<ListRowData>
  extends Assign<
  Omit<ITableYProps<ListRowData>, 'rowKey' | 'columns'>,
  ComponentProps<TableValueType<ListRowData>>
  > {
  rowKey?: string | string[] | ((record: ListRowData) => string);
  columns:
  | Array<TableColumnProps<ListRowData>>
  | ((
    reportValue: ReportValueType<ListRowData[]>,
    env?: EnvType
  ) => Array<TableColumnProps<ListRowData>>);
  pipelines?: PipelineType<
  TablePopelineValueType<ListRowData>,
  PipelineFuncOptionsType
  >[];
  /**
   * 是否直接展示合计行数据，为 true 合计行则直接展示后端返回的数据结果
   */
  directlyTotal?: boolean
  /**
   * 展开 Icon 所在的位置
   */
  expandIconColumnDataIndex?: string | string[],
  indexNoColumnProps?: ((
    row?: ListRowData,
    index?: number
  ) => HTMLAttributes<any> | undefined);
  indexNoColumnFormat?: ((row?: ListRowData, index?: number) => React.ReactNode | string | number | boolean | undefined | null);
  forceUseCurColumns?: boolean;
  filterDataSourceByColumnSetting?: (list: ListRowData[], columnsSettingValue?: TableColumnProps<ListRowData>[]) => ListRowData[];
}

/** auto report 的数据格式 */
export type AutoDataNode = {
  /** 节点的值，只有为叶子节点是有数据 */
  values?: {
    [field: string]: number | string;
  };
  /** 子元素分组依据 */
  groupDims?: {
    [field: string]: number | string;
  };
  /** 子元素的分组统计，最外层为整体合计值 */
  aggr?: {
    [field: string]: number | string;
  };
  /** 子元素 */
  items?: AutoDataNode[];
  page?: {
    pageNo: number;
    pageSize: number;
    totalCount: number;
    totalPageSize: number;
  };
};
