import React from 'react';
import Container from '../../Desktop/Layout/Container';
import { ScreenBlockProps, Props } from '../../../types/config';
import ReportComponent from '../../ReportComponent';

export default function Block8(props: Props<ScreenBlockProps>) {
  const {
    cornerSize, title, type, className,
    ...otherProps
  } = props;
  const width = Number(props.width) || 250;
  const height = Number(props.height) || 50;
  const style: React.CSSProperties = {
    boxSizing: 'border-box',
    background: 'rgba(255,255,255,0.06)',
    position: 'relative',
    display: 'felx',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 'none',
    // fontSize: 24,
    ...otherProps,
    width,
    height,
  };

  const border = React.useMemo(() => {
    const padding = 2.5;
    const dasharray = (width - padding * 2 + height - padding * 2) * 2;
    const path = [
      `M${padding} ${padding}`,
      `L${width - padding} ${padding}`,
      `L${width - padding} ${height - padding}`,
      `L${padding} ${height - padding}`,
      `L${padding} ${padding}`,
    ].join(' ');

    return (
      <>
        <svg
          className="dv-svg-container" style={{
            position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
          }}
        >
          <defs>
            <path id="border-box-8-path-67079e54-a4af-4e5c-8ca1-da99fe0d28ee" d={path} fill="transparent" />
            <radialGradient id="border-box-8-gradient-67079e54-a4af-4e5c-8ca1-da99fe0d28ee" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#fff" stopOpacity="1" />
              <stop offset="100%" stopColor="#fff" stopOpacity="0" />
            </radialGradient>
            <mask id="border-box-8-mask-67079e54-a4af-4e5c-8ca1-da99fe0d28ee">
              <circle opacity="0" cx="0" cy="0" r={Math.min(height, width) - padding * 2} fill="url(#border-box-8-gradient-67079e54-a4af-4e5c-8ca1-da99fe0d28ee)">
                <animateMotion dur="3s" path={path} rotate="auto" repeatCount="3" />
                <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="3" />
              </circle>
            </mask>
          </defs>
          <use strokeWidth="1" xlinkHref="#border-box-8-path-67079e54-a4af-4e5c-8ca1-da99fe0d28ee" stroke="#235fa7" />
          <use strokeWidth="3" xlinkHref="#border-box-8-path-67079e54-a4af-4e5c-8ca1-da99fe0d28ee" mask="url(#border-box-8-mask-67079e54-a4af-4e5c-8ca1-da99fe0d28ee)" stroke="#4fd2dd">
            <animate attributeName="stroke-dasharray" from={`0, ${dasharray}`} to={`${dasharray}, 0`} dur="3s" repeatCount="3" />
          </use>
        </svg>
      </>
    );
  }, [cornerSize]);
  const titleDom = title ? <ReportComponent>{title}</ReportComponent> : null;
  return (
    <Container {...style} className={[className, 'auto-report-screen-block8', 'auto-report-screen-block8-title'].join(' ')}>
      {border}
      {titleDom && <div className="auto-report-block-title">{titleDom}</div>}
      <ReportComponent>{props.children}</ReportComponent>
    </Container>
  );
}
