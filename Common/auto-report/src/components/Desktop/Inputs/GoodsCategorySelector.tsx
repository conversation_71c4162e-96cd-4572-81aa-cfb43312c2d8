import React from 'react';
import {
  ReportGoodsCategorySelector,
  SimplifiedCategory,
} from '@mtfe/next-biz/src/components/GoodsCategory/ReportGoodsCategorySelector';
import {
  ReportGoodsCategorySelectorYJ,
} from '@mtfe/next-biz/src/components/GoodsCategory/ReportGoodsCategorySelectorYJ';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  GoodsCategorySelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: GoodsCategorySelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as GoodsCategorySelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    const { showCateNames = false } = this.config;
    return showCateNames ? (
      <ReportGoodsCategorySelectorYJ
        size={props.size}
        multiple={!!multiple}
        useMultiplePopSearch
        placeholder="全部"
        {...otherProps}
        customFilter={{ ...deps, ...this.config.customFilter }}
      />
    ) : (
      <ReportGoodsCategorySelector
        size={props.size}
        multiple={!!multiple}
        useMultiplePopSearch
        placeholder="全部"
        {...otherProps}
        customFilter={{ ...deps, ...this.config.customFilter }}
      />
    );
  }

  getDisplayValue(goods?: SimplifiedCategory | SimplifiedCategory[]) {
    return toArray(goods)?.map(o => o.name).join('，') || '全部';
  }

  getQuery(goods: SimplifiedCategory | SimplifiedCategory[] | undefined) {
    goods = toArray(goods);
    const firstLev = goods?.filter(g => g.level === 1);
    const secondLev = goods?.filter(g => g.level === 2);

    const queryValues = {
      cateIdList: firstLev?.reduce((r, c) => r.concat(c.ids || []), [] as ID[]),
      secondCateIdList: secondLev?.reduce((r, c) => r.concat(c.ids || []), [] as ID[]),
    }
    if (this.config.showCateNames && goods?.length) {
      const dishCategoryConcatenatedNames = goods?.map(item => item.id)?.filter(Boolean);
      return {
        dishCategoryConcatenatedNames
      }
    }
    return queryValues;
  }
}
