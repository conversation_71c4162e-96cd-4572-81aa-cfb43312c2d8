import * as React from 'react';
import { Alert, Spin } from '@mtfe/sjst-antdx-next';
import { getService } from '@mtfe/next-biz/es/services/user';
import OrgService, { BusinessModule } from '@mtfe/next-biz/es/services/org';
import showUnavailablePoi from '@mtfe/next-biz/es/components/Org/Poi/UnavailablePoi';

import './index.less';

// const { error } = Modal;

const empty = require('./Empty.png');

const msgRegExp = /仅展示服务在有效期内(,|，)?且当前(帐|账)号具有管辖权限的门店数据/;

export const isControlError = (err = '', showExpiredPoi = true): boolean => (showExpiredPoi ? false : msgRegExp.test(err || ''));

export interface ControlTipProps {
  businessModuleId?: number; // 业务id
  tipStyle?: React.CSSProperties;
  className?: string;
  isChainReport?: boolean; // 是否是集团视角特有的页面
  children?: React.ReactNode;
  isOffline?: boolean; // 默认true
}

export interface NodeRes {
  whetherManagedPoi?: boolean; // 是否有管辖范围门店
  whetherControlNoAuthPoi?: boolean; // 是否有管控无权门店
  controlNoAuthPoiCount?: number; // 无权门店数量
  managedNormalPoiCount?: number; // 管辖范围门店状态为正常的数量
}

export interface IState extends NodeRes {
  isChain?: boolean;
  poiAllNotNormal: boolean; // 管辖范围门店状态都不正常
  isLoading: boolean;
}

export class ControlTip extends React.Component<ControlTipProps, IState> {
  state = {
    isChain: true,
    whetherManagedPoi: true,
    whetherControlNoAuthPoi: false,
    controlNoAuthPoiCount: 0,
    poiAllNotNormal: false,
    isLoading: false,
  };

  get businessModuleId() {
    return this.props.businessModuleId ?? BusinessModule.收银报表;
  }

  get isOffline() {
    return this.props.isOffline ?? true;
  }

  componentDidMount() {
    if (!this.isOffline) {
      this.queryOrgData();
    }
  }

  async queryOrgData() {
    const userService = await getService();
    const isChain = userService?.isHeadOffice();
    if (isChain && this.businessModuleId) {
      this.setState({
        isChain,
        isLoading: true,
      });
      const orgService = new OrgService();
      orgService.queryRootOrgForSelector({
        needManagedData: true,
        businessModuleId: this.businessModuleId,
      }, true).then((data: NodeRes) => {
          this.setState({
            isLoading: false,
            whetherManagedPoi: data?.whetherManagedPoi,
            whetherControlNoAuthPoi: data?.whetherControlNoAuthPoi,
            controlNoAuthPoiCount: data?.controlNoAuthPoiCount,
            poiAllNotNormal: data.managedNormalPoiCount === 0,
          });
        })
        .catch((err) => {
          this.setState({
            isLoading: false,
          });
          console.log(err);
          // error({
          //   title: '错误',
          //   content: err?.message || '获取失败',
          // });
        });
    } else {
      this.setState({
        isChain,
      });
    }
  }

  // 打开不可用列表
  showUnableOrg = () => {
    showUnavailablePoi({
      whetherFilteredByManaged: true,
      businessModuleId: this.businessModuleId,
    });
  };

  // 有无权门店
  getTipDom = () => {
    const {
      isChain,
      whetherManagedPoi,
      whetherControlNoAuthPoi,
      controlNoAuthPoiCount,
    } = this.state;
    const { tipStyle } = this.props;
    const tipDom = isChain && whetherManagedPoi && whetherControlNoAuthPoi ? (
      <Alert
        className="control-tip-wrap"
        message={(
          <span>
            {this.props.isChainReport
              ? '统计结果中未包含服务已过期的门店数据'
              : '统计结果中未包含管辖范围内服务已过期的门店数据，可切换到门店视角查询'}
            <span className="control-link-btn" onClick={this.showUnableOrg}>
              查看过期门店
              {Number(controlNoAuthPoiCount) > 0
                ? `(${
                  controlNoAuthPoiCount > 99 ? '99+' : controlNoAuthPoiCount
                })`
                : ''}
            </span>
          </span>
        )}
        type="warning"
        showIcon
        style={tipStyle}
      />
    ) : null;
    return tipDom;
  };

  // 未分配可用管辖机构
  getNoControlDom = () => (
    <div className="no-control-page">
      <img src={empty} />
      <span className="no-control-txt">
        当前帐号未分配可用管辖机构，请联系管理员前往【运营中心-组织机构及帐号-员工管理-员工帐号】设置
      </span>
    </div>
  );

  render() {
    const {
      isChain,
      whetherManagedPoi,
      whetherControlNoAuthPoi,
      poiAllNotNormal,
      isLoading,
    } = this.state;
    const { children } = this.props;
    if (this.isOffline) {
      return <>{children}</>;
    }
    if (
      isChain &&
      (!whetherManagedPoi ||
        (whetherManagedPoi && !whetherControlNoAuthPoi && poiAllNotNormal))
    ) {
      return this.getNoControlDom();
    } else {
      return (
        <>
          {isChain && this.businessModuleId ? this.getTipDom() : ''}
          {isLoading ? <Spin /> : <div>{children}</div>}
        </>
      );
    }
  }
}

export default ControlTip;
