/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Icon, Tooltip } from 'antd';
import { TooltipProps } from 'antd/es/tooltip';
import { Field, FormInstance } from '@mtfe/rmsform-antd';
import { renderBasicMaterial, Materials } from '../materials';
import type { CommonAutoFormProps, IV, Property, Material, FunctionMaterial } from '../type';
import { FieldPropsTransform } from '../utils/component';
import { AutoFormContext } from './context';
import { DataContextType } from './dataContext';

type RenderMaterialProps = {
  material: Material<IV>;
  context: DataContextType<{}>;
  key: string;
  formInstance: FormInstance | null;
};

// 解析物料
export const renderMaterial = (props: RenderMaterialProps) => {
  let dom: JSX.Element = <></>;
  const { material, context, key, formInstance } = props;
  const getFieldValue: FormInstance['getFieldValue'] = fieldKey => formInstance?.getFieldValue(fieldKey);
  const getDom = () => {
    if (typeof material === 'function') {
      let tempDom = material(context, {
        getFieldValue,
      });
      if (typeof tempDom === 'object' && !React.isValidElement(tempDom)) {
        tempDom = renderBasicMaterial(tempDom as Materials);
      }
      return tempDom;
    } else if (typeof material === 'object' && !React.isValidElement(material)) {
      return renderBasicMaterial(material);
    }
    return <></>;
  };
  dom = getDom();
  return (
    // 将最外层接收的value onChaneg属性转移到物料上，否则consumer会调至属性丢失，表单无法初始化和值变化监听
    <FieldPropsTransform>
      {({ value, onChange }) => (
        <AutoFormContext.Consumer>
          {({ registerFormRef, unregistterFormRef }) => {
            const newProps = getDom()?.props;
            const refProps =
              dom && dom.type.isAutoForm
                ? {
                    forwardedRef: (v: any) => {
                      if (!(dom && dom.type.isAutoForm)) {
                        return;
                      }
                      if (v && registerFormRef) {
                        registerFormRef(key, v);
                      } else if (!v && unregistterFormRef) {
                        unregistterFormRef(key);
                      }
                    },
                  }
                : {};
            const element = React.cloneElement(dom, {
              // ref: (v: any) => {
              //   if (!(dom && dom.type.isAutoForm)) {
              //     return;
              //   }
              //   console.log('isAutoForm');
              //   if (v && registerFormRef) {
              //     registerFormRef(key, v);
              //   } else if (!v && unregistterFormRef) {
              //     unregistterFormRef(key);
              //   }
              // },
              ...refProps,
              value,
              onChange,
              ...newProps,
            });
            return element;
          }}
        </AutoFormContext.Consumer>
      )}
    </FieldPropsTransform>
  );
};

type WrapProps = {
  properties: CommonAutoFormProps['properties'];
  context: DataContextType<{}>;
  formRef: React.RefObject<FormInstance>;
};

/**
 * 为label添加问号说明文案
 */
export const addHelpForLabel = (props: { label: React.ReactNode; help: Property<IV>['help'] }) => {
  const { label, help } = props;
  let helpProps: TooltipProps = {};
  if (typeof help === 'string') {
    helpProps.title = help;
  } else if (typeof help === 'object') {
    helpProps = { ...help };
  }
  const labelDom = help ? (
    <span>
      {label}&nbsp;
      <Tooltip {...helpProps}>
        <Icon type="question-circle-o" />
      </Tooltip>
    </span>
  ) : (
    label
  );
  return labelDom;
};

export function getLabel<D>(
  label: React.ReactNode | FunctionMaterial<IV>,
  options: { context: DataContextType<{}>; formInstance: FormInstance | null },
) {
  const { context, formInstance } = options;
  const getFieldValue = formInstance?.getFieldValue!;
  if (typeof label === 'function') {
    return label(context, { getFieldValue });
  }
  return label;
}

/**
 * 将物料用Fiedle方法包裹
 */
export const wrapMaterialWithField = (props: WrapProps) => {
  const { properties, context, formRef } = props;
  const map: { [i: string]: JSX.Element | null } = {};
  Object.entries(properties)
    // .filter(([_, value]) => {
    //   value = typeof value === 'function' ? value(context) : value;
    //   // 过滤隐藏的表单项
    //   return !value.hidden;
    // })
    .forEach(([key, _value]) => {
      const value = typeof _value === 'function' ? _value(context) : _value;
      const { fieldProps, material, label, wrapper, ifUpdate, required, checkers, extra, help, slot } = value;
      const getField = (formInstance: FormInstance | null) => {
        // 获取联动更新后的 hidden 值
        const isHidden = typeof value.hidden === 'function' ? value.hidden(context, formInstance) : value.hidden;
        if (isHidden) {
          return null;
        }
        const _label = getLabel(label, { context, formInstance });
        const labelDom = addHelpForLabel({ help, label: _label });
        return (
          <Field
            {...fieldProps}
            label={labelDom}
            name={key}
            key={key}
            required={required}
            checkers={checkers}
            info={extra}
          >
            {renderMaterial({
              context,
              material,
              key,
              formInstance,
            })}
          </Field>
        );
      };
      const field = wrapper ? (
        <Field noStyle ifUpdate={ifUpdate}>
          {i => {
             const isHidden = typeof value.hidden === 'function' ? value.hidden(context, i) : value.hidden;
             if (isHidden) {
               return null;
             }
            return wrapper(i, getField(i));
          }}
        </Field>
      ) : (
        getField(formRef.current)
      );
      map[key] = slot ? slot(field) : field;
    });
  return map;
};
