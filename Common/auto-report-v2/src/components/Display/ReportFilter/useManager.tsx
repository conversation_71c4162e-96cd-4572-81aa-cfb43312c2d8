/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Button, Modal, Form, Input, Tag, message } from '@mtfe/sjst-antdx-next';
import { FormComponentProps, } from 'antd/lib/form';
import Net from '@mtfe/next-biz/es/lib/Net';
import { dataContext } from '../../../contexts';
import { BaseObjectRefType, Config, ObjectRefType } from '../../../types';

const defaultInputState = {
  input: '',
  visible: false,
  confirmLoading: false,
};

export const getName = (name: string, items: string[], index: number = 1): string => {
  const n = `${name}${index < 10 ? `0${index}` : index}`;
  if (items.indexOf(n) === -1) {
    return n;
  }
  return getName(name, items, index + 1);
}

export const NameInput = Form.create({
  mapPropsToFields(props: any) {
    return {
      schemeName: Form.createFormField({
        value: props.value,
      })
    }
  }
})(
  (
    props: FormComponentProps & {
      value?: string;
      onChange: (e: React.ChangeEvent) => void;
    }
  ) => {
    const { onChange } = props;
    const { getFieldDecorator } = props.form;
    return (
      <Form>
        <Form.Item label="查询方案名称：">
          {getFieldDecorator('schemeName', {
            rules: [{ max: 12, message: '名称长度最多12个字!' }],
          })(<Input onChange={onChange} placeholder="请输入查询方案名称" />)}
        </Form.Item>
      </Form>
    );
  }
);

export const useManager = (
  items: Config[],
  refObject?: BaseObjectRefType<any>,
  $$auto?: any
) => {
  const { env = $$auto?.$global?.env } = React.useContext(dataContext);
  const nameInputRef = React.createRef<any>();

  const reportProps =
    refObject?.version === 2
      ? $$auto.withParentName('Report')?.$self.otherProps
      : (refObject as ObjectRefType<any>)?.withParentName('Report')?.otherProps;

  const dimensionRef = React.useRef(
    reportProps.queryManageKey
      ? `${env.user.tenantId}-${env.user.poiId}-${env.user.login}-${env.isChain ? '1' : '0'}-${
          env.os
        }-${reportProps.queryManageKey}`
      : ''
  );

  const [state, setState] = React.useState<any[]>([]);
  const [selectedId, setSelectedId] =  React.useState<String>();
  const [inputState, setInputState] = React.useState(defaultInputState);

  const openManageModal = React.useCallback(
    () => {
      setInputState({ ...defaultInputState, input: getName('查询方案', state.map(v => v.schemeName), 1), visible: true });
      reportProps?.queryManageSaveCallback?.(refObject);
    },
    [state]
  );

  const closeManageModal = React.useCallback(
    () => setInputState(defaultInputState),
    []
  );

  const create = React.useCallback(() => {
    if (!nameInputRef.current) {
      return;
    }

    nameInputRef.current.validateFields(async (errors: any) => {
      if (!errors) {
        setInputState((p) => ({ ...p, confirmLoading: true }));
        const name = inputState.input;
        const queryContent = items.reduce((query: any, item) => {
          if (item.type !== 'DatePicker' && item.props?.dataKey) {
            const queryObject = (refObject as ObjectRefType<any>)?.withDataKey(
              item.props.dataKey
            );
            if (queryObject?.value) {
              query[item.props.dataKey] = queryObject.value;
            }
          }
          return query;
        }, {});

        if (state.length >= 5) {
          message.error('最多创建5个筛选方案');
          setInputState((p) => ({ ...p, confirmLoading: false }));
        } else if (state.findIndex(v => v.schemeName === name) !== -1) {
          message.error('查询方案名称重复，请重新输入');
          setInputState((p) => ({ ...p, confirmLoading: false }));
        } else {
          const newData = {
            dimension: dimensionRef.current,
            schemeName: name,
            schemeContent: JSON.stringify(queryContent),
          };
          try {
            const res = await Net.post(
              '/api/v1/admin/report-view-scheme/create',
              newData,
            );
            if (res.id) {
              setState(state.concat([
                {
                  ...newData,
                  schemeId: res.id,
                },
              ]));
              closeManageModal();
              setQuery(res.id, newData.schemeContent);
            } else {
              message.error('创建失败');
              setInputState((p) => ({ ...p, confirmLoading: false }));
            }
          } catch (e) {
            message.error(e.message || '创建失败');
            setInputState((p) => ({ ...p, confirmLoading: false }));
          }
        }
      }
    });
  }, [inputState, state]);

  const remove = React.useCallback(async (id: string) => {
    if (selectedId === id) {
      setSelectedId(undefined);
    }
    await Net.post(
      '/api/v1/admin/report-view-scheme/delete',
      {
        dimension: dimensionRef.current,
        schemeId: id,
      },
    );
    setState((preState) => {
      return preState.filter((v) => v.schemeId !== id);
    });
  }, [selectedId]);

  const setQuery = React.useCallback((id?: string, queryContentText?: string) => {
    try {
      setSelectedId(id)
      refObject?.emit('submit@Report', {
        query: queryContentText ? JSON.parse(queryContentText) : undefined,
        isQuick: true,
      });
    } catch (e) {
      console.error(e);
    }
  }, []);

  const queryManageList = React.useMemo(() => {
    if (!state.length) {
      return <React.Fragment />;
    }

    return (
      <div className="auto2-query-manage-tags">
        <Tag onClick={() => setQuery()} className={!selectedId ? 'active' : ''}>默认</Tag>
        {state.map((s, i) => {
          return (
            <Tag
              key={i}
              className={selectedId === s.schemeId ? 'active' : ''}
              closable
              onClick={() => setQuery(s.schemeId, s.schemeContent)}
              onClose={(e: Event) => {
                e.preventDefault();
                e.stopPropagation();
                Modal.confirm({
                  title: <div>是否删除此查询方案？</div>,
                  onOk: async () => {
                    await remove(s.schemeId);
                  },
                });
              }}
            >
              {s.schemeName}
            </Tag>
          );
        })}
      </div>
    );
  }, [state, selectedId]);

  const queryManageButton = React.useMemo(() => {
    if (!reportProps.queryManageKey) {
      return <React.Fragment />;
    }
    return (
      <>
        <Button
          onClick={openManageModal}
          className="auto2-query-manage-button"
          icon={'save'}
        >
          保存为常用查询
        </Button>
        <Modal
          title="保存查询方案"
          visible={inputState.visible}
          onCancel={closeManageModal}
          onOk={create}
          okButtonProps={{
            disabled: !inputState.input.trim(),
          }}
          confirmLoading={inputState.confirmLoading}
          maskClosable={false}
          className="report-query-manage-modal"
        >
          <NameInput
            ref={nameInputRef}
            // @ts-ignore
            value={inputState.input}
            onChange={(e: any) => {
              const value = e.target?.value;
              setInputState((p) => ({ ...p, input: value }));
            }}
          />
        </Modal>
      </>
    );
  }, [inputState, openManageModal]);

  React.useEffect(() => {
    // 宏任务中执行，不要影响主流程
    setTimeout(async () => {
      if (!dimensionRef.current) {
        return;
      }
      const res = await Net.post(
        '/api/v1/admin/report-view-scheme/list',
        {
          dimension: dimensionRef.current
        },
      );
      if (res?.schemes?.length) {
        setState(res.schemes);
      }
    }, 100);
  }, []);

  return {
    setSelectedId,
    queryManageList,
    queryManageButton,
  };
};

export default useManager;
