import React from 'react';
import {
  TableColumnConfig,
  DataNode,
} from '../../../../../types';
import { Row, Cell } from './types';

type Props = {
  isTreeTable: boolean,
  isCrossTable: boolean,
  rootNode: DataNode | undefined | null,
  columns: TableColumnConfig[],
  showIndex: boolean | undefined,
};

export function isEmptyGroupDims(groupDims?: DataNode['groupDims']) {
  if (!groupDims) return true;
  const values = Object.values(groupDims);
  if (!values.length) return true;
  return values.find(v => v !== null) === undefined;
}

export function isEmptyObject(object?: DataNode['values'] | DataNode['aggr']) {
  if (!object) return true;
  const keys = Object.keys(object);
  if (!keys.length) return true;
  return false;
}

export const flattenNode = (
  node: DataNode,
  column: TableColumnConfig,
  parentKey?: string,
): {[k: string]: string | number } => {
  const {
    values, aggr, groupDims, items,
  } = node;
  let key = groupDims ? `${column.field}[${groupDims[column.field || '']}]` : '';
  if (parentKey) {
    key = `${parentKey}-${key}`;
  }

  const result: {[k: string]: string | number} = {};
  Object.entries(
    { ...values, ...aggr },
  ).forEach(([k, v]: [string, string | number]) => {
    result[`${key}-${k}`] = v;
  });

  if (items) {
    column.children?.forEach((_column: TableColumnConfig) => {
      items.forEach((item: DataNode) => {
        const r = flattenNode(item, _column, key);
        Object.assign(result, r);
      });
    });
  }

  return result;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function makeCell(data: any, rowSpan: number, colSpan: number, bold: boolean): Cell {
  return {
    data,
    rowSpan,
    colSpan,
    bold,
  };
}

type DimSet = {
  [k: string]: string | number,
}

export function cleanTree(node: DataNode, parentDims: DimSet = {}): DataNode {
  const { groupDims } = node;
  let { items } = node;
  let newDims: DimSet | undefined = {};
  Object.entries(groupDims || {}).forEach(([k, v]: [string, string | number]) => {
    if (k in parentDims) return;
    if (!newDims) return;
    newDims[k] = v;
  });
  if (items) {
    const keys = { ...parentDims, ...groupDims };
    items = items.map((n: DataNode) => cleanTree(n, keys));
  }
  if (!Object.keys(newDims || {}).length) {
    newDims = groupDims;
  }
  return { ...node, groupDims: newDims, items };
}

/** 将返回的树状数据扁平化，交叉表专用 */
export const transformNodeForCrossTable = (
  node: DataNode,
  _columns: TableColumnConfig[],
  /** 强制扁平化，忽略维度，拍平所有层级 */
  forceFlatten?: boolean,
): [DataNode, boolean] => {
  if (!_columns.length) return [node, false];
  const { groupDims, items } = node;
  const machedColumn = groupDims && !isEmptyGroupDims(groupDims) && _columns.find(column => (column.field || '') in groupDims && column.children?.length);
  if (
    groupDims
    && machedColumn
  ) {
    node = {
      values: flattenNode(node, machedColumn, ''),
    };
    return [node, true];
  } else if (items) {
    if (machedColumn) {
      _columns = _columns.filter(c => c !== machedColumn);
    }
    const children: DataNode[] = [];
    items.forEach((item: DataNode) => {
      const [child, isDyn] = transformNodeForCrossTable(item, _columns, forceFlatten);
      if (isDyn || forceFlatten) {
        node = { ...node, values: { ...node.values, ...child.values } };
      } else {
        children.push(child);
      }
    });
    node = { ...node, items: children };
  }

  return [node, false];
};

export default (props: Props) => {
  const {
    isCrossTable,
    rootNode,
    columns,
    isTreeTable,
  } = props;
  const id = React.useRef(1);
  /** 获取合计行 */
  const getTotalRow = React.useCallback((
    aggr: {
      [field: string]: string | number;
    } | undefined,
    firstChild: Row,
    subGroupDims: string[],
    /** 合计字段所在的列 */
    column: TableColumnConfig | null,
    isRootLayer: boolean,
  ) => {
    if (aggr && !isCrossTable) {
      const totalRow: Row = {};
      totalRow.isTotalRow = true;
      // 填充空白数据
      Object.entries(firstChild).forEach(([k, v]) => {
        if (k === 'children') return;
        totalRow[k] = makeCell(undefined, 1, 1, true);
      });
      // 填充汇总数据
      Object.entries(aggr).forEach(([k, v]) => {
        totalRow[k] = makeCell(v, 1, 1, true);
      });
      subGroupDims.forEach((dim) => {
        totalRow[dim] = makeCell(undefined, 0, 1, false);
      });
      if (props.showIndex && isRootLayer) {
        // 汇总行，是在「序号」列展示「合计」
        totalRow.rootIndex = makeCell('合计', 1, 1, true);
      } else {
        // 分组汇总行，在当前维度列展示「合计」，序号列为空
        totalRow[column?.alias || column?.field || ''] = makeCell('合计', 1, 1, true);
        totalRow.rootIndex = makeCell(undefined, isTreeTable || isRootLayer ? 1 : 0, isTreeTable || isRootLayer ? 1 : 0, true);
      }
      totalRow.id = `${id.current++}-${Date.now().toString(36)}`;
      return totalRow;
    }
    return null;
  }, [isCrossTable, props.showIndex]);

  type TransformDataReduceResult = {
    rows: Row[],
    columns: TableColumnConfig[],
    groupDims: string[],
    deepth: number,
    showTotalRow: boolean,
    parentIndex: number[],
  }
  /** 分组表专用数据转换，主要功能是设置每组的合计 */
  const transformData = React.useCallback((
    result: TransformDataReduceResult,
    node: DataNode,
    index: number,
  ): TransformDataReduceResult => {
    const {
      values, aggr, items,
    } = node;
    const groupDims = isEmptyGroupDims(node.groupDims) ? {} : node.groupDims;
    const groupDimsKeys = Object.keys(groupDims || {});
    const isRootLayer = result.deepth === 0;
    const isFirstLayer = result.deepth === 1;
    const columnMap: {[k: string]: TableColumnConfig} = {};
    result.columns.forEach((c) => {
      columnMap[c.field || ''] = c;
    });
    const aliasField = (row: Row) => {
      Object.keys(row).forEach((k) => {
        const column = columnMap[k];
        const alias = column && column.alias;
        if (!alias) return;
        const data = row[alias as string]?.data;
        const cell = row[k];
        if (cell) {
          cell.data = data;
        }
      });
    };
    /** 获取当前数据行 */
    const getCurrentRow = () => {
      // 如果 values 和 aggr 为 null 或控对象 且 groupDims 的值都是 null， 则返回 null
      if (isEmptyObject(values) && isEmptyObject(aggr)) {
        if (isEmptyGroupDims(groupDims)) return null;
      }
      const row: Row = {
        rootIndex: (isRootLayer || isFirstLayer) ? makeCell(index + 1, 1, 1, false) : makeCell(undefined, 0, 0, false),
      };
      row.id = `${id.current++}-${Date.now().toString(36)}`;
      const set = { ...values, ...aggr, ...groupDims };
      Object.entries(set).forEach(([k, v]: [string, string | number | undefined]) => {
        if (result.groupDims.includes(k)) {
          row[k] = makeCell(undefined, 0, 1, false);
        } else {
          row[k] = makeCell(v, 1, 1, false);
        }
      });
      return row;
    };
    /** 分组数据处理 */
    if (items && items.length) {
      const currentColumnIndex = result.columns.findIndex(c => groupDimsKeys.includes(c.field || ''));
      // const column = currentColumnIndex >= 0 ? result.columns[currentColumnIndex] : undefined;
      const childrenColumns = currentColumnIndex === -1 ? result.columns : result.columns.slice(currentColumnIndex).filter(c => !groupDimsKeys.includes(c.field || ''));
      const childrenGroupDims = result.groupDims.concat(Object.keys(groupDims || {}));
      const { rows } = items.reduce(transformData, {
        rows: [],
        columns: childrenColumns, // 分组列
        groupDims: childrenGroupDims, // 上层维度
        deepth: result.deepth + 1, // 树深度
        showTotalRow: true,
        parentIndex: [...result.parentIndex, index + 1],
      });
      const firstChild = rows[0];
      const totalRow = childrenColumns[0] && result.showTotalRow && getTotalRow(aggr, firstChild, childrenGroupDims, childrenColumns[0], isRootLayer);
      if (totalRow) rows.push(totalRow);
      // 分组逻辑
      Object.entries(groupDims || {}).forEach(([k, v]) => {
        if (result.groupDims.includes(k)) return;
        firstChild[k] = makeCell(v, rows.length, 1, false);
      });
      if (!isRootLayer) {
        firstChild.rootIndex = isFirstLayer ? makeCell(index + 1, rows.length, 1, false) : makeCell(undefined, 0, 0, false);
      }
      rows.forEach(aliasField);
      result.rows = result.rows.concat(rows);
    } else if (values || aggr || groupDims) {
      const row = getCurrentRow();
      if (row) {
        aliasField(row);
        result.rows = result.rows.concat(row);
      }
    }
    return result;
  }, [getTotalRow, props.isTreeTable]);

  /** 树表专用数据转换，主要功能是设置每组的合计 */
  const transformTreeTableData = React.useCallback((
    result: TransformDataReduceResult,
    node: DataNode,
    index: number,
  ): TransformDataReduceResult => {
    const {
      values, aggr, items,
    } = node;
    const groupDims = isEmptyGroupDims(node.groupDims) ? {} : node.groupDims;
    const groupDimsKeys = Object.keys(groupDims || {});
    const columnMap: {[k: string]: TableColumnConfig} = {};
    result.columns.forEach((c) => {
      columnMap[c.field || ''] = c;
    });
    const aliasField = (row: Row) => {
      Object.keys(row).forEach((k) => {
        const column = columnMap[k];
        const alias = column && column.alias;
        if (!alias) return;
        const data = row[alias as string]?.data;
        const cell = row[k];
        if (cell) {
          cell.data = data;
        }
      });
      if (row.children) {
        row.children.map(aliasField);
      }
    };
    /** 获取当前数据行 */
    const getCurrentRow = () => {
      // 如果 values 和 aggr 为 null 或控对象 且 groupDims 的值都是 null， 则返回 null
      if (isEmptyObject(values) && isEmptyObject(aggr)) {
        if (isEmptyGroupDims(groupDims)) return null;
      }
      const row: Row = { };
      row.id = `${id.current++}-${Date.now().toString(36)}`;
      const set = { ...values, ...aggr, ...groupDims };
      Object.entries(set).forEach(([k, v]: [string, string | number | undefined | null]) => {
        if (result.groupDims.includes(k)) {
          row[k] = makeCell(undefined, 0, 1, false);
        } else {
          row[k] = makeCell(v, 1, 1, false);
        }
      });
      return row;
    };
    /** 分组数据处理 */
    if (items && items.length) {
      const column = result.columns.find(c => groupDimsKeys.includes(c.field || '') && c.subField);
      const childrenColumns = result.columns.filter(c => !groupDimsKeys.includes(c.field || ''));
      const { subField } = column || {};
      if (subField && column) {
        childrenColumns.unshift({
          ...column,
          field: subField,
          subField: undefined,
        });
      }
      const childrenGroupDims = result.groupDims.concat(Object.keys(groupDims || {}));
      let { rows } = items.reduce(transformTreeTableData, {
        rows: [],
        columns: childrenColumns, // 分组列
        groupDims: childrenGroupDims, // 上层维度
        deepth: result.deepth + 1, // 树深度
        showTotalRow: !subField,
        parentIndex: [...result.parentIndex, index + 1],
      });
      const firstChild = rows[0];
      if (result.deepth === 0) {
        const totalRow = getTotalRow(aggr, firstChild, childrenGroupDims, result.columns[0], true);
        totalRow && rows.push(totalRow);
      }
      if (subField && column) {
        // 行展开逻辑
        const row = getCurrentRow();
        if (row) {
          row.isTotalRow = 'tree-total-row';
          // row[column.field || ''] = makeCell(firstChild[column.field || '']?.data, 1, 1, false);
          rows.forEach((r: Row) => {
            // 置换 field 和 subField
            Object.entries(groupDims || {}).forEach(([k, v]) => {
              r[k] = makeCell(v, 1, 1, false);
            });
            r[column.field || ''] = r[subField];
          });
          row.children = rows;
          rows = [row];
        }
      } else {
        // 分组逻辑
        Object.entries(groupDims || {}).forEach(([k, v]) => {
          if (result.groupDims.includes(k)) return;
          if (props.isTreeTable) {
            const iter = (_rows: Row[]) => {
              _rows.forEach((r) => {
                if (r.children) {
                  iter(r.children);
                }
                r[k] = makeCell(v, 1, 1, false);
              });
            };
            iter(rows);
          } else {
            firstChild[k] = makeCell(v, rows.length, 1, false);
          }
        });
      }
      rows.forEach(aliasField);
      result.rows = result.rows.concat(rows);
    } else if (values || aggr || groupDims) {
      const row = getCurrentRow();
      if (row) {
        aliasField(row);
        result.rows = result.rows.concat(row);
      }
    }
    if (result.deepth === 0) {
      const iter = (row: Row, key: number[]) => {
        row.rootIndex = row.rootIndex || makeCell(key.join('-'), 1, 1, false);
        if (row.children) {
          row.children.forEach((r, i) => iter(r, [...key, i + 1]));
        }
      };
      result.rows.forEach((r, i) => iter(r, [i + 1]));
    }
    return result;
  }, [getTotalRow, props.isTreeTable]);

  /**
   * 表格的数据
   */
  const rows = React.useMemo(() => {
    let _rootNode = rootNode && cleanTree(rootNode);
    id.current = 1;
    if (_rootNode) {
      if (isCrossTable) {
        [_rootNode] = transformNodeForCrossTable(_rootNode, columns);
      }
      const result = {
        rows: [],
        columns,
        groupDims: [],
        deepth: 0,
        showTotalRow: true,
        parentIndex: [],
      };
      if (isTreeTable) {
        return [_rootNode].reduce(transformTreeTableData, result).rows;
      } else {
        return [_rootNode].reduce(transformData, result).rows;
      }
    } else {
      return null;
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rootNode, columns, transformData, transformNodeForCrossTable, isCrossTable, isTreeTable]);

  return { rows };
};
