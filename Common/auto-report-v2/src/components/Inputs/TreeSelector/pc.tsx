/* eslint-disable @typescript-eslint/no-explicit-any */
import { values } from 'lodash';
import React from 'react';
import classNames from 'classnames';
import { TreeSelect, Input } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { TreeSelectorProps, TreeOption } from './types';
import { ID } from '../../../types';

import './pc.less';

const nodeSetPaths: ((nodes: TreeOption[], paths?: String[]) => TreeOption[]) = (nodes, paths) => {
  return (nodes || []).map((v => {
    return {
      ...v,
      paths: paths?.length ? paths : undefined,
      children: v.children?.length ? nodeSetPaths(v.children, (paths || []).concat(v.title || '')) : v.children,
    };
  }));
};

const filterOptions = (
  keyWord: string,
  options: TreeOption[]
): TreeOption[] => {
  return options
    .map((v) => ({
      ...v,
      children: v.children?.length
        ? filterOptions(keyWord, v.children)
        : v.children,
    }))
    .filter((v) => {
      if (!keyWord) {
        return true;
      }
      return v.title.includes(keyWord) || v.children?.length;
    });
};

const getValues = (ids: ID[], options: TreeOption[]): TreeOption[] => {
  let values: TreeOption[] = [];
  options.forEach((v) => {
    if (ids.includes(v.value)) {
      values.push(v);
    }
    if (v.children?.length) {
      values = values.concat(getValues(ids, v.children));
    }
  });
  return values;
};

const TreeSelector = (props: TreeSelectorProps) => {
  const {
    dropdownClassName,
    placeholder,
    options,
    multiple = true,
    defaultValue,
    updateStatus,
    baseRefObject,
    value,
    onChange,
    header,
    ...otherProps
  } = props;

  const [keyWord, setKeyWord] = React.useState('');

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const multipleData = React.useMemo(() => {
    return {
      multiple,
      renderSearch: multiple || header
      ? () => {
          return (
            <>
              {header}
              {
                multiple && <div className="biz-pop-search-warp">
                  <Input
                    className="search-input"
                    placeholder="搜索"
                    allowClear
                    onChange={(et) => {
                      const { value } = et.target;
                      setKeyWord(value);
                    }}
                  />
                </div>
              }
            </>
          );
        }
      : undefined
    };
  }, [multiple]);

  const valueData = React.useMemo(() => {
    return {
      value,
      options,
      treeData: nodeSetPaths(options) as any,
      maxTagCount: value && value.length > 1 ? 0 : 1,
      maxTagPlaceholder: `已选${(value || []).length}个`,
      xValue: getValues(value || [], options),
    }
  }, [value, options])

  const filterTreeNode = React.useCallback((inputValue, node) => {
    if (node.props.title.includes(inputValue)) {
      return true;
    }
    const paths = node.props.paths || [];
    for (let i = 0; i < paths.length; i++) {
      const pathName = paths[i];
      if (pathName.includes(inputValue)) {
        return true;
      }
    }
    return false;
  }, []);

  const _onChange = React.useCallback((_value: any) => {
    onChange?.(
      Array.isArray(_value) ? _value.map((v) => v?.value) : [_value?.value]
    );
  }, [onChange]);

  return React.useMemo(() => {
    return (
      <TreeSelect
        // @ts-ignore
        renderSearch={multipleData.renderSearch}
        treeData={valueData.treeData}
        showArrow
        searchValue={keyWord}
        showSearch={false}
        allowClear
        treeCheckable={multipleData.multiple}
        multiple={multipleData.multiple}
        dropdownMatchSelectWidth={false}
        maxTagCount={valueData.maxTagCount}
        maxTagPlaceholder={valueData.maxTagPlaceholder}
        {...otherProps}
        treeNodeFilterProp="title"
        filterTreeNode={filterTreeNode}
        className={classNames('auto-report-select', props.className)}
        placeholder={placeholder ?? '全部'}
        dropdownClassName={classNames(
          'auto-report-select-dropdown',
          dropdownClassName
        )}
        labelInValue
        value={valueData.xValue}
        onChange={_onChange}
      />
    );
  }, [multipleData, valueData, keyWord, dropdownClassName, placeholder, _onChange, ...values(otherProps)])
};

TreeSelector.prototype.getDisplayValue = (
  value: ID[],
  props?: TreeSelectorProps
) => {
  if (value?.length > 1) {
    return `已选${value.length}个`
  }
  const display = value
    ? getValues(value, props?.options || [])
        ?.map((o) => o.title)
        .join('，')
    : '全部';
  return display || '全部';
};

export default withAdapter(TreeSelector, { name: 'TreeSelector' });
