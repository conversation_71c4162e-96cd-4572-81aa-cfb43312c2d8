import { useEffect, useState } from 'react';
import { ua } from '@mtfe/next-biz/src/utils/ua';
import { isInBatchGetClearingTimeAb } from '../../../auto-report-v2/src/services/common';
import { getBatchDailyClearingSetting } from '../services/common';

export function useManualDailyClearingSetting() {
  const [isOpenManualDailyClearing, setIsOpenManualDailyClearing] = useState<boolean>(false);
  useEffect(() => {
    (async () => {
      const isTll = await isInBatchGetClearingTimeAb();
      if (ua.os == 'pc' && !isTll) {
        const isOpen = await getBatchDailyClearingSetting();
        setIsOpenManualDailyClearing(isOpen);
      }
    })();
  }, []);
  return isOpenManualDailyClearing;
}
