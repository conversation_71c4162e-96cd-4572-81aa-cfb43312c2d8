/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { isNil } from 'lodash';
import Net from '@mtfe/next-biz/src/lib/Net';
import { UserContext } from '@mtfe/next-biz/src/contexts/user';
import { reportClickQuery, reportQueryRenderTime, reportSuccessQuery } from '@mtfe/next-biz/es/utils/reportQueryRenderLog';
import PoiService from '@mtfe/next-biz/src/services/poi';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import { ReportProps } from '../types/config';
import { RequestManager } from '../types/context';
import { DataNode } from '../types/response';
import { checkTimePermission } from '../utils/common';
import { pushReportOperationsLog, isInYJResetApiErrorAbAndChain } from '../../../auto-report-v2/src/services/common';
import { resetYJErrorMsg } from '../../../auto-report-v2/src/helper/utils';

/**
 * 网络请求管理
 */
export default function useRequestManager(config: ReportProps | null): RequestManager {
  const [state, updateState] = React.useState<{
    rootNode: DataNode | null,
    query: any | null,
    clearTime: { startDate: number, endDate: number } | null,
    loading: boolean,
    noDailySettlement?: boolean,
  }>({
    rootNode: null,
    query: null,
    clearTime: null,
    loading: false,
    noDailySettlement: false,
  });

  const {
    rootNode, query, clearTime, loading, noDailySettlement,
  } = state;
  const [error, setError] = React.useState<Error | null>(null);
  const userService = React.useContext(UserContext);

  const poiService = React.useMemo(() => new PoiService(), []);
  const { useClearTime, permissionCode, title = '' } = config || {};

  /** 获取结算时间 */
  const getClearTime = React.useCallback(async (_query: any) => {
    const { periodType } = _query;
    const { startDate, endDate } = _query as { startDate: number, endDate: number };
    const poiId = userService?.org.poiId;
    const timeRange = { startDate, endDate };
    const _clearTime = { startDate, endDate };
    let noDailySettlement = false;
    if (poiId && useClearTime !== false) {
      // 做了结算时间转换，返回转换后的时间范围
      const res = await poiService.getClearingTimeByRange(poiId, startDate, endDate, periodType);
      _clearTime.startDate = res.start;
      _clearTime.endDate = res.end;
      // 发送给后端的时间需要是自然日,汇总表实际查询使用startZero, endZero参数
      timeRange.startDate = res.startZero;
      timeRange.endDate = res.endZero;
      if (isNil(res.endOrigin) || isNil(res.endZeroOrigin) || res.start === res.endOrigin) {
        noDailySettlement = true;
      }
    }
    // 展示的需要是结算日
    return noDailySettlement ? {
      clearTime: _clearTime,
      timeRange,
      noDailySettlement,
    } : {
      clearTime: _clearTime,
      timeRange,
    };
  }, [userService, poiService, useClearTime]);

  /** 发起请求 */
  const doRequest = React.useCallback(async (_query: any, useBaseQuery?: boolean) => {
    const inYJResetApiErrorAb = await isInYJResetApiErrorAbAndChain();
    try {
      const api = useBaseQuery ? '/api/v2/reports/common-api/query' : `/api/v2/reports/common-api/query/${_query.queryKey}`;
      if (permissionCode) {
        // 把权限传给后端
        _query.permissionCode = permissionCode;
        // 合计行不需要弹框提示了，不然重复了
        await checkTimePermission({permissionCode, params: _query, showConfirmTip: !_query.isTotalRequest});
      }
      if (!_query.isTotalRequest) {
        pushReportOperationsLog();
      }
      delete _query.isTotalRequest;
      const req = { reqJson: JSON.stringify(_query) };
      const resp = await Net.post(api, req);
      const root = JSON.parse(resp) as DataNode;
      return root;
    } catch (error) {
      if (inYJResetApiErrorAb) {
        resetYJErrorMsg(error as IV);
      }
      throw error;
    }
  }, [setError, permissionCode]);

  /** 查询条件更变时，更新数据 */
  const updateDataByQuery = React.useCallback(async (_query: any, onSuccess?: () => void) => {
    let _loading = true;
    const requestStart = Date.now();
    updateState(_state => ({ ..._state, loading: _loading }));
    const time = await doAction(getClearTime(_query), {}, e => setError(e));
    if (!time) return;
    updateState(_state => ({ ..._state, clearTime: time.clearTime, noDailySettlement: time.noDailySettlement }));

    if (config?.api) {
      reportClickQuery({ name: title });
      _query = { ..._query, queryKey: config?.api, ...time.timeRange };
      const root = await doAction(doRequest(_query, config?.useBaseQuery), {}, e => setError(e));
      _loading = false;
      if (root) {
        onSuccess && onSuccess();
        updateState(_state => ({
          ..._state,
          rootNode: root,
          query: _query,
        }));
        reportSuccessQuery({ name: title });
        setError(null);
      } else {
        updateState(_state => ({
          ..._state,
          rootNode: {},
          query: _query,
        }));
      }
      reportQueryRenderTime(requestStart);
    } else {
      _loading = false;
      setError(null);
    }

    updateState(_state => ({ ..._state, loading: _loading }));
  }, [config?.api, getClearTime, updateState, doRequest, setError]);

  /** 返回对象加入缓存 */
  return React.useMemo(() => ({
    loading,
    updateDataByQuery,
    rootNode,
    query,
    clearTime,
    doRequest,
    error,
    noDailySettlement,
  }), [
    error,
    loading,
    updateDataByQuery,
    rootNode,
    query,
    clearTime,
    doRequest,
    noDailySettlement,
  ]);
}
