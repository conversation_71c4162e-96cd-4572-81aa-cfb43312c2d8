import React from 'react';

const Widget1 = (props: { className: string }) => (
  <svg width="200px" height="50px" className={props.className}>
    <defs>
      <linearGradient x1="12.7189516%" y1="17.9282071%" x2="91.658928%" y2="50%" id="widget-liner-5">
        <stop stopColor="#4C77FF" offset="0%" />
        <stop stopColor="#52BFFD" offset="100%" />
      </linearGradient>
    </defs>
    <rect fill="#fff" x="8.273809523809524" y="8.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.8575679015595861" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="17.797619047619047" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="46.36904761904762" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="55.89285714285714" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="103.51190476190476" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="122.55952380952381" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="132.08333333333334" y="8.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.54020070074932" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="141.60714285714286" y="8.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="160.6547619047619" y="8.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.7501911229356741" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="36.845238095238095" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.812642523219695" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="46.36904761904762" y="18.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="55.89285714285714" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.638544665191215" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="65.41666666666667" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.36809524780264313" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="74.94047619047619" y="18.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="93.98809523809524" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.037183200720793" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="103.51190476190476" y="18.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="132.08333333333334" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.241566990970977" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="160.6547619047619" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.915012009930483" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="170.17857142857142" y="18.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.42693114538099763" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="8.273809523809524" y="28.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.26778588756004096" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="27.32142857142857" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="46.36904761904762" y="28.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.0184382377148045" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="65.41666666666667" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="93.98809523809524" y="28.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.8439744882531928" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="113.03571428571428" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="151.13095238095238" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="179.70238095238096" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="189.22619047619048" y="28.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="55.89285714285714" y="38.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="113.03571428571428" y="38.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.8379249561905633" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="132.08333333333334" y="38.75" width="2.5" height="2.5" />
    <rect fill="#fff" x="151.13095238095238" y="38.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="0.3171689625925058" repeatCount="indefinite" />
    </rect>
    <rect fill="#fff" x="179.70238095238096" y="38.75" width="2.5" height="2.5">
      <animate attributeName="fill" values="#fff;transparent" dur="1s" begin="1.2706267136080376" repeatCount="indefinite" />
    </rect>
    <rect fill="url(#widget-liner-5)" x="189.905" y="19.4291" width="1.14172" height="1.14172">
      <animate attributeName="width" values="0;5" dur="2s" repeatCount="indefinite" />
      <animate attributeName="height" values="0;5" dur="2s" repeatCount="indefinite" />
      <animate attributeName="x" values="190.47619047619048;187.97619047619048" dur="2s" repeatCount="indefinite" />
      <animate attributeName="y" values="20;17.5" dur="2s" repeatCount="indefinite" />
    </rect>
    <rect fill="url(#widget-liner-5)" x="153.161" y="17.5" width="18.2676" height="5">
      <animate attributeName="width" values="0;40;0" dur="2s" repeatCount="indefinite" />
      <animate attributeName="x" values="171.42857142857142;131.42857142857142;171.42857142857142" dur="2s" repeatCount="indefinite" />
    </rect>
  </svg>
);

export default Widget1;
