import React from 'react';

/**
 * 自动计算字符截断，当长度超过 maxWidth 指定值的时候
 * 自动截断后续的内容，并追加省略号
 * 类似 text-overflow: ellipsis css 属性
 * 测量不是 100% 准确
 */
export default function useEllipsisText() {
  const ellipsisText = React.useMemo(() => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    return (str: string, size: number, maxWidth: number) => {
      if (!ctx) return str;
      const ellipses = '…';

      ctx.font = `${size}px`;
      maxWidth -= ctx.measureText(ellipses).width;
      return str.split('').reduce((result, char) => {
        if (result.width >= maxWidth) return result;
        const charWidth = ctx.measureText(char).width;
        if (result.width + charWidth >= maxWidth) {
          result.width = maxWidth;
          result.str += ellipses;
          return result;
        } else {
          result.width += charWidth;
          result.str += char;
          return result;
        }
      }, { str: '', width: 0 }).str;
    };
  }, []);

  return ellipsisText;
}
