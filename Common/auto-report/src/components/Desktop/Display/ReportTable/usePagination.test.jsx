const { default: usePagination, TableXPaginationPageSize } = require('./usePagination');
const { renderHook, act } = require('@testing-library/react-hooks');

describe('usePagination', () => {
  let loading, updateDisplayQuery, query, rootNode, showPagination;
  beforeEach(() => {
    loading = false;
    updateDisplayQuery = jest.fn((cb) => cb({}));
    query = {
      pageNo: 1,
    };
    rootNode = {
      page: {
        totalCount: 100,
      },
    };
    showPagination = true;
    window.localStorage.removeItem(TableXPaginationPageSize);
  });

  it('pageSize会取localStorage里上一次的值', () => {
    window.localStorage.setItem(TableXPaginationPageSize, 12345);
    const { result } = renderHook(() => usePagination(loading, updateDisplayQuery, query, rootNode, showPagination));
    expect(result.current.props.pageSize).toBe(12345);
  });

  it('pageSize更变时会修改 localStorage', () => {
    const { result } = renderHook(() => usePagination(loading, updateDisplayQuery, query, rootNode, showPagination));
    act(() => {
      result.current.props.onShowSizeChange(1, 34556);
    });
    const pageSize = window.localStorage.getItem(TableXPaginationPageSize);
    expect(pageSize).toBe('34556');
  });

  it('pageSize或者pageNo修改时会触发onChange', () => {
    let query;
    updateDisplayQuery = jest.fn((cb) => {
      query = cb({});
    });
    const { result } = renderHook(() => usePagination(loading, updateDisplayQuery, query, rootNode, showPagination));
    
    act(() => {
      result.current.props.onShowSizeChange(32, 1234);
    });
    expect(query.pageSize).toBe(1234);
    expect(query.pageNo).toBe(32);
  });
});