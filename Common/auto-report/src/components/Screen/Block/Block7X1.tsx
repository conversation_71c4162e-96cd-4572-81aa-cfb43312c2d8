import React from 'react';
import Container from '../../Desktop/Layout/Container';
import { ScreenBlockProps, Props } from '../../../types/config';
import ReportComponent from '../../ReportComponent';

export default function Block7(props: Props<ScreenBlockProps>) {
  const {
    cornerSize, title, type, ...otherProps
  } = props;
  const style: React.CSSProperties = {
    border: '1px solid rgba(77, 118, 255, 0.3)',
    borderRadius: '0 0 40px 0',
    position: 'relative',
    padding: '20px',
    ...otherProps,
  };

  const border = React.useMemo(() => {
    const cornerStyle: React.CSSProperties = {
      position: 'absolute',
    };
    const size = cornerSize || 40;
    const svgProps = { width: size, height: size };
    return (
      <>
        <svg {...svgProps} style={{ ...cornerStyle, left: 0, top: 0 }}>
          <defs>
            {/* <polyline id="auto-report-block-7-element" strokeLinecap="round" stroke="rgba(77, 118, 255, 0.3)" points="0, 25 0, 0 25, 0" fill="none" /> */}
            <g id="auto-report-border-7-element" fill="#52BFFD" fillOpacity="0.1">
              <path d="M2,2 L2,38 C2,39.1045695 1.1045695,40 -7.10542736e-15,40 L-3.55271368e-16,2 L-3.55271368e-15,0 L40,0 C40,1.1045695 39.1045695,2 38,2 L2,2 Z" />
              <path d="M4,4 L4,16 C4,18.209139 2.209139,20 8.8817842e-16,20 L3.55271368e-15,-3.6739404e-16 L4,3.6739404e-16 L20,0 C20,2.209139 18.209139,4 16,4 L4,4 Z" />
            </g>
          </defs>
          <use xlinkHref="#auto-report-border-7-element" />
        </svg>
        <svg
          {...svgProps}
          style={{
            ...cornerStyle,
            right: 0,
            top: 0,
            transform: 'rotate(90deg)',
          }}
        >
          <use xlinkHref="#auto-report-border-7-element" />
        </svg>
        <svg
          {...svgProps}
          style={{
            ...cornerStyle,
            left: 0,
            bottom: 0,
            transform: 'rotate(-90deg)',
          }}
        >
          <use xlinkHref="#auto-report-border-7-element" />
        </svg>
      </>
    );
  }, [cornerSize]);
  const titleDom = title ? <ReportComponent>{title}</ReportComponent> : null;
  return (
    <Container {...style} className="auto-report-screen-block7">
      {border}
      {titleDom && <div className="auto-report-block-title">{titleDom}</div>}
      <ReportComponent>{props.children}</ReportComponent>
    </Container>
  );
}
