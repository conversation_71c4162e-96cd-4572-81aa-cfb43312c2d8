/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
  registerShape,
} from '@antv/g2';
import type {
  Shape,
  Point,
} from '@antv/g2/lib/interface';
import { BarChartProps, Props, ReportContext } from '../../../../types';
import { registorComponent } from '../../registor';
import useBasicChart, { Data } from './useBasicChart';
import { BarGradient } from './const';
import Empty from './Empty';
import format from '../../../common/format';
import useEllipsisText from '../../../common/useEllipsisText';

export const LineGradient = [
  'l(0) 0:#FF5DA3 1:FF4143',
  'l(0) 0:rgba(82, 191, 253, 1) 1:rgba(76, 119, 255, 1)',
];

registerShape('interval', 'round', {
  draw(this: Shape, cfg, container) {
    const points = this.parsePoints(cfg.points as Point[]);
    const radius = Math.abs((points[2].y - points[1].y) / 2);
    const temp = [];
    const width = points[1].x - points[0].x < radius * 2 ? points[0].x + radius * 2 : points[1].x;
    points[1] = { ...points[1], x: width };
    points[2] = { ...points[2], x: width };

    temp.push(['M', points[1].x - radius, points[1].y]);
    temp.push(['A', radius, radius, 0, 0, 1, points[1].x, points[1].y + radius]);
    temp.push(['A', radius, radius, 0, 0, 1, points[2].x - radius, points[2].y]);
    temp.push(['L', points[3].x + radius, points[3].y]);
    temp.push(['A', radius, radius, 0, 0, 1, points[3].x, points[3].y - radius]);
    temp.push(['A', radius, radius, 0, 0, 1, points[0].x + radius, points[0].y]);
    temp.push(['Z']);

    const group = container.addGroup();
    return group.addShape('path', {
      attrs: {
        ...cfg.defaultStyle,
        ...cfg.style,
        fill: cfg.color,
        path: temp,
      },
    });
  },
});

export default function BarChart(props: Props<BarChartProps>) {
  const report = React.useContext(ReportContext);
  const {
    y,
    x,
    x1,
    legend,
    width,
    height,
    pageSize,
    totalPage,
  } = props;
  const ellipsisText = useEllipsisText();
  React.useEffect(() => {
    if (!pageSize || !totalPage) return;
    report?.filterManager.updateDisplayQuery({
      pageSize: pageSize * totalPage,
    });
  }, [pageSize, totalPage]);

  const [currentPageNo, setCurrentPageNo] = React.useState(1);
  const [totalCount, setTotalCount] = React.useState(0);

  React.useEffect(() => {
    if (!totalPage) return;
    const interval = setInterval(() => {
      setCurrentPageNo((page) => {
        const _totalPage = Math.min(totalPage, Math.floor(totalCount / (pageSize || 1)));
        if (page >= _totalPage) page = 0;
        return page + 1;
      });
    }, 10 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [setCurrentPageNo, totalPage, totalCount, pageSize]);

  /** 初始化图表 */
  const renderDim1Bar = React.useCallback((chart: Chart) => {
    // 添加主线
    const column = chart
      .interval()
      .shape('round')
      .position(`${y.field}*${x.field}`)
      .size(6);
    chart.axis(false);
    chart.coordinate().reflect('y').transpose();

    column.color(BarGradient);
    return chart;
  }, [x, y, legend]);

  /** 初始化图表 */
  const renderDim2Bar = React.useCallback((chart: Chart) => {
    if (!x1) return chart;
    chart
      .coordinate()
      .transpose();
    chart.axis(x.field, {
      grid: {
        line: {
          style: {
            lineDash: [5, 5],
            fillOpacity: 1,
            strokeOpacity: 1,
            stroke: 'rgb(58, 57, 83)',
          },
        },
      },
    });
    chart.axis(y.field, {
      line: {
        style: {
          stroke: 'white',
        },
      },
    });
    chart
      .interval()
      .size({ values: [8] })
      .position([y.field, x.field])
      .color(x1.field, LineGradient)
      .adjust([
        {
          type: 'dodge',
          marginRatio: 0,
        },
      ]);
    return chart;
  }, [x, y, x1, legend]);

  const renderer = React.useCallback((chart: Chart) => {
    chart.scale(x.field, {
      alias: x.label,
      formatter: v => format(x.format, v),
    });
    chart.scale(y.field, {
      alias: y.label,
      formatter: v => format(y.format, v),
    });
    if (x1) {
      chart.scale(x1.field, {
        alias: x1.label,
        formatter: v => format(x1.format, v),
      });
    }

    if (!x1) {
      return renderDim1Bar(chart);
    } else {
      return renderDim2Bar(chart);
    }
  }, [x, x1, y, renderDim1Bar, renderDim2Bar]);

  const loadDim1Data = React.useCallback((chart: Chart, data: Data) => {
    chart.annotation().clear();
    setTotalCount(data.length);
    if (pageSize && currentPageNo) {
      data = data.slice((currentPageNo - 1) * pageSize, currentPageNo * pageSize);
    }
    const max = data.reduce((m, d) => Math.max(m, d[x.field] as number), 0);
    const offsetY = -15;
    data.forEach((d, index) => {
      chart
        .annotation()
        .text({
          position: [d[y.field], max],
          content: format(x.format, d[x.field]) || '',
          style: {
            stroke: '0',
            fontSize: 14,
            fill: 'rgba(255,255,255,1)',
            textAlign: 'right',
          },
          offsetY,
        })
        .text({
          position: [d[y.field], 0],
          content: `No.${((pageSize || 0) * (currentPageNo - 1)) + index + 1}`,
          style: {
            fontSize: 14,
            fill: 'rgba(76, 119, 255, 1)',
            stroke: '0',
          },
          offsetY,
        })
        .text({
          position: [d[y.field], 0],
          content: ellipsisText(String(format(y.format, d[y.field]) || ''), 14, 200),
          style: {
            stroke: '0',
            fontSize: 14,
            fill: 'rgba(255,255,255,0.65)',
          },
          offsetY,
          offsetX: 40,
        })
        .line({
          start: [d[y.field], 0],
          end: [d[y.field], max],
          style: {
            fill: 'rgba(255,255,255,0.25)',
          },
          offsetY: 7,
        });
    });
    chart.data(data);
    chart.render(true);
  }, [y, pageSize, currentPageNo]);

  const loadDim2Data = React.useCallback((chart: Chart, data: Data) => {
    chart.annotation().clear();
    chart.data(data);
  }, [y]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    if (!x1) {
      return loadDim1Data(chart, data);
    } else {
      return loadDim2Data(chart, data);
    }
  }, [loadDim1Data, loadDim2Data, x1]);

  const dims = React.useMemo(() => [y.field, legend?.field], [y, legend]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
    empty: <Empty />,
  });

  return dom;
}

registorComponent('BarChart', BarChart);
