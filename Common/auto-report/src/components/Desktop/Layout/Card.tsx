import React from 'react';
import ReportComponent from '../../ReportComponent';
import { registorComponent } from '../registor';
import {
  CardProps,
  Props,
} from '../../../types';
import {
  ContainerContext,
} from '../../../types/context';
import './Card.less';

export default function Card(props: Props<CardProps>) {
  const context = React.useContext(ContainerContext);
  const header = React.useMemo(() => {
    if (!props.header) return null;
    if (typeof props.header === 'string') return <h3>{props.header}</h3>;
    return <ReportComponent>{props.header}</ReportComponent>;
  }, [props.header]);

  return (
    <div className="auto-report-card">
      <ContainerContext.Provider value={{ ...context }}>
        {header ? <div className="auto-report-card-header">{header}</div> : null}
        <div className="auto-report-card-content">
          <ReportComponent>{props.children}</ReportComponent>
        </div>
      </ContainerContext.Provider>
    </div>
  );
}

registorComponent('Card', Card);
