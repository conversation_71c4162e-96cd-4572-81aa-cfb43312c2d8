:root .auto-report-page .auto2-query-container {
  padding: 0 15px;
  .filter-first {
    padding-right: 60px;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    width: 100%;

    .auto2-query-item {
      display: none;
      align-items: center;
      padding-right: 16px;
      height: 37px;
      &.show-type-only,
      &.show-type-both {
        display: flex;
      }
      &.show-type-only {
        .auto2-query-item-control {
          font-weight: 500;
          display: flex;
          align-items: center;
          text-overflow: ellipsis;
          word-break: break-all;
          &::after {
            content: ' ';
            color: transparent;
            width: 0;
            height: 0;
            border: 4px solid transparent;
            border-top-color: #666;
            border-bottom-style: none;
            margin-left: 5px;
          }
        }
      }
    }
  }

  .block_item {
    .mc-item {
      min-height: 0;
    }
    .mc-label-item_children {
      margin-top: 0;
    }
  }

  .center{
    padding: 0;
    justify-content: center;
  }
  .container-filter-modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    z-index: 4;
    display: flex;
    flex-direction: column;
    .filter-more {
      padding-bottom: 70px;
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .auto2-query-item {
      .query-label {
        font-size: 16px;
        line-height: 24px;
        margin-left: 15px;
        margin-top: 20px;

        .title-right {
          font-size: 16px;
          color: #ccc;
          font-weight: normal;
          float: right;
          margin-right: 15px;
        }
      }
      &.show-type-only {
        display: none;
      }
    }
  }

  .auto2-query-toggle {
    position: absolute;
    right: -10px;
    bottom: 4px;
    padding: 4px 15px;
    &::before {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      content: '';
      width: 0;
      height: 14px;
      border-left: 1px solid #e5e5e5;
    }
  }

  .auto2-query-item-label {
    margin-right: 8px;
  }
  .auto2-query-item-control {
    display: flex;
    position: relative;
    zoom: 1;
  }
}
