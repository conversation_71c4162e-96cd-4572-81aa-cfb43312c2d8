const git = require('./pre-commit');

describe(`Regex unicode modifier check`, () => {
  it(`test`, () => {
    const regex = git.unicodeRegExp;

    expect(regex.test(`new RegExp("sdgasd",'gu')`)).toBeTruthy();
    expect(regex.test(`new RegExp("sdgasd", 'gu')  `)).toBeTruthy();
    expect(regex.test(`new RegExp("sdgasd", 'gm')`)).toBeFalsy();

    expect(regex.test(`/dgasdf/u`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug;`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug ;`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug )`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug)`)).toBeTruthy();
    expect(regex.test(`/dgasdf/ug{`)).toBeTruthy();

    expect(regex.test(`/dgasdf/)`)).toBeFalsy();
    expect(regex.test(`/dgasdf/abc`)).toBeFalsy();
    expect(regex.test(`/dgasdf/443`)).toBeFalsy();
    expect(regex.test(`/dgasdf/gm`)).toBeFalsy();
    expect(regex.test(`/dgasdf/uga`)).toBeFalsy();
    expect(regex.test(`/dgasdf/ug/a`)).toBeFalsy();
    expect(regex.test(`/dgasdf/ug/443`)).toBeFalsy();
    expect(regex.test(`/dgasdf/ug-443`)).toBeFalsy();
  });
});
