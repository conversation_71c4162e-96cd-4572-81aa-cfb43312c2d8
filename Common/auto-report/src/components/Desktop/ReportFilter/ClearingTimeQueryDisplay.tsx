import React from 'react';
import { UserContext } from '@mtfe/next-biz/src/contexts/user';
import showBusinessTimeRecord from '@mtfe/next-biz/src/components/Poi/BusinessTimeRecord';
import { ua } from '@mtfe/next-biz/src/utils/ua';
import transObj from 'trans-object';
import NavLink from '@mtfe/next-biz/es/lib/StorageHistoryReportQueryPersistence/NavLink';
import { ClearingTimeQueryDisplayProps } from './types';
import { formatDateTime, queryPersistence } from '../../../utils/format';
import { useManualDailyClearingSetting } from '../../../utils/hook';
import { registorComponent } from '../registor';
import {
  Props,
} from '../../../types';


export default function ClearingTimeQueryDisplay(props: Props<ClearingTimeQueryDisplayProps>) {
  const { clearTime, noDailySettlement, originQuery = {} } = props;
  const { timeRange: originTimeRange, date, poiIds: originPoiIds } = originQuery;
  const userService = React.useContext(UserContext);
  const isOpenManualDailyClearing = useManualDailyClearingSetting();
  const isChain = userService?.isHeadOffice();
  // 日期参数
  const timeRange = React.useMemo(() => {
    return originTimeRange || date || {};
  }, [originTimeRange, date]);
  // 门店参数
  const poiIds = React.useMemo(() => {
    return originPoiIds || []
  }, [originPoiIds]);
  // 跳转参数
  const linkParams = React.useMemo(() => {
    return transObj({
      timeRange,
      poiIds,
    })
    .deleteEmptyFields()
    .done();
  }, [timeRange, poiIds]);
  const detailLink = React.useMemo(() => {
    const link = <NavLink crossMenu to={`/web/report/dailySettlementLog?${queryPersistence.encodeQuery(linkParams)}#/rms-report/dailySettlementLog`}>点击查看详情</NavLink>
    if (ua.os === 'pc' && isOpenManualDailyClearing && (isChain || noDailySettlement)) {
      return link;
    }
    return null;
  }, [isOpenManualDailyClearing, noDailySettlement, linkParams, isChain]);
  /** 展示查询时间范围 */
  const clearingTimeQueryDisplay: (React.ReactElement | null) = React.useMemo(() => {
    if (!clearTime) return null;
    if (userService && userService.isHeadOffice()) {
      return <>查询时间范围：基于各门店营业结算时间分别查询后汇总，{isOpenManualDailyClearing ? detailLink : <a onClick={() => showBusinessTimeRecord()}>点击查看详情</a>}</>;
    }
    return <>查询时间范围：{formatDateTime(clearTime.startDate)}-{formatDateTime(clearTime.endDate)}{detailLink}
    </>;
  }, [clearTime, userService, detailLink, isOpenManualDailyClearing]);

  return clearingTimeQueryDisplay;
}

registorComponent('ClearingTimeQueryDisplay', ClearingTimeQueryDisplay);
