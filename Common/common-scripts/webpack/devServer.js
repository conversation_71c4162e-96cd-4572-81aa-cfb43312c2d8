const fs = require('fs');
const path = require('path');
const request = require('request');
const dllConfig = require('@mtfe/mpack/dll/out/all_dev.json');
const { HOST } = process.env;

const envConfig = {
  target: "http://rms.sjst.test.sankuai.com",
  swimlane: "1960-stavs",
};

const router = () => {
  const testEnv = 'http://rms.sjst.test.sankuai.com';
  let swimlane = process.env.SWIMLANE;
  let target = 'http://rms.sjst.test.sankuai.com';
  try {
    if (fs.existsSync(path.resolve(__dirname, '../../../envconf.js'))) {
      const conf = require('../../../envconf.js');
      if (conf.target) {
        swimlane = conf.swimlane;
        target = conf.target;
      }
    }
  } catch (error) {
    console.log(error);
  }
  if (target !== testEnv) return target;
  if (swimlane && target) {
    delete require.cache[require.resolve('../../../envconf.js')];
    return `http://${swimlane}-sl-rms.sjst.test.sankuai.com`;
  }
};

const pkgName = require("../../../package.json").name;

function proxy(req, res, next) {
  const reg = /\/([^\/]*)\.pagemap\.html/;
  const matchedParam = req.path.match(reg);
  if (matchedParam && matchedParam.length > 1) {
    const projectName = matchedParam[1];
    if (projectName === pkgName) {
      const js = `http://${HOST ? HOST : '127.0.0.1'}:8081/${projectName}.js`;
      res.set("Access-Control-Allow-Origin", "*");
      res.json({
        id: pkgName,
        publicPath: "",
        css: [],
        js: [dllConfig.index.js[0], js],
      });
    }
    next();
    return;
  } else {
    const ctxPath = req.path.replace(/\/web/, '');
    const blackList = [];
    const method = req.method.toUpperCase();
    if (blackList.indexOf(`${method}${ctxPath}`) !== -1) {
      request({
        method,
        url: `${envConfig.target}${ctxPath}`,
        headers: {
          'x-shepherd-swimlane': envConfig.swimlane,
        },
      }, (error, response, body) => {
        if (!error && response.statusCode === 200) {
          res.json(JSON.parse(body));
        } else {
          res.status(404);
          console.error(error);
        }
        next('route');
      });
    } else {
      next();
    }
  };
}

const devServer = (env, argv) => {
  return {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods":
        "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers":
        "X-Requested-With, content-type, Authorization",
    },
    port: 8081,
    disableHostCheck: true,
    host: HOST ? HOST : '127.0.0.1',
    before: (app) => {
      app.all("/*", (req, res, next) => {
        proxy(req, res, next);
      });
    },
    proxy: [{
      context: ['/**', `!/${pkgName}.js`],
      target: envConfig.target,
      changeOrigin: true,
      secure: false,
      router,
    }],
  };
};

module.exports = {
  devServer,
  router,
  proxy,
  envConfig,
};
