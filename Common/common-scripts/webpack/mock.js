/* 未启用 */
const request = require('request');
const path = require('path');
const pathToRegexp = require('path-to-regexp');
const hotRequire = require('../../../mock/utils/hotRequire');

const mockMiddleWare = (app) => {
  app.all('/*', (req, res, next) => {
    req.headers.appCode = '49';
    req.headers.model = 'chrome';
    req.headers.unionId = '1111';

    const reqPath = req.path.replace(/\/web/, '');
    const blackList = hotRequire(path.resolve(__dirname, '../mocks/blackList.js'));

    const method = req.method.toUpperCase();
    const api = `${method}${reqPath}`;
    if (blackList.some(_ => pathToRegexp(_.replace(/ /g, '')).exec(api))) {
      request({
        method,
        url: `http://localhost:9988${reqPath}`,
        headers: {
          appCode: '49',
        },
      }, (error, response, body) => {
        if (!error && response.statusCode === 200) {
          res.json(JSON.parse(body));
        } else {
          res.status(404);
          console.error(error);
        }
        next('route');
      });
    } else {
      next();
    }
  });
};

module.exports = {
  mockMiddleWare,
};