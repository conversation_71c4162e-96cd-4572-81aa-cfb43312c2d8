import React from 'react';
import Bolck7 from './Block7';
import Bolck14 from './Block14';
import Bolck8 from './Block8';
import { ScreenBlockProps, Props } from '../../../types/config';
import { stateContext } from '../../../types/context';
import { registorComponent } from '../registor';
import './index.less';

type Move = {
  x: number,
  y: number,
}

function BorderBlock(props: Props<ScreenBlockProps>) {
  switch (props.type) {
    case 14:
      return <Bolck14 {...props} />;
    case 8:
      return <Bolck8 {...props} />;
    case 7:
    default:
      return <Bolck7 {...props} />;
  }
}

export default function ScreenBlock(props: Props<ScreenBlockProps>) {
  const { state } = React.useContext(stateContext);
  const {
    className, blockKey, onSelected, ...otherProps
  } = props;
  const [move, setMove] = React.useState<{ down?: Move, move?: Move }>({});

  if (!blockKey || !onSelected) {
    return <BorderBlock className={[className, 'screen-block'].join(' ')} {...otherProps} />;
  }

  const {
    cornerSize, title, type, ...styleProps
  } = otherProps;

  const isSelected = state.selectedCard === blockKey;
  return (
    <div
      className={['screen-block-edit-content', isSelected ? 'selected' : ''].join(' ')}
      style={styleProps}
      onMouseDown={(e) => {
        setMove({ ...move, down: { x: e.pageX, y: e.pageY } });
      }}
      onMouseMove={(e) => {
        if (move.down) {
          setMove({ ...move, move: { x: e.pageX, y: e.pageY } });
        }
      }}
      onClick={(e) => {
        e.stopPropagation();
        // @ts-ignore
        if (document.fullscreen || document.mozFullScreen || document.webkitIsFullScreen || document.webkitFullScreen || document.msFullScreen) {
          return;
        }
        if (move.down && move.move) {
          if (Math.abs(move.move.x - move.down.x) > 5 || Math.abs(move.move.y - move.down.y) > 5) {
            setMove({});
            return;
          }
        }
        setMove({});
        if (!isSelected) {
          onSelected(blockKey);
        }
      }}
    >
      <BorderBlock className={[className, 'screen-block'].join(' ')} {...otherProps} />
    </div>
  );
}

registorComponent('ScreenBlock', ScreenBlock);
