import Default from './Default';
import StringInput from './String';
import PoiSelector from './PoiSelector';
import PoiAndSalesDepartSelector from './PoiAndSalesDepart';
import DatePicker from './DatePicker';
import EnumSelector from './EnumSelector';
import GroupByRadio from './GroupByRadio';
import GroupByCheckbox from './GroupByCheckbox';
import GroupBySelector from './GroupBySelector';
import AreaSelector from './AreaSelector';
import BrandSelector from './BrandSelector';
import GoodsSpecSelector from './GoodsSpecSelector';
import DiscountSelector from './DiscountSelector';
import FeeSelector from './FeeSelector';
import GoodsCategorySelector from './GoodsCategorySelector';
import DictionarySelector from './DictionarySelector';
import GoodsSelector from './GoodsSelector';
import MealSegment from './MealSegment';
import PaymentSelector from './PaymentSelector';
import StaffSelector from './StaffSelector';
import TableSelector from './TableSelector';
import DepartSelector from './DepartSelector';
import Checkbox from './Checkbox';
import Number from './Number';
import PoiCitySelector from './PoiCitySelector';
import { InputConstructor } from '../../../types/filter';

export default {
  Number,
  Default,
  StringInput,
  DepartSelector,
  DatePicker,
  EnumSelector,
  PoiSelector,
  PoiAndSalesDepartSelector,
  GroupByRadio,
  GroupByCheckbox,
  GroupBySelector,
  AreaSelector,
  BrandSelector,
  GoodsSpecSelector,
  DiscountSelector,
  FeeSelector,
  GoodsCategorySelector,
  GoodsSelector,
  MealSegment,
  PaymentSelector,
  StaffSelector,
  TableSelector,
  DictionarySelector,
  Checkbox,
  PoiCitySelector,
} as {
  [k: string]: InputConstructor,
};
