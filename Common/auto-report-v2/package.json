{"name": "@mtfe/auto-report-v2", "version": "1.0.0-62", "description": "", "scripts": {"lint": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "build:tsc": "tsc -p ./src --outDir es --declaration --skipL<PERSON><PERSON><PERSON><PERSON>", "build": "node ./scripts/build-npm.js", "prepublishOnly": "npm run build"}, "main": "./es/index", "author": "", "license": "ISC", "dependencies": {"deep-equal": "^1.0.1", "lodash": "4.17.11", "mitt": "^2.1.0", "memoize-one": "^5.1.1", "js-cookie": "^2.2.1", "react-sortable-hoc": "^1.11.0", "net4j": "1.1.13", "net4j-formatter-plugin": "^1.0.11", "archer-svgs": "^0.2.4", "braft-editor": "^2.3.5", "braft-utils": "^3.0.12", "rc-resize-observer": "^1.0.0", "xhr-proxy-plus": "0.0.20"}, "devDependencies": {"@mtfe/mpack": "^1.0.55", "@testing-library/react-hooks": "^3.4.1", "@types/deep-equal": "^1.0.1", "@types/jest": "^26.0.10", "@types/react-test-renderer": "^16.9.3", "jest": "^26.4.2", "react-test-renderer": "^16.13.1", "react": "^16.8.0", "react-dom": "^16.8.0", "typescript": "3.8.2"}}