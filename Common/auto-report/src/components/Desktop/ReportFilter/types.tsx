import React from 'react';
import {
  Component,
} from '../../../types';

export type ClearingTimeQueryDisplayProps = {
  clearTime?: { startDate: number, endDate: number } | null;
  noDailySettlement?: boolean,
  originQuery?: any,
}

export type ClearingTimeQueryConfig = Component<'ClearingTimeQueryDisplay', ClearingTimeQueryDisplayProps>;

export type QueryItemProps = {
  index: number,
  label?: string,
  size?: 'small' | 'default' | 'large',
  bid?: string,
  error?: React.ReactNode,
  help?: string,
  moduleClick?: (bid?: string) => void,
}

export type QueryItemConfig = Component<'QueryItem', QueryItemProps>;

export type QueryContainerProps = {
  hidden?: boolean,
  realTimeUpdate?: boolean,
  loading: boolean,
  errors?: Record<string, React.ReactNode>,
  // eslint-disable-next-line
  onSubmit: (query: any) => void,
  onReset: () => void,
  count: number,
  tip: React.ReactNode,
  extraTips?: React.ReactNode,
  size?: 'small' | 'default' | 'large',
  moduleClick?: (bid?: string) => void,
  expandBid?: string,
  maxQueryCount?: number,
  otherParams?: {
    isPos?: boolean,
    description?: string,
    extraButtons?: React.ReactNode,
  },
}

export type QueryContainerConfig = Component<'QueryContainer', QueryContainerProps>;
