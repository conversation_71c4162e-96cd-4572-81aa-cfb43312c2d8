/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Spin } from 'antd';
import Form, { FormInstance, FormProps } from '@mtfe/rmsform-antd';
import { wrapMaterialWithField } from '../cores/material';
import { DataContext, withDataContext } from '../cores/dataContext';
import { ContextWrapper } from '../cores/context';
import type { FormValues, IV, AutoFormProps, AutoFormCompoentProps } from '../type';
import { parseFormValue } from '../utils/common';
import './index.less';

export type IState = {
  // form初始化中
  isIniting: boolean;
  // 数据提交中
  isSubmiting: boolean;
};

export const createAutoFormBasic = <D extends IV, F extends FormValues>(props: AutoFormProps<D, F>) => {
  const {
    properties,
    template,
    onInit,
    onSubmit,
    container,
    onDistribute,
    formProps = {},
    submitText = '数据提交中',
    loadingText = '数据加载中',
  } = props;
  // rms-form ref
  const ref = React.createRef<FormInstance>();

  let formValues: F | null = null;

  // 获取autoForm示例，用于管控内部状态
  let autoFormInstance: AutoForm | undefined;
  // 标志组件是否挂载，用于执行对象实例
  let isMount = false;

  // 检查方法
  const check = async () => {
    await autoFormInstance?.checkAllRefs();
    return await ref.current?.check();
  };

  // 提交，对应参数里的onSubmit，包装一层维护一个loadin态，禁止重复提交
  const submit = async () => {
    // 当没有渲染完成或者正在提交时，禁止submit方法执行
    if (!ref.current || !isMount || autoFormInstance?.state.isSubmiting || !onSubmit) {
      return;
    }
    try {
      const values = await check();
      autoFormInstance?.toggleSubmiting();
      const parsedValue = parseFormValue(values);
      // eslint-disable-next-line
      const r = await onSubmit(parsedValue as any);
      autoFormInstance?.toggleSubmiting();
      return r;
    } catch (e) {
      console.error(e);
    }
  };

  class AutoForm extends ContextWrapper<AutoFormCompoentProps<D, F>, IState> {
    static isAutoForm = true;

    static contextType = DataContext;

    state = {
      isIniting: true,
      isSubmiting: false,
    };

    toggleSubmiting = () => {
      const { isSubmiting } = this.state;
      this.setState({ isSubmiting: !isSubmiting });
    };

    async componentDidMount() {
      const { onInitBefore } = this.props;
      if (onInitBefore) {
        await onInitBefore(this.context);
      }

      if (onInit) {
        const r = await onInit(this.context);
        if (r) {
          formValues = r;
        }
      }

      isMount = true;
      autoFormInstance = this;
      this.setState({ isIniting: false });
      this.setTopRef(ref);
    }

    componentWillUnmount() {
      isMount = false;
      autoFormInstance = undefined;
    }

    renderTemplate = () => {
      const map = wrapMaterialWithField({
        context: this.context,
        properties,
        formRef: ref,
      });
      // eslint-disable-next-line
      return template
        ? template(map as any, {
            ctx: this.context,
            initialValue: formValues,
            submit,
          })
        : Object.values(map);
    };

    onChangeX: FormProps['onChange'] = (v, c) => {
      const { onChange } = this.props;
      onChange?.(v as any);
      formProps?.onChange?.(v, c);

      const getFieldValue: FormInstance['getFieldValue'] = key => ref.current?.getFieldValue(key);
      const setFieldValue: FormInstance['setFieldValue'] = (...args) => ref.current?.setFieldValue(...args);
      onDistribute?.(v as any, {
        changedKeys: Object.keys(c || {}),
        getFieldValue,
        setFieldValue,
      });
    };

    renderForm = () => {
      const { value } = this.props;
      const form = (
        <Form
          className="auto-form"
          {...formProps}
          onChange={this.onChangeX}
          initialValues={value || formValues || formProps?.initialValues}
          ref={ref}
        >
          {this.renderTemplate()}
        </Form>
      );
      return this.wrapContext(form);
    };

    check = check;

    render() {
      const { isIniting, isSubmiting } = this.state;
      if (isIniting) {
        return (
          <Spin tip={loadingText}>
            <div style={{ height: 100 }} />
          </Spin>
        );
      }
      const form = (
        <Spin spinning={isSubmiting} tip={submitText}>
          <div className="auto-form-container">{this.renderForm()}</div>
        </Spin>
      );

      return container
        ? container(form, {
            ctx: this.context,
            initialValue: formValues,
            submit,
          })
        : form;
    }
  }

  return {
    AutoForm: withDataContext(AutoForm),
    formRef: ref,
    submit,
    check,
  };
};

export type AutoFormInstance = ReturnType<typeof createAutoFormBasic>['AutoForm'];

export const createAutoForm = <D extends IV, F extends FormValues>(props: AutoFormProps<D, F>) =>
  createAutoFormBasic(props);
