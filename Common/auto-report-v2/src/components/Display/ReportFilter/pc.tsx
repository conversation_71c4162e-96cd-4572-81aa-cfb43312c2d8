import { forEach, throttle } from 'lodash';
import React from 'react';
import classNames from 'classnames';
import { Button } from '@mtfe/sjst-antdx-next';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import { useManager } from './useManager';
import { Config, ComponentProps, ObjectRefType } from '../../../types';

import './pc.less';

interface ButtonProps {
  hide?: boolean;
}

interface Props extends ComponentProps<Config[]> {
  // 默认展示组件的数量，默认是按照默认一行展示
  defaultQueryCount?: number;
  items: Config[];
  resetButtonProps?: ButtonProps;
  analytics?: {
    submitBid: string;
    resetBid: string;
    toggleQueryBid: string;
  };
}

function getParentElementByClassName(element: HTMLElement, className: string): HTMLElement | null {
  while (element && element.parentElement) {
    element = element.parentElement;
    if (element && element.classList.contains(className)) {
      return element;
    }
  }
  return null;
}

function ReportFilter({
  defaultQueryCount,
  items,
  baseRefObject,
  updateStatus,
  analytics,
  resetButtonProps,
  $$auto,
}: Props) {
  const queryContainerRef = React.useRef<HTMLDivElement>(null);
  const actionRef = React.useRef<HTMLDivElement>(null);

  const refObject = useRefObject(baseRefObject);

  const [state, setState] = React.useState({
    firstCount: 100,
    separatorWidth: 0,
  });
  const [isExpand, setExpand] = React.useState(false);
  const queryWidthsRef = React.useRef<{ [id: string]: number }>({});

  const { setSelectedId, queryManageList, queryManageButton } = useManager(
    items,
    refObject,
    $$auto,
  );

  React.useEffect(() => {
    if (!queryContainerRef.current) {
      return;
    }

    const calculate = () => {
      const refDom = queryContainerRef.current;
      if (!refDom) {
        return;
      }

      // 预留宽度。切换时间/选择下拉时，输入框会变长，导致折叠
      let reservedWidth = 120;
      // 初始化筛选项宽度
      const _widths: { [id: string]: number } = { ...queryWidthsRef.current };

      forEach(refDom.querySelectorAll('.auto2-query-item'), (itemDom) => {
        const key = itemDom.getAttribute('data-key');
        const width = itemDom.clientWidth;
        if (key) {
          _widths[key] = width;
          // 查询、重设区域
          if (key === 'auto2_active') {
            reservedWidth += width;
          }
        }
      });

      queryWidthsRef.current = _widths;

      // 计算折叠多少个
      const containerWidth = refDom.clientWidth;
      if (!containerWidth) {
        return;
      }
      const data = { firstCount: 1, separatorWidth: 0, preWidth: 0 };
      items.forEach((item, index) => {
        if (item.props?.dataKey) {
          const width = _widths[item.props?.dataKey];
          if (containerWidth >= data.preWidth + width + reservedWidth) {
            data.firstCount = index + 1;
            data.separatorWidth = containerWidth - data.preWidth - reservedWidth - 20;
          }
          data.preWidth += width;
        }
      });

      setState((oldState) => {
        if (
          oldState.firstCount === data.firstCount
          && oldState.separatorWidth === data.separatorWidth
        ) {
          return oldState;
        }
        return {
          firstCount: data.firstCount,
          separatorWidth: data.separatorWidth,
        };
      });
    };

    const throttleCalculate = throttle(calculate, 1000, { leading: true, trailing: true });
    const throttleCalculateX = throttle(calculate, 1000, { leading: false, trailing: true });
    const delayCalculate = (mutationRecords?: MutationRecord[]) => {
      const reportStatus =
      refObject?.version === 2
        ? $$auto.withParentName('Report')?.$self.status
        : (refObject as ObjectRefType<any>)?.withParentName('Report')?.status;
      if (reportStatus !== 'done') {
        setTimeout(() => {
          delayCalculate(mutationRecords);
        }, 100);
      } else if (!mutationRecords) {
        throttleCalculate();
      } else if (mutationRecords?.findIndex(m => m.target === queryContainerRef.current) !== -1) {
        // 展开收起
        throttleCalculate();
      } else if (mutationRecords?.findIndex(m => m.type === 'childList' && m.target !== actionRef.current) !== -1) {
        // 子节点发生变化
        throttleCalculateX();
      }
    };

    calculate();

    window.addEventListener('resize', calculate);
    const observer = new MutationObserver(delayCalculate);
    observer.observe(queryContainerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
    });
    
    const observer2 = new MutationObserver(throttleCalculate);
    const reportDom = getParentElementByClassName(queryContainerRef.current, 'wrapperForCssHide');
    if (reportDom) {
      observer2.observe(reportDom, {
        childList: true,
        attributes: true,
      })
    }
    return () => {
      window.removeEventListener('resize', calculate);
      observer.disconnect();
      observer2.disconnect();
    };
  }, [queryContainerRef.current]);

  const toggleExpand = React.useCallback(() => {
    setExpand(a => !a);
    analytics?.toggleQueryBid
      && refObject?.analytics.moduleClick(analytics.toggleQueryBid);
  }, []);

  const handleSubmit = React.useCallback(() => {
    refObject?.emit('submit@Report');
    analytics?.submitBid
      && refObject?.analytics.moduleClick(analytics.submitBid);
  }, [refObject, analytics]);

  const handleReset = React.useCallback(() => {
    refObject?.emit('reset@Report');
    refObject?.emit('submit@Report', { isReset: true });
    setSelectedId(undefined);
    analytics?.resetBid && refObject?.analytics.moduleClick(analytics.resetBid);
  }, [refObject, analytics]);

  const expandButton = React.useMemo(() => {
    const notExpandButton = defaultQueryCount ? items.length <= defaultQueryCount : items.length <= state.firstCount;
    if (notExpandButton && !isExpand) {
      return null;
    }
    return (
      <Button
        onClick={toggleExpand}
        className="auto2-query-toggle"
        type="link"
        icon={isExpand ? 'up' : 'down'}
      >
        {isExpand ? '收起筛选' : '更多筛选'}
      </Button>
    );
  }, [isExpand, toggleExpand, state, items]);

  const itemDoms = React.useMemo(
    () => items.filter(Boolean).map(d => ({
      props: d.props,
      dom: refObject?.renderManager.render(d),
    })),
    [items],
  );

  const itemNode = React.useMemo(() => {
    const containerWidth = queryContainerRef.current?.clientWidth || 0;
    const columnCount = Math.floor(containerWidth / 300); // 显示列数
    const flex = `0 1 ${100 / (columnCount || 1)}%`;

    return itemDoms.map((item, index) => {
      const isFirst = index < state.firstCount;
      const isHide =  defaultQueryCount ? index >= defaultQueryCount : !isFirst;
      return (
        <div
          key={`query-${item?.props?.dataKey || index}`}
          data-key={isFirst ? item?.props?.dataKey : undefined}
          className={classNames('auto2-query-item', item?.props?.className, isFirst ? 'first' : 'more', isHide ? 'hide' : '')}
          style={{ flex: isFirst ? undefined : flex }}
        >
          {item?.props?.label && (
            <div className="auto2-query-item-label">{item.props.label}:</div>
          )}
          <div className="auto2-query-item-control">{item.dom}</div>
        </div>
      );
    });
  }, [items, state]);

  const queryActive = React.useMemo(() => (
    <div
      className="auto2-query-item action"
      data-key={isExpand ? '' : 'auto2_active'}
      ref={actionRef}
    >
      {expandButton}
      <Button icon="search" type="primary" onClick={handleSubmit}>
        查询
      </Button>
      {!resetButtonProps?.hide && <Button icon="redo" onClick={handleReset}>
        重置
      </Button>}
      {queryManageButton}
    </div>
  ), [expandButton, isExpand, handleSubmit, handleReset, resetButtonProps]);

  React.useEffect(() => updateStatus?.('done', items), [items]);

  return (
    <div className="auto2-filter">
      {queryManageList}
      <div
        className={classNames('auto2-query-container', isExpand ? 'expand' : '')}
        ref={queryContainerRef}
      >
        {itemNode}
        {queryActive}
      </div>
    </div>
  );
}

// @ts-ignore
ReportFilter.auto = {
  childrenIsConfig: false,
};

export default withAdapter(ReportFilter, {
  name: 'ReportFilter',
  isComponentRender: false,
});
