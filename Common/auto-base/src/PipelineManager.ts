import { reverse } from 'lodash';

export type PipelineCallBackFuncType<DataType> = (
  data: DataType,
  options?: any
) => Promise<void>;

export type PipelineFuncType<DataType> = (
  data: DataType,
  options?: any
) =>
  | Promise<void>
  | void
  | Promise<PipelineCallBackFuncType<DataType>>
  | PipelineCallBackFuncType<DataType>;

export type PipelineType<DataType> =
  | string
  | { name: string; options?: any }
  | PipelineFuncType<DataType>;

export class PipelineManager<DataType> {
  private _funcs = new Map<string, PipelineFuncType<DataType>>();

  registor(name: string, func: PipelineFuncType<DataType>) {
    this._funcs.set(name, func);
  }

  async run(
    pipelines: Array<PipelineType<DataType>>,
    data: DataType,
    options?: any
  ) {
    const callbackPipelines: Array<PipelineCallBackFuncType<DataType>> = [];
    for (let pipeline of pipelines) {
      let pipelineFunc: PipelineFuncType<DataType> | undefined;
      let pipelineFuncOptions: any;
      if (typeof pipeline === 'string') {
        pipelineFunc = this._funcs.get(pipeline);
      } else if (typeof pipeline === 'object') {
        pipelineFunc = this._funcs.get(pipeline.name);
        pipelineFuncOptions = pipeline.options;
      } else if (typeof pipeline === 'function') {
        pipelineFunc = pipeline;
      }
      if (pipelineFunc) {
        const callback = await pipelineFunc(data, {
          ...options,
          ...pipelineFuncOptions,
        });
        if (callback) {
          callbackPipelines.push(callback);
        }
      }
    }

    for (let callbackPipeline of reverse(callbackPipelines)) {
      await callbackPipeline(data, options);
    }
    return data;
  }
}

export default PipelineManager;
