const React = require('react');
const { Alert, Table } = require('antd');
const { default: ReportTable } = require('./index');
const renderer = require('react-test-renderer');
const {
  ReportTableProps,
  ReportContext,
  ContainerContext,
  Column,
} = require('../../../../types');

describe('ReportTable', () => {
  let instance, report, render, columns;
  const reset = () => {
    columns = [];
    report = {
      props: {},
      requestManager: { },
      filterManager: null,
    };
    render = null;
    instance = null;
  };

  beforeEach(() => {
    render = () => renderer.act(() => {
      instance = renderer.create(
        <ReportContext.Provider value={report}>
          <ReportTable columns={columns} />
        </ReportContext.Provider>
      );
    });
  });

  reset();
  afterEach(reset);

  it('出错时显示 Alert', () => {
    report.requestManager.error = new Error();
    render();
    let alerts = instance.root.findAllByType(Alert);
    expect(alerts.length).toBe(1);
    report.requestManager.error = null;
    render();
    alerts = instance.root.findAllByType(Alert);
    expect(alerts.length).toBe(0);
  });

  it('有数据时才能导出', () => {
    let exportable;
    report.exportManager = {
      updateExportable: (v) => {
        exportable = v
      },
    };
    render();
    expect(exportable).toBe(false);
    report.requestManager.rootNode = {
      items: [{
        groupDims: 'key1',
        aggr: { a: 1, b: 2},
      }],
    };
    render();
    expect(exportable).toBe(true);
  });

  // it('表格onChange 时，会触发分页', () => {
  //   let query;
  //   let lastQuery = {};
  //   report.filterManager = {
  //     updateDisplayQuery: (cb) => {
  //       query = cb(lastQuery);
  //       lastQuery = query;
  //     },
  //   };
  //   render();
  //   const table = instance.root.findByType(Table);
  //   table.props.onChange({ current: 123, pageSize: 23 }, {});
  //   expect(query.pageSize).toBe(23);
  //   expect(query.pageNo).toBe(1);
  //   table.props.onChange({ current: 234, pageSize: 23 }, {});
  //   expect(query.pageNo).toBe(234);
  // });

  it('表格onChange 时，会触发过滤', () => {
    let query;
    let lastQuery = {};
    columns = [{
      field: 'testField',
      filterField: 'key12345',
      filterMultiple: false,
    }];
    report.filterManager = {
      updateDisplayQuery: (cb) => {
        query = cb(lastQuery);
        lastQuery = query;
      },
    };
    render();
    const table = instance.root.findByType(Table);
    table.props.onChange({ }, {
      testField: [1234],
    });
    expect(query['key12345']).toBe(1234);
  });

  it('表格onChange 时，会触发排序', () => {
    let query;
    let lastQuery = {};
    columns = [{ }];
    report.filterManager = {
      updateDisplayQuery: (cb) => {
        query = cb(lastQuery);
        lastQuery = query;
      },
    };
    render();
    const table = instance.root.findByType(Table);
    table.props.onChange({ }, { }, { column: { }, order: 'descend', field: 'key1234' });
    expect(query.orderBy).toBe('key1234');
    expect(query.orderByType).toBe('desc');
    table.props.onChange({ }, { }, { column: { alternate: 'were'},  order: 'ascend', field: 'key1234' });
    expect(query.orderBy).toBe('were');
    expect(query.orderByType).toBe('asc');
  });

  // it('会根据query和rootNode上的信息设置分页数据', () => {
  //   report.requestManager = {
  //     rootNode: {
  //       page: {
  //         totalCount: 10000,
  //       },
  //     },
  //     query: {
  //       pageNo: 23,
  //       pageSize: 30,
  //     },
  //   };
    
  //   render();
  //   const table = instance.root.findByType(Table); 
  //   expect(table.props.pagination.pageSize).toBe(30);
  //   expect(table.props.pagination.total).toBe(10000);
  //   expect(table.props.pagination.current).toBe(23);
  // })
});
