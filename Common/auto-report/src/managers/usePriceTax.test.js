let queryTaxSwitch = jest.fn();

class PoiService {
  queryTaxSwitch = queryTaxSwitch;
}

jest.mock('@mtfe/next-biz/src/services/poi', () => PoiService);
const { renderHook, act } = require('@testing-library/react-hooks');
const { default: usePriceTax } = require('./usePriceTax');

describe('usePriceTax', () => {
  it('updateHavePriceTaxField调用后会查询税率开关状态', async () => {
    queryTaxSwitch.mockReturnValue(true);
    const { result, waitForNextUpdate } = renderHook(() => usePriceTax());
    expect(result.current.priceTaxEnabled).toBe(null);
    act(() => {
      result.current.updateHavePriceTaxField(true);
    });
    await waitForNextUpdate();
    expect(queryTaxSwitch.mock.calls.length).toBe(1);
    expect(result.current.priceTaxEnabled).toBe(true);
  });
});