import React from 'react';
import ReportFeeSelector, { SimplifiedFee } from '@mtfe/next-biz/src/components/Fee/ReportFeeSelector';
import { ID } from '@mtfe/next-biz/src/services/types';
import {
  Filter,
  FeeSelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: FeeSelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as FeeSelectorFilter;
  }

  getDeps() {
    return {
      PoiSelector: 'poiIds',
    };
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps, deps: { poiIds?: ID[] }) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <ReportFeeSelector
        size={props.size}
        multiple={!!multiple}
        placeholder="全部"
        {...otherProps}
        customFilter={deps}
      />
    );
  }

  getDisplayValue(fees?: SimplifiedFee | SimplifiedFee[]) {
    return toArray(fees)?.map(o => o.name).join('，') || '全部';
  }

  getValue(fees: SimplifiedFee | SimplifiedFee[] | undefined) {
    return toArray(fees)?.map(o => o.id);
  }
}
