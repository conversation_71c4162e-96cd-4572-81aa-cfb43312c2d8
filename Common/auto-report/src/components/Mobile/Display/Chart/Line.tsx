/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
  Data,
  Geometry,
  CanvasAttr,
} from '@antv/f2';
import { LineChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart from './useBasicChart';
import { Color, LinearGradient } from '../../../common/chartConst';

export default function LineChart(props: Props<LineChartProps>) {
  const {
    y,
    x,
    legend,
    showArea,
    width,
    height,
  } = props;
  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    // 添加主线
    const line = chart
      .line()
      .position(`${x.field}*${y.field}`);
    let area: Geometry | undefined;
    if (showArea) {
      area = chart.area()
        .position(`${x.field}*${y.field}`);
    }
    if (legend) {
      chart.legend(legend.field, {
        position: 'bottom',
        align: 'center',
      });
      line.color(legend.field, Color);
      area && area.adjust('stack');
      area && area.color(legend.field, Color);
    } else {
      line.color(Color);
      area && area.color(LinearGradient);
    }
    chart.tooltip({
      triggerOn: ['touchstart', 'touchmove', 'mousedown', 'mousemove'],
      triggerOff: ['touchend', 'mouseout'],
      showCrosshairs: true,
      showItemMarker: false,
      layout: 'vertical',
    });

    chart.axis(x.field, {
      label(text: string, index: number, total: number) {
        const textCfg: CanvasAttr = {};
        if (index === 0) {
          textCfg.textAlign = 'left';
        } else if (index === total - 1) {
          textCfg.textAlign = 'right';
        }
        return textCfg;
      },
    });

    // if (y1) {
    //   // 添加辅线
    //   const line1 = _chart
    //     .line()
    //     .position(`${x.field}*${y1.field}`)
    //     .shape('dash');
    //   if (legend) {
    //     line1.color(`${legend.field}`, Color);
    //   } else {
    //     line1.color(Color);
    //   }
    //   // 添加次要 Y 轴标题
    //   // scale[y1.field] = { alias: y1.label };
    //   // 添加次要 Y 轴
    //   _chart.axis(y1.field, { grid: null });
    // }
    // _chart.scale(scale);
    return chart;
  }, [x, y, legend]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    chart.source(data, {
      [x.field]: {
        range: [0, 1],
      },
    });
  }, [x]);

  const dims = React.useMemo(() => [x.field, legend?.field], [x, legend]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
  });

  return dom;
}

registorComponent('LineChart', LineChart);
