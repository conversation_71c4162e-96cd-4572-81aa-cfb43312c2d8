import Rules from './Rules';
import PipelineManager, { PipelineFuncType } from './PipelineManager';
import visible from './Linkages/visible';
import props from './Linkages/props';

export type GlobalValueType = {
  [id: string]: any;
};

export type GlobalPipelineFuncType = PipelineFuncType<GlobalValueType>; // 这里只需要返回改变的值

export class GlobalManager {
  private _state: GlobalValueType = {};
  private _pipelines: GlobalPipelineFuncType[] = [];
  private _pipelineManager = new PipelineManager();
  private _rules = new Rules();

  get state() {
    return this._state;
  }

  get pipelineManager() {
    return this._pipelineManager;
  }

  get rules() {
    return this._rules;
  }

  async init(options: any) {
    await this._pipelineManager.run(this._pipelines, this._state, options);
  }

  /**
   * 全局变量管理
   * @param pipeline GlobalPipelineFuncType 
   *  示例: globalManager.use(async (global) => { global.isChain = true; })
   */
  use(pipeline: GlobalPipelineFuncType) {
    this._pipelines.push(pipeline);
  }
}

export const manager = new GlobalManager();

manager.rules.addLinkage('visible', visible);
manager.rules.addLinkage('props', props);

export default manager;
