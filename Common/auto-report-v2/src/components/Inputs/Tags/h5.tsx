import React from 'react';
import { Tags } from '@mtfe/sjst-ui-next';
import withAdapter from '../../../core/withAdapter';
import { TagsProps } from './types';

const _Tags = (props: TagsProps) => {
  const { options, updateStatus, value, onChange } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const _options = React.useMemo(() => {
    const _tagMap: { [key: string]: string } = {};
    options.forEach((o) => {
      _tagMap[o.value] = o.title;
    });
    return _tagMap;
  }, [options]);

  const _onChange = React.useCallback(
    (value: string[]) => {
      onChange?.(
        options
          .filter((v) => value.indexOf(String(v.value)) !== -1)
          .map((v) => v.value)
      );
    },
    [options, onChange]
  );

  return (
    <Tags options={_options} value={value as string[]} onChange={_onChange} />
  );
};

export default withAdapter(_Tags, { name: 'Tags' });
