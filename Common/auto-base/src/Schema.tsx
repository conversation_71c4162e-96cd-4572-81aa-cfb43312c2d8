import { omit, startsWith } from 'lodash';
import loglevel from 'loglevel';
import mitt, { Emitter } from 'mitt';
import React from 'react';
import hoistNonReactStatic from 'hoist-non-react-statics';
import componentManager, { ComponentManager } from './ComponentManager';
import { useStore, StateObject } from './Contexts';
import { deepEqual, isReactNode } from './helper';
import { Config, ConfigProps, StateObjectValueType } from './types';

export interface ComponentProps<ValueType = {}> {
  value?: ValueType;
  defaultValue?: ValueType;
  onChange?: (value: ValueType, stateObject?: StateObject<ValueType>) => void;
  onChangeTemporary?: (
    value: ValueType,
    stateObject?: StateObject<ValueType>
  ) => void;
  children?: any;
  $$auto?: StateObject<ValueType>;
}

export type SchemaProps = {
  config: Config;
  options?: {
    key?: string;
    componentManager?: ComponentManager;
  };
};

const logger = loglevel.getLogger('Auto-SchemaComponent');

const idCaches = new Map<string, string>();

const getId = (name: string, dataKey?: string) => {
  if (dataKey && idCaches.has(dataKey)) {
    const id = idCaches.get(dataKey);
    if (id) {
      return id;
    }
  }
  const newId = `${name}@${Math.round(Math.random() * 100000000)}`;
  if (dataKey) {
    idCaches.set(dataKey, newId);
  }
  return newId;
};

const emitterCaches = new Map<string, Emitter>();

const getEmitter = (id: string) => {
  if (id && emitterCaches.has(id)) {
    const emitter = emitterCaches.get(id);
    if (emitter) {
      return emitter;
    }
  }
  const newEmitter = mitt();
  emitterCaches.set(id, newEmitter);
  return newEmitter;
};

const pathContext = React.createContext<{ id: string; path: string[] }>({
  id: '',
  path: [],
});

/**
 * 组件适配器
 * 除了布局类组件都建议满足该适配器的接口，以方便组件的联动等操作
 * @param Component
 * @param options
 */
export function withAdapter<T extends ComponentProps<P>, P>(
  Component: React.FC<T | any> & { auto: any },
  options: any
): React.FC<T | any> {
  const { childrenIsConfig = true, isAsync = false, mapProps = [] } =
    Component?.auto || {};
  const forwardRef = React.forwardRef((props: T, ref) => {
    const {
      value,
      defaultValue,
      onChange,
      children,
      $$auto,
      ...otherProps
    } = props;

    const { current: initStateObject } = React.useRef(
      mapProps.reduce(
        (c: any, k: string) => {
          c[k] = (props as any)?.[k];
          return c;
        },
        {
          ...($$auto?.$self || {}),
          value: value || defaultValue,
          defaultValue,
          status: isAsync ? 'pending' : 'done',
          otherProps,
        }
      ) as StateObjectValueType<P>
    );

    const { value: $self, store } = useStore<
      StateObjectValueType<P> | undefined
    >((store) => {
      if (!$$auto?.$self) {
        return;
      }
      return store.getters.withId<P>($$auto.$self.id) || initStateObject;
    });

    const stateObject = React.useMemo(() => new StateObject(store, $self), [$self])

    const componentChildren = React.useMemo(() => {
      if (children && childrenIsConfig) {
        return ((Array.isArray(children)
          ? children
          : [children]) as Config[]).map((c, i) => {
          const key = options?.key ? `${options?.key}-${i}` : `${i}`;
          return c
            ? React.createElement(Schema, {
                key,
                config: c,
                options: {
                  key,
                },
              })
            : '';
        });
      }
      return children;
    }, [children]);

    const _onChange = React.useCallback(
      (v: P | any) => {
        if (v?.target?.value) {
          v = v?.target?.value;
        }
        store.dispatch('update', {
          id: $self?.id,
          value: v,
        });
        onChange?.(v, new StateObject(store, $self?.id));
        if ($self?.relations?.length) {
          $self?.relations.forEach((relation) => {
            if (startsWith(relation, 'change->')) {
              setTimeout(() => {
                // 这里在主线程结束后的下一帧再触发
                stateObject.emit(relation.replace('change->', ''));
              }, 60);
            }
          });
        }
      },
      [onChange]
    );

    React.useEffect(
      () => {
        store.dispatch('update', {
          id: $self?.id,
          otherProps,
        });
      },
      Object.keys(otherProps).map((k: string) => (otherProps as any)?.[k])
    );

    React.useEffect(() => {
      store.dispatch('update', initStateObject);
      return () => {
        store.dispatch('unRegister', initStateObject);
      };
    }, []);

    return React.useMemo(() => {
      return (
        <Component
          {...otherProps}
          value={stateObject.$self?.value}
          defaultValue={stateObject.$self?.defaultValue}
          onChange={_onChange}
          children={componentChildren}
          $$auto={
            typeof Component === 'string'
              ? undefined
              : stateObject
          }
        />
      );
    }, [otherProps, _onChange, componentChildren, stateObject]);
  });

  hoistNonReactStatic(forwardRef, Component);
  return forwardRef;
}

const SchemaComponent = (props: SchemaProps) => {
  const { config: oldConfig, options } = props;

  const { id, path: currentPath } = React.useContext(pathContext);
  const { value: config, store } = useStore<Config>(
    (store) => {
      const stateObject = store.getters.getStateObject(id);
      return stateObject ? stateObject.complieConfig(oldConfig) : oldConfig;
    },
    [id, oldConfig]
  );

  const { current: emitter$ } = React.useRef(getEmitter(id));

  const { type, dataKey, relations, props: configProps } = config || oldConfig;

  const $self = React.useMemo(
    () => ({ id, name: type, path: currentPath, dataKey, relations, emitter$ }),
    [id, type, currentPath, dataKey, relations]
  );

  const otherConfigProps = React.useMemo(
    () => omit(configProps, ['children']),
    [configProps]
  );

  const [lazyComponent, setLazyComponent] = React.useState<React.ElementType>();
  const [otherProps, setOtherProps] = React.useState<ConfigProps>(otherConfigProps);
  const [children, setChildren] = React.useState<Config | undefined>(configProps?.children);

  const component = React.useMemo(() => {
    let _component: React.ElementType | string | undefined;

    // 支持一些基础类型
    if (['div', 'p', 'span', 'ul', 'li', 'ol', 'a', 'empty'].indexOf(type) !== -1) {
      _component = type;
    } else {
      const _componentManager = options?.componentManager || componentManager;
      _component = _componentManager.getComponent(type);
    }


    if (_component === 'empty') {
      return 'empty';
    }
    if ((typeof _component === 'object' && '$$typeof' in _component) || typeof _component === 'function') {
      return withAdapter(_component as any, { name: type });
    }
    if (!_component) {
      return React.forwardRef((_props: any, _) =>
        React.createElement(_component as string, _props, _props.children)
      )
    }
    return undefined;
  }, [type])


  // 组件更新
  React.useEffect(() => {
    if (!component) {
      (async () => {
        const _componentManager = options?.componentManager || componentManager;
        const _component = await _componentManager.getLazyComponent(type);
  
        if (!_component) {
          logger.warn(`配置化报表未找到组件：${type}，组件配置：`, props);
        } else {
          setLazyComponent(withAdapter(_component as any, { name: type }));
        }
      })();
    }
  }, [type, component]);

  // 这里每次都是函数 render 出来的所以每次 otherConfigProps 都是最新的，所以使用 state 来保存一下
  React.useEffect(() => {
    setOtherProps((oldOtherProps) => {
      const newOtherProps = { ...otherConfigProps };
      if (deepEqual(oldOtherProps, newOtherProps)) {
        return oldOtherProps;
      }
      return newOtherProps;
    });
  }, [otherConfigProps]);

  // children 更新
  React.useEffect(() => {
    setChildren((oldChildren) => {
      if (Object.is(oldChildren, configProps?.children)) {
        return oldChildren;
      }
      return configProps?.children;
    });
  }, [configProps?.children]);

  return React.useMemo(() => {
    const _component = component || lazyComponent;
    if (!_component || _component === 'empty') {
      return null;
    }
    const Component = _component;
    return (
      <Component {...otherProps} $$auto={new StateObject(store, $self)}>
        {children}
      </Component>
    );
  }, [component, lazyComponent, otherProps, children]);
};

export function Schema(props: SchemaProps) {
  const { type, dataKey } = props.config || {};
  const { path: parentPath } = React.useContext(pathContext);
  const PathContext = pathContext;
  PathContext.displayName = 'AT.Path.Context';

  const path = React.useMemo(() => {
    const id = getId(type || '', dataKey);
    return {
      id,
      path: parentPath.concat([id]),
    };
  }, [type, dataKey, parentPath]);

  if (isReactNode(props.config)) {
    return <>{props.config || null}</>;
  }

  if (!type) {
    return null;
  }

  return (
    <PathContext.Provider value={path}>
      <SchemaComponent key={path.id} {...props} />
    </PathContext.Provider>
  );
}

export function render(config: Config | Config[], options?: { key?: string }) {
  return (Array.isArray(config) ? config : [config]).map((c, i) => {
    const key = options?.key ? `${options?.key}-${i}` : `${i}`;
    return c
      ? React.createElement(Schema, {
          key,
          config: c as any,
          options: {
            key,
          },
        })
      : '';
  });
}

export default Schema;
