import React from 'react';
import Container from '../../Desktop/Layout/Container';
import { ScreenBlockProps, Props } from '../../../types/config';
import ReportComponent from '../../ReportComponent';
import './Block14.less';

export default function Block7(props: Props<ScreenBlockProps>) {
  const {
    cornerSize, title, type, className, ...otherProps
  } = props;
  const style: React.CSSProperties = {
    position: 'relative',
    padding: '20px 40px',
    ...otherProps,
  };

  const border = React.useMemo(() => {
    const cornerStyle: React.CSSProperties = {
      position: 'absolute',
    };
    const svgProps = { width: 24, height: 170 };
    return (
      <>
        <svg {...svgProps} style={{ ...cornerStyle, left: 0, top: 40 }}>
          <defs>
            <g id="auto-report-border-14-element">
              <linearGradient id="Fill-33_1_" gradientUnits="userSpaceOnUse" x1="-638.1829" y1="467.8889" x2="-637.3934" y2="467.5682" gradientTransform="matrix(21.1023 0 0 -167.0523 13469.5928 78218.6016)">
                <stop offset="0" stopColor="#52BFFD" />
                <stop offset="1" stopColor="#4C77FF" />
              </linearGradient>
              <polygon points="2.4,169.1 1,169.1 1,23.7 21.2,2 22.1,3 2.4,24.3 " opacity="0.2" fillRule="evenodd" clipRule="evenodd" fill="url(#Fill-33_1_)" />
              <linearGradient id="Fill-35_1_" gradientUnits="userSpaceOnUse" x1="-530.5255" y1="412.1215" x2="-529.736" y2="411.8008" gradientTransform="matrix(4.8061 0 0 -5.1778 2569.2837 2135.5432)">
                <stop offset="0" stopColor="#52BFFD" />
                <stop offset="1" stopColor="#4C77FF" />
              </linearGradient>
              <polygon points="21.6,0 24,2.6 21.6,5.2 19.2,2.6 " opacity="0.6" fillRule="evenodd" clipRule="evenodd" fill="url(#Fill-35_1_)" />
              <linearGradient id="Fill-37_1_" gradientUnits="userSpaceOnUse" x1="-472.7729" y1="388.2785" x2="-471.9835" y2="387.9578" gradientTransform="matrix(3.3983 0 0 -3.6611 1606.8428 1589.8639)">
                <stop offset="0" stopColor="#52BFFD" />
                <stop offset="1" stopColor="#4C77FF" />
              </linearGradient>
              <polygon points="0,170.8 3.4,170.8 3.4,167.2 0,167.2 " opacity="0.6" fillRule="evenodd" clipRule="evenodd" fill="url(#Fill-37_1_)" />
            </g>
          </defs>
          <use xlinkHref="#auto-report-border-14-element" />
        </svg>
        <svg
          {...svgProps}
          style={{
            ...cornerStyle,
            right: 0,
            top: 40,
            transform: 'scaleX(-1)',
          }}
        >
          <use xlinkHref="#auto-report-border-14-element" />
        </svg>
        <svg
          {...svgProps}
          style={{
            ...cornerStyle,
            left: 0,
            bottom: 40,
            transform: 'scaleY(-1)',
          }}
        >
          <use xlinkHref="#auto-report-border-14-element" />
        </svg>
        <svg
          {...svgProps}
          style={{
            ...cornerStyle,
            right: 0,
            bottom: 40,
            transform: 'scaleX(-1) scaleY(-1)',
          }}
        >
          <use xlinkHref="#auto-report-border-14-element" />
        </svg>
      </>
    );
  }, [cornerSize]);

  const titleBG = (
    <svg x="0px" y="0px" viewBox="0 0 171 39" className="auto-report-screen-block14-title-bg">
      <linearGradient id="auto-report-screen-block14-g2" x1="100%" y1="50%" x2="0%" y2="0%">
        <stop stopColor="#FFFFFF" stopOpacity="0" offset="30%" />
        <stop stopColor="#52BFFD" stopOpacity="0.46" offset="100%" />
      </linearGradient>
      <path d="M170.6,0.3H16L0.3,16v22.7h170.3V0.3z" fill="transparent" stroke="url(#auto-report-screen-block14-g2)" strokeWidth={0.6} />
    </svg>
  );

  const titleDom = title ? <ReportComponent>{title}</ReportComponent> : null;

  return (
    <Container {...style} className={[className, 'auto-report-screen-block14'].join(' ')}>
      {border}
      {titleDom && <div className="auto-report-block-title">{titleBG}{titleDom}</div>}
      <ReportComponent>{props.children}</ReportComponent>
    </Container>
  );
}
