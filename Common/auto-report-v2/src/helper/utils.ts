/* eslint-disable @typescript-eslint/no-explicit-any */
import { isEqualWith } from 'lodash';
import { ua } from "@mtfe/next-biz/es/utils/ua";
import { IV } from '@mtfe/sjst-report/es/ReportCore/utils';
import { OsType } from '../types';

interface SchedulerInteraction {
  id: number;
  name: string;
  timestamp: number;
}

function isNode(value: any) {
  return value && typeof value === 'object' && '$$typeof' in value;
}

/**
 * 深对比
 */
export const deepEqual = (value: any, other: any) => {
  return isEqualWith(value, other, (a, b) => {
    if (isNode(a) || isNode(b)) {
      return a === b;
    }
  });
};

export function getOsType(props?: { isPos?: boolean }): OsType {
  if ((window as any).report_env_is_h5) {
    return 'h5';
  }
  if (props?.isPos) {
    return 'pos';
  }
  return 'pc';
}

export function isBasicType(
  element: any
): element is string | number | boolean {
  switch (typeof element) {
    case 'string':
    case 'number':
    case 'boolean':
      return true;
    default:
      return false;
  }
}

export function isReactNode(
  element: any
): element is React.ReactElement | string | number | boolean {
  if (!element) return true;
  if ('$$typeof' in element) return true;
  return isBasicType(element);
}

const countMaps: { [name: string]: number } = {};
export function stepCount(name: string = '___default___') {
  if (!countMaps[name]) {
    countMaps[name] = 0;
  }
  countMaps[name]++;
  return countMaps[name];
}


export function profilerConsole(
  id: string,
  phase: "mount" | "update",
  actualDuration: number,
  baseDuration: number,
  startTime: number,
  commitTime: number,
  interactions: Set<SchedulerInteraction>,
) {
  console.log(`id: ${id}，${phase}，渲染耗时${actualDuration.toFixed(2)}，渲染成本${baseDuration.toFixed(2)}，start ${startTime.toFixed(2)}，commit ${commitTime.toFixed(2)}`, interactions)
}

export function isH5() {
  return Boolean((window as any).report_env_is_h5);
}

interface KNBProps {
  customKnb?: () => void; // 自定义
  url?: string; // 非h5时必传
}
export const linkMonitoring = (props: KNBProps) => {
  const { customKnb, url } = props;
  if (customKnb) {
    customKnb();
    return;
  }
  const KNB = (window as IV).KNB;
  // h5
  if (isH5()) {
    KNB.ready(() => {
      KNB.publish({
        type: "native",
        action: "erpbosspro:WebViewDidAppear",
        data: true,
      });
    });
  } else {
    if (!url) return;
    // win pos
    if (ua.os !== "android") {
      if ((window as IV).cefQuery) {
        // @ts-ignore
        const promise = new Promise<void>((resolve, reject) => {
          (window as IV).cefQuery({
            request: `report/WebViewDidAppear:${url}`,
            persistent: false,
            onSuccess() {
              resolve();
            },
            onFailure(code: number, msg2: string) {
              const err = new Error(msg2);
              Object.assign(err, {
                code,
              });
              reject(err);
            },
          });
        });
      }
    } else {
      // android pos
      KNB.ready(() => {
        KNB.publish({
          type: "native",
          action: "rmspos:WebViewDidAppear",
          data: url
        });
      });
    }
  }
};

export const resetYJErrorMsg = async (err?: IV, msgKey?: string) => {
  msgKey = msgKey ?? 'message';
  if (err?.[msgKey]) {
    err[msgKey] = '所选门店数过多或时间跨度过长，请缩小范围后查询'
  }
};