// 设置颜色环境变量
process.env.FORCE_COLOR = '1';

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const preCommitCheck = require('../mpack/bin/script/check-handler/pro-component');
const { performRmsGoodsCheck } = require('./rms-goods-check');

/**
 * 在根目录执行命令并返回对应的输出
 * @param {string} cmd 命令
 * @returns {string} 执行命令的输出
 */
const execSyncX = (cmd) => execSync(cmd, { cwd: path.resolve(__dirname, '../../') }).toString();

/**
 * 获取暂存区文件列表
 * @returns {Array<string>} 暂存区文件列表
 */
const getCachedFileList = () => {
  const output = execSyncX(`git diff --name-only --cached`);
  return output.split(`\n`).filter(Boolean);
};

/**
 * 检查文件是否存在
 * @param {Array<string>} fileList 文件列表
 * @returns {Array<string>} 存在的文件列表
 */
const filterExistingFiles = (fileList) => fileList.filter(fs.existsSync);

/**
* 正则规则：
* 以'/'开始和结束  第二个'/'后面包含u || 使用RegExp构造函数
* 在 JavaScript 中，只有 6 个修饰符。 都是小写igmsuy： https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_expressions
* 示例见: ./.pre-commit.test.ts
*/
const unicodeRegExp = /\/.*?\/[igmsy]*u[igmsy]*([^\w\/\-]|$)|RegExp\(.*,\s*(['"])[igmsy]*u[igmsy]*\2\)/

/**
 * 检查文件是否使用了Chrome49不兼容的 RegExp.prototype.unicode 语法
 * 设计说明：https://km.sankuai.com/collabpage/1862767504#
 * @returns {Array<string>} 使用了不兼容语法的文件列表
 */
const checkIncompatibleSyntax = () => {
  // 获取本次commit的diff信息
  const diffInfo = execSyncX(`git diff --cached`);
  // 在 Git 的 diff 输出中，\n+++ 是一个标记，用于表示一个新文件的开始。
  const files = diffInfo.split('\n+++').filter(Boolean).slice(1);
  const incompatibleFiles = [];

  files.forEach((file) => {
    // 提取文件名
    const name = file.substring(3, file.indexOf("\n"))
    // 只对tsx、ts、js文件进行检测
    if (!['.ts', '.tsx', '.js'].some(ext => name.includes(ext))) {
      return;
    }
    // 提取 Git diff 输出中所有添加的代码行，除了文件路径那一行: Git diff 输出中，以 + 开头的行表示添加的代码
    const addedLines = file.split('\n+').filter(Boolean).slice(1);
    const hasIncompatibleSyntax = addedLines.some((line) =>
      /**
       * 正则规则：
       * 以'/'开始和结束  第二个'/'后面包含u || 使用RegExp构造函数
       * 在 JavaScript 中，只有 6 个修饰符。 都是小写igmsuy： https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_expressions
       * ex: /\u{61}/guw、/\u{61}/gu、/\u{61}/ug、new RegExp('\u{61}',    'u')、new RegExp('\u{61}','guw')
       */
      /\/.*?\/[igmsy]*u[igmsy]*([^\w\/\-]|$)|RegExp\(.*,\s*(['"])[igmsy]*u[igmsy]*\2\)/.test(line)
    );

    if (hasIncompatibleSyntax) {
      incompatibleFiles.push(name);
    }
  });

  return incompatibleFiles;
};

/**
 * 主函数
 * 在各git仓库的git.hooks.pre-commit中调用
 */
const main = () => {
  const cachedFiles = getCachedFileList();
  const existingFiles = filterExistingFiles(cachedFiles);

  if (existingFiles.some((file) => file.endsWith('.ts') || file.endsWith('.js'))) {
    const incompatibleFiles = checkIncompatibleSyntax();

    if (incompatibleFiles.length) {
      console.error(
        '⚠️⚠️⚠️ 以下文件可能使用了RegExp.prototype.unicode，该语法不被chrome49兼容，请处理！'
      );
      incompatibleFiles.forEach((file) => console.error(file));
      process.exit(-1);
    }
  }

  const rmsGoodsCheckResult = performRmsGoodsCheck();
  
  if (!rmsGoodsCheckResult) {
    process.exit(1);
  }
  

  // 提交代码前的部分校验
  preCommitCheck({ type: 'commit' });
};

main();


module.exports = {
  unicodeRegExp
}