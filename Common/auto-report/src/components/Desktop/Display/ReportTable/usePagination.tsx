/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Pagination } from 'antd';
import type { PaginationProps } from 'antd/lib/pagination';
import './usePagination.less';
import {
  DataNode,
} from '../../../../types';

export { PaginationProps };

// 和 TableX 保持一致
export const TableXPaginationPageSize = 'TableXPaginationPageSize';

const defaultPaginationProps: PaginationProps = {
  /**
   * 当表格中无任何数据时，整个页签都隐藏
   * 当表格中有数据，但是数据条数<页码设置中的最小可设置条数（目前是10），页签全部隐藏
   * 当表格中有数据，但是数据条数<页码设置中的条数，只有1页的时候，隐藏翻页和跳页的部分，仅展示页码设置
   * 表格中数据条数大于1页，页签部分展示完整的
   */
  // hideOnSinglePage: true, 这会导致BUG pagesize是可以切换的，比如页面11条数据pagesize为10，此时不隐藏。当用户切到20页每条。
  // 页签就再也没有了。这个隐藏逻辑应该是正对每页最小分页10条的场景
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  defaultPageSize: 20,
  showTotal: (total: number) => (
    <span>
      共<strong>{total}</strong>条记录
    </span>
  ),
};

export default function usePagination(
  loading?: boolean,
  updateDisplayQuery?: (value: any) => void,
  query?: any,
  rootNode?: DataNode | null,
  showPagination?: boolean,
) {
  const [pageSize, setPageSize] = React.useState<number>(() => query?.pageSize || Number(window.localStorage.getItem(TableXPaginationPageSize)) || defaultPaginationProps.pageSize);

  React.useEffect(() => {
    setPageSize(size => query?.pageSize || size);
  }, [query?.pageSize]);

  const onChange = React.useCallback((_pageNo: number, _pageSize?: number) => {
    if (loading) return;
    updateDisplayQuery && updateDisplayQuery((lastQuery: any) => {
      const newQuery = {
        ...lastQuery,
        pageSize: _pageSize,
        pageNo: _pageNo,
      };
      return newQuery;
    });
  }, [updateDisplayQuery, loading]);

  const onShowSizeChange = React.useCallback((_pageNo: number, _pageSize: number) => {
    if (loading) return;
    onChange(_pageNo, _pageSize);
    setPageSize(_pageSize);
    window.localStorage.setItem(
      TableXPaginationPageSize,
      String(_pageSize),
    );
  }, [onChange, setPageSize, loading, TableXPaginationPageSize]);

  const current = query?.pageNo || 1;
  const total = rootNode?.page?.totalCount || rootNode?.items?.length;
  const className = React.useMemo(() => [
    'auto-report-table-pagination',
    (total || 0) < (pageSize || 0) ? 'size-changer-only' : null,
  ].filter(Boolean).join(' '), [total, pageSize]);

  if (
    // 当表格中无任何数据时，整个页签都隐藏
    !total
    // 当小于最小pageSize时隐藏
    || (total < 10 && current === 1)
  ) {
    return false;
  }

  if (showPagination === false) return null;

  return (
    <Pagination
      {...defaultPaginationProps}
      total={total}
      pageSize={pageSize || 20}
      current={current}
      className={className}
      onChange={onChange}
      onShowSizeChange={onShowSizeChange}
    />
  );
}
