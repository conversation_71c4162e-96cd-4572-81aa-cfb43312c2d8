import React from 'react';
import { ContainerProps, Props } from '../../../types/config';
import { registorComponent } from '../registor';
import ReportComponent from '../../ReportComponent';
import './Container.less';

export default function Container(props: Props<ContainerProps>) {
  const { children, className, ...style } = props;
  return (
    <div className={['auto-report-container', className].filter(Boolean).join(' ')} style={style}>
      <ReportComponent>{children}</ReportComponent>
    </div>
  );
}

registorComponent('Container', Container);
