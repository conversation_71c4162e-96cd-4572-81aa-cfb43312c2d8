/* eslint-disable @typescript-eslint/no-explicit-any */
import { isEqual, merge, omit, isPlainObject, isEqualWith, isArray, isObject, isEmpty } from 'lodash';
import classNames from 'classnames';
import React, { HtmlHTMLAttributes } from 'react';
import { useStore } from '@mtfe/auto-base';
import { apiRequestTime, manualFstScreenTime } from '@mtfe/next-biz/es/utils/pos';
import { Analytics } from '@mtfe/next-biz/es/utils/analytics';
import { ua } from '@mtfe/next-biz/es/utils/ua';
import { reportClickQuery, reportQueryRenderTime, reportSuccessQuery } from '@mtfe/next-biz/es/utils/reportQueryRenderLog';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import PipelineManager, {
  PipelineFuncOptionsType,
  PipelineType,
} from '../../../core/PipelineManager';
import serviceManager from '../../../core/ServiceManager';
import { dataContext, EnvType, stateContext } from '../../../contexts';
import { getQueryPersistence } from './helper';
import useRules, { useRulesX } from './useRules';
import { linkMonitoring } from '../../../helper/utils';
import {
  ObjectRefType,
  ReportQuery,
  ReportValueType,
  ReportQueryPersistence,
  ComponentProps,
  ObjectRefValueType,
} from '../../../types';
import { pipelineManager } from './pipelines';
import { getService } from '@mtfe/next-biz/src/services/user';
import { chainCancelDefaultQuery, pushReportOperationsLog } from '../../../services/common';

export { pipelineManager } from './pipelines';

function _isEmpty(v: any) {
  if (!v) {
    return true;
  }
  if (isArray(v) || isObject(v)) {
    return isEmpty(v);
  }
  return false;
}

function getSystem(env: EnvType, os: string) {
  if (env.os === 'pc') {
    return 'pc';
  }
  if (env.os === 'pos') {
    return os === 'winpos' ? 'windows' : 'android';
  }
  return os;
}

export interface ReportOptions<DataType = {}> extends PipelineFuncOptionsType {
  env: EnvType;
  refObject: ObjectRefType<ReportValueType<DataType>>;
}

export type QueryPersistenceType =
  | 'UrlQueryPersistence'
  | 'StoragePersistence'
  | ReportQueryPersistence;

export interface ReportProps<DataType>
  extends ComponentProps<ReportValueType<DataType>> {
  queryPersistence?: QueryPersistenceType; // query 处理方法，不受控，只接受初始值
  queryManageKey?: string; // 筛选项管理 key, 为空或不填则是不使用筛选项保存功能
  queryManageSaveCallback?: (refObject?: ObjectRefType<any>)=> void;
  isReportPage?: boolean; // 是否需要分页，默认 true
  pageKey?: string; // 页面 key，用于分页
  pipelines?: PipelineType<
    ReportValueType<DataType>,
    ReportOptions<DataType>
  >[];
  defaultPageSize?: number;
  callback?: (error?: Error) => void;
  hideSuccessMsg?: boolean;
}

const getOtherPropsByDataKey = (datakey: string, config: any): any => {
  if (Array.isArray(config)) {
    for (let i = 0; i < config.length; i++) {
      const c = config[i];
      const _props = getOtherPropsByDataKey(datakey, c);
      if (_props) {
        return _props;
      }
    }
  }
  if (isPlainObject(config)) {
    const keys = Object.keys(config || {});
    for (let i = 0; i < keys.length; i++) {
      const k = keys[i];
      if (config[k] === datakey) {
        return config;
      }
      const _props = getOtherPropsByDataKey(datakey, config[k]);
      if (_props) {
        return _props;
      }
    }
  }
  return undefined;
};

function Report<DataType>(
  props: ReportProps<DataType> & HtmlHTMLAttributes<Element>
) {
  const {
    queryPersistence,
    queryManageKey,
    queryManageSaveCallback,
    isReportPage = true,
    pageKey,
    children,
    pipelines,
    callback,
    baseRefObject,
    updateStatus,
    value,
    className,
    onChange,
    $$auto,
    hideSuccessMsg = false,
    defaultPageSize = 20,
    ...otherProps
  } = props;
  const defaultPageSizeRef = React.useRef<number>();

  const { current: _queryPersistence } = React.useRef(
    getQueryPersistence(queryPersistence)
  );

  const reset = React.useRef<boolean>();
  const init = React.useRef(false);
  const initQueryPersistenceValueRef = React.useRef(_queryPersistence?.read());
  const submitKey = React.useRef('');
  const isFirstLoad = React.useRef(true);
  const columnFilters = React.useRef<any>({});
  const firstRequestFinished = React.useRef<boolean>(false);

  const state = React.useRef<{
    init: boolean;
    queryPersistenceValue?: ReportQuery;
  }>({
    init: false,
    queryPersistenceValue: initQueryPersistenceValueRef.current,
  });
  // const [state, setState] = React.useState<{
  //   init: boolean;
  //   queryPersistenceValue?: ReportQuery;
  // }>({
  //   init: false,
  //   queryPersistenceValue: initQueryPersistenceValueRef.current,
  // });

  const { env = $$auto?.$global?.env } = React.useContext(dataContext);
  const autoState = React.useContext(stateContext);

  const refObject = useRefObject(baseRefObject);
  const { queryRefList, state: ruleState }
    = refObject?.version === 2
      ? useStore<any>(
          useRulesX(refObject.id, children, initQueryPersistenceValueRef)
        ).value
      : useRules(
          env,
          autoState,
          refObject as ObjectRefType<any>,
          children,
          initQueryPersistenceValueRef.current
        );

  const { onSubmit, onReset } = React.useMemo(() => {
    const _onSubmit = async (submitProps?: {
      query: ReportQuery; // 默认参数
      filters: any; // 字段过来参数
      isQuick?: boolean; // 是否是快捷方式
      isInit?: boolean; // 是否是初始化请求
      isReset?: boolean; // 是否是重置
    }) => {
      // 提交的变更了的筛选项
      const allFilters: String[] = [];
      const filters: String[] = [];
      const key = Math.random().toString(36).slice(2);
      submitKey.current = key;
      const { query, filters: _columnFilters, isQuick, isInit, isReset } = submitProps || {};
      if (_columnFilters) {
        columnFilters.current = _columnFilters;
      }
      if (ruleState.status === 'pending') {
        refObject?.logger.debug('report 重置状态');
        // pending 状态中，返回 init false 等完成之后再触发提交
        reset.current = isReset;
        init.current = false;
        state.current = { init: false, queryPersistenceValue: query };
        // setState({ init: false, queryPersistenceValue: query });
        return;
      }
      refObject?.analytics.startTime('report-submit');
      const pageTitle = refObject?.withChildrenName('PageHeader')?.[0]?.otherProps?.title;

      // 获取组件原始参数，并同步到组件 value 上
      const updateQueryRefs: Array<{
        ref: ObjectRefValueType<any>;
        value: any;
      }> = [];
      const originalQuery = {
        ...omit(query, ['pageNo', 'pageSize']),
        ...queryRefList?.reduce((previous: any, queryRef: any) => {
          if (queryRef.dataKey) {
            if (isQuick) {
              const _value = query ? query[queryRef.dataKey] : queryRef.defaultValue;
              const queryValue
                = queryRef.name === 'DatePicker'
                  ? queryRef.value || queryRef.defaultValue
                  : _value;
              previous[queryRef.dataKey] = queryValue;
              if (!isEqual(queryRef.value, queryValue)) {
                updateQueryRefs.push({
                  ref: queryRef,
                  value: queryValue,
                });
              }
            } else {
              const refValue
                = queryRef.temporaryValue === undefined
                  ? queryRef.value
                  : queryRef.temporaryValue;
              const queryValue
                = isReset && !queryRef.otherProps?.isCantReset
                  ? queryRef.defaultValue
                  : query?.[queryRef.dataKey] || refValue;
              previous[queryRef.dataKey] = queryValue;
              if (!isEqual(queryRef.value, queryValue)) {
                updateQueryRefs.push({
                  ref: queryRef,
                  value: queryValue,
                });
              }
            }
          }
          return previous;
        }, {} as ReportQuery),
      };

      setTimeout(() => {
        updateQueryRefs.forEach(({ ref, value: v }) => {
          ref?.emitter$?.emit('updateValue', v);
        });
      }, 60);

      updateStatus?.('pending', (oldValue: ReportValueType<DataType>) => ({
        ...oldValue,
        originalQuery,
      }));

      refObject?.analytics.startTime('report-item-get-query');
      const preData = (
        await Promise.all(
          Object.keys(originalQuery).map(async (key) => {
            const _queryValue = originalQuery[key];
            const _query = { [key]: _queryValue };
            const queryRef = queryRefList?.find((v: any) => v.dataKey === key);
            const _queryName = queryRef?.otherProps?.title || queryRef?.otherProps?.label;
            if (!_queryName) {
              refObject?.logger.warn(`该筛选组件未定义名称：`, queryRef);
            } else {
              allFilters.push(_queryName);
              if (!_isEmpty(_queryValue) && !isEqualWith(_queryValue, queryRef.defaultValue)) {
                filters.push(_queryName);
              }
            }
            const component = queryRef?.name
              ? refObject?.renderManager.getComponent(queryRef.name)
              : undefined;
            const getQuery = component?.prototype?.getQuery;
            if (getQuery) {
              const otherProps = getOtherPropsByDataKey(key, ruleState.config);
              const getOtherParams = component?.prototype?.getOtherParams?.() || {};
              const data = await getQuery(_query, {
                ...getOtherParams,
                ...(otherProps || queryRef?.otherProps),
                dataKey: key,
              });
              return {
                query: data?.query || _query,
                others: data?.others,
              };
            }
            return {
              query: _query,
            };
          })
        )
      ).reduce(
        (previous, current) => merge(previous, current),
        {} as ReportValueType<DataType>
      );

      const duration = refObject?.analytics.endTime(
        'report-item-get-query'
      );
      refObject?.logger.debug(`getQuery 耗时：${duration}`);

      refObject?.logger.debug(
        '开始发起请求, query:',
        query,
        'originalQuery:',
        originalQuery,
        'preData:',
        preData,
        'auto 页面耗时',
        refObject?.analytics.endTime('auto-report-start', { clear: false })
      );

      const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<
        ReportValueType<any[]>
        >;
        const pageHeaderRefObject = reportRefObject?.withChildrenName(
        'PageHeader'
      )?.[0] as ObjectRefType<string> | undefined;
      const title = pageHeaderRefObject?.value || '';

      reportClickQuery({ name: title });

      const sortQuery = {
        orderBy: query?.orderBy,
        orderByType: query?.orderByType,
      };

      // 默认每页条数
      if (!defaultPageSizeRef.current) {
        defaultPageSizeRef.current
          = serviceManager.getDefaultPageSize && pageKey
            ? await serviceManager.getDefaultPageSize(pageKey)
            : defaultPageSize;
      }

      if (query?.pageSize) {
        defaultPageSizeRef.current = query?.pageSize;
        if (serviceManager.setDefaultPageSize && pageKey) {
          serviceManager.setDefaultPageSize(pageKey, query.pageSize);
        }
      }
      const pageQuery = isReportPage
        ? {
            pageNo: query?.pageNo || 1,
            pageSize: defaultPageSizeRef.current,
          }
        : {};
      
      if (isFirstLoad.current && originalQuery?.timeRange) {
        const datePicker = queryRefList.find((i: any) => i?.name === 'DatePicker' && i?.dataKey === 'timeRange');
        if (datePicker && !datePicker.defaultValue) {
          datePicker.defaultValue = originalQuery.timeRange;
        }
      }

      try {
        const requestStart = Date.now();
        let _pipelines = pipelines;
        if (env.os === 'pc' && isFirstLoad.current) {
          const user = await getService();
          const isTll = await chainCancelDefaultQuery();
          if (isTll && user.isHeadOffice()) {
            _pipelines = [];
          } else {
            firstRequestFinished.current = true;
          }
        } else {
          firstRequestFinished.current = true;
        }
        if (env.os === 'pc') {
          pushReportOperationsLog();
        }
        const data = await (pipelineManager as PipelineManager<
          ReportValueType<DataType>
        >).run(
          [
            'login' as PipelineType<
              ReportValueType<DataType>,
              ReportOptions<DataType>
            >,
          ].concat(_pipelines || []),
          {
            ...preData,
            originalQuery,
            query: { ...preData.query, ...sortQuery, ...pageQuery },
            filters: columnFilters.current,
          },
          {
            env,
            refObject,
          }
        );
        // 是当前 submit 才更新数据
        if (submitKey.current === key) {
          updateStatus?.('done', { ...(data || {}), firstRequestFinished: firstRequestFinished.current });

          refObject?.logger.debug(
            `请求完毕，请求耗时：${refObject?.analytics.endTime(
              'report-submit'
            )}，data:`,
            data
          );

          reportSuccessQuery({
            name: title,
          })

          if (env.os === 'pos') {
            if (!hideSuccessMsg) {
              setTimeout(() => {
                serviceManager.message?.success?.('查询成功');
              }, 0);
            }
            if (isFirstLoad.current) {
              (window as any).__$log?.log(`【AutoReport2】数据接口请求时间 ----> ${Date.now() - requestStart}`);
              manualFstScreenTime(_queryPersistence?.read() || {}, 'AutoReport2');
              apiRequestTime(requestStart);
              const url = window.location.href;
              linkMonitoring({ url });
            }
          } else if (env.os === 'pc') {
            serviceManager.monitor?.end('PAGE_LOAD_TIME', {
              result: 0,
            });
          }
          reportQueryRenderTime(requestStart);
          callback?.();
        }
      } catch (e) {
        refObject?.logger.error('请求失败:', e);
        if (submitKey.current === key) {
          updateStatus?.('done', {
            originalQuery,
            alert: {
              type: 'error',
              message: e.message || '数据获取失败',
            },
            firstRequestFinished: firstRequestFinished.current,
          });
          if (env.os === 'pc' && isFirstLoad.current) {
            serviceManager.monitor?.end('PAGE_LOAD_TIME', {
              result: 1,
              error_msg: e.message || '数据获取失败',
            });
          }

          callback?.(e);
        }
      } finally {
        if (isInit) {
          // initQueryPersistenceValueRef 在初始化完成之后就不需要了
          initQueryPersistenceValueRef.current = undefined;
        }
        isFirstLoad.current = false;
        _queryPersistence?.write(originalQuery);
        setTimeout(() => {
          const a = new Analytics({ cid: 'c_eco_2r9gnm8y' })
          a.moduleClick('b_eco_hj9kdl6y_mc', {
            custom :  {
              os: env?.os,
              system: getSystem(env, ua.os),
              url: window.location.href,
              title: pageTitle,
              filters,
              allFilters,
            }
          });
        }, 300);
      }
    };

    const _onReset = () => {
      refObject?.logger.debug('重置筛选项');
      queryRefList?.forEach((queryRef: any) => {
        if (!queryRef.otherProps?.isCantReset) {
          if (!isEqual(queryRef.value, queryRef.defaultValue)) {
            queryRef?.emitter$?.emit('updateValue', queryRef.defaultValue);
          }
          if (!isEqual(queryRef.temporaryValue, queryRef.defaultValue)) {
            queryRef?.emitter$?.emit(
              'updateTemporaryValue',
              queryRef.defaultValue
            );
          }
        }
      });
    };

    return {
      onSubmit: _onSubmit,
      onReset: _onReset,
    };
  }, [queryRefList, ruleState]);

  React.useEffect(() => {
    refObject?.on('submit', onSubmit);
    return () => {
      refObject?.off('submit', onSubmit);
    };
  }, [onSubmit]);

  React.useEffect(() => {
    refObject?.on('reset', onReset);
    return () => {
      refObject?.off('reset', onReset);
    };
  }, [onReset]);

  // React.useEffect(() => {
  //   setState((preState) => {
  //     if (preState.init && init.current) {
  //       return preState;
  //     }
  //     init.current = ruleState.status === 'done';
  //     return { ...preState, init: init.current };
  //   });
  // }, [ruleState]);

  React.useEffect(() => {
    if (state.current.init && init.current) {
      return;
    }

    init.current = ruleState.status === 'done';
    state.current = { ...state.current, init: init.current };
    if (state.current.init) {
      const duration = refObject?.analytics.endTime('auto-report-start', {
        clear: false,
      });
      refObject?.logger.debug(
        `auto 页面组件初始化完成，auto-report 耗时：${duration}`
      );
      refObject?.emit('submit', {
        query: state.current.queryPersistenceValue,
        isReset: reset.current,
        isInit: true,
      });
    }
  }, [ruleState]);

  return React.useMemo(() => {
    return (
      // @ts-ignore
      <div
        className={classNames(['auto-report-page', className])}
        {...otherProps}
      >
        {refObject?.renderManager.render(ruleState.config)}
      </div>
    );
  }, [className, ruleState]);
}

// @ts-ignore
Report.auto = {
  childrenIsConfig: false,
  isAsync: true,
};

export default withAdapter(Report, {
  name: 'Report',
  isComponentRender: false,
});
