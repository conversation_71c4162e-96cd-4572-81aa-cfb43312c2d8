/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { ReactNode } from 'react';
import classNames from 'classnames';
import { useRefObject, withAdapter } from '../../../core/withAdapter';
import Slot from '../Slot';
import './h5.less';

type Props = {
  header: ReactNode;
  notice: ReactNode;
  tab: ReactNode;
  tooltip: ReactNode;
  filter: ReactNode;
  body: ReactNode;
  baseRefObject: any;
};

const PageComponents: (keyof Props)[] = [
  'header',
  'notice',
  'tab',
  'tooltip',
  'filter',
  'body',
];

/**
 * 页面顶层容器
 * @param props
 * @returns
 */
function PageContainer(props: Props) {
  const refObject = useRefObject(props.baseRefObject);

  const refPageHeaderObject = refObject?.withChildrenName('PageHeader')?.[0];
  const landscape = refPageHeaderObject?.value?.landscape;

  return React.useMemo(() => (
    <div className={classNames('auto2-root-container', landscape ? 'auto2-root-container-landscape' : '')}>
      {PageComponents.map((name) => (
        <Slot key={name} id={name}>
          {props[name]}
        </Slot>
      ))}
    </div>
  ), [props, landscape]);
}

export default withAdapter(PageContainer, { name: 'PageContainer' });
