{
  "compileOnSave": true,
  "compilerOptions": {
    "allowJs": true,
    "moduleResolution": "node",
    "removeComments": false, // 支持webpack async code split是通过代码注释给异步chunk加上自定义名称；
    "module": "esnext", // 为了防止构建过程中对 import 的错误处理，改为 commonjs
    "experimentalDecorators": true, // 支持@注解语法
    "strict": true,
    "strictFunctionTypes": false,
    "noUnusedLocals": true,
    "esModuleInterop": true,  // 兼容在 es6 module 中 import commonjs 包
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES5",
    "sourceMap": true,
    "importHelpers": true,
    "jsx": "react",
    "lib": ["dom", "es7", "esnext", "es6"],
    "downlevelIteration": true,
    "skipLibCheck": true,
  },
  "exclude": ["node_modules"],
  "include": ["node_modules/query-string"]
}
