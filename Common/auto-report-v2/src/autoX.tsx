/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Auto, globalManager } from '@mtfe/auto-base';
import loglevel from 'loglevel';
import serviceManager from './core/ServiceManager';
import { AutoReportProps } from './auto';
import Analytics from './helper/analytics';

import './auto.less';

const logger = loglevel.getLogger('Auto');

globalManager.use(async (data: any, options: AutoReportProps) => {
  const { env, queryTaxSwitch, cid } = options;
  data.analytics = new Analytics({ cid });

  logger.debug('auto 页面开始加载');
  data.analytics.pageView();
  data.analytics.startTime('auto-report-start');

  if (serviceManager.prerenderData) {
    const { user, isTaxOpen } = serviceManager.prerenderData;
    data.env = {
      os: env?.os,
      user,
      isTaxOpen: isTaxOpen || false,
      isChain: user.isChain,
    };
  } else {
    const [user, isTaxOpen] = await Promise.all([
      serviceManager.getUserService(),
      queryTaxSwitch ? serviceManager.queryTaxSwitch() : false,
    ]);
    data.env = {
      os: env?.os,
      user,
      isTaxOpen: isTaxOpen || false,
      isChain: user.isChain,
    };
  }

  if (env?.os) {
    // 全局加上 os
    window.document.body.classList.add(env.os);
  }
});

export function AutoReport<StateType = {}>(props: AutoReportProps<StateType>) {
  return <Auto {...props} />;
}

export default AutoReport;
