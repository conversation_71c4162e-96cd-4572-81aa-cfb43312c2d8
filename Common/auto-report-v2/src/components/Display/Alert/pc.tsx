import React from 'react';
import { Alert } from '@mtfe/sjst-antdx-next';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import { AlertProps, ReportAlertProps } from './types';
import { ObjectRefType, ReportValueType } from '../../../types';

const _Alert = (props: AlertProps) => {
  const { baseRefObject, message, type, updateStatus } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  const component = baseRefObject?.renderManager.render(message);

  if (!message) {
    return null;
  }

  return (
    <Alert message={component} type={type || 'warning'} showIcon closable />
  );
};

// 该组件用于展示 report 的消息
const _ReportAlert = (props: ReportAlertProps) => {
  const { baseRefObject, updateStatus } = props;

  const refObject = useRefObject(baseRefObject);
  const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<
    ReportValueType<any>
  >;
  const alert = reportRefObject?.value?.alert;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  if (!alert) {
    return null;
  }

  return React.useMemo(
    () => (
      <Alert
        message={alert.message}
        type={alert.type || 'warning'}
        showIcon
        closable
      />
    ),
    [alert]
  );
};

export const ReportAlert = withAdapter(_ReportAlert, { name: 'ReportAlert' });

export default withAdapter(_Alert, { name: 'Alert' });
