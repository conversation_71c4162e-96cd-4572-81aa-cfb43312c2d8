import React from 'react';
import { Button, Icon } from '@mtfe/sjst-antdx-next';
import ReportService, { ReportInfo } from '@mtfe/next-biz/es/services/reports';
import { doAction } from '@mtfe/next-biz/es/lib/actions';
import withAdapter from '../../../core/withAdapter';
import { StarProps } from './types';

function Star(props: StarProps) {
  const { baseRefObject, updateStatus, bid, customPathname } = props;

  const [loading, setLoading] = React.useState(false);
  const [reports, setReports] = React.useState<ReportInfo[]>([]);
  const [bookmarks, setBookmarks] = React.useState<ReportInfo[]>([]);

  const { current: service } = React.useRef(new ReportService());

  /** 当前页面是否已收藏 */
  const bookmarked = React.useMemo(
    () => Boolean(bookmarks.find(b => b.url.split('#')[0] === ( customPathname || window.location.pathname))),
    [bookmarks, customPathname],
  );

  /** 更新收藏状态 */
  const updateBookmarks = React.useMemo(() => {
    const report = reports.find(
      b => b.url.split('#')[0] === (customPathname || window.location.pathname),
    );
    if (!report) return null;

    return async (enable: boolean) => {
      if (enable) {
        const success = await doAction(service.addBookmark(report.id));
        if (success) {
          setBookmarks(b => b.concat(report));
        }
      } else {
        const success = await doAction(service.removeBookmark(report.id));
        if (success) {
          setBookmarks(b => b.filter(r => r.id !== report.id));
        }
      }
    };
  }, [reports, service, customPathname]);

  const onBookmarkClick = React.useCallback(async () => {
    baseRefObject?.analytics.moduleClick(bid);
    setLoading(true);
    await updateBookmarks?.(!bookmarked);
    setLoading(false);
  }, [bid, bookmarked, updateBookmarks]);

  const { onClick, icon } = React.useMemo(
    () => (loading
      ? {}
      : {
        onClick: onBookmarkClick,
        icon: (
          <Icon
            type="star"
            theme={bookmarked ? 'filled' : undefined}
            style={{ color: bookmarked ? '#FF6000' : undefined }}
          />
        ),
      }),
    [loading, onBookmarkClick],
  );

  React.useEffect(() => {
    (async () => {
      const [_reports, _bookmarks] = await Promise.all([
        service.getAll(),
        service.getBookmarks(),
      ]);
      setReports(_reports);
      setBookmarks(_bookmarks);
      updateStatus?.('done');
    })();
  }, []);

  return (
    <Button type="link" key="Star" onClick={onClick} loading={loading}>
      {props.children || (
        <>
          {icon}
          <span>{bookmarked ? '取消常用' : '设为常用'}</span>
        </>
      )}
    </Button>
  );
}

export default withAdapter(Star, { name: 'Star' });
