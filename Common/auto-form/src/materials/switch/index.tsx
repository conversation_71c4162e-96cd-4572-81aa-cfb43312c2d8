import React from 'react';
import {
  Switch,
} from 'antd';
import { SwitchProps } from 'antd/es/switch';
import { ValueControl } from '../types';

export type SwitchMaterialProps = {
  name: 'switch',
  props?: SwitchProps,
};

type Props = SwitchMaterialProps & ValueControl<SwitchProps['checked']>

export const SwitchMaterial = (p: Props) => {
  const {
    name, value, onChange, props,
    ...otherProps
  } = p;
  return (
    <Switch
      {...otherProps}
      {...props}
      checked={value}
      onChange={onChange}
    />
  );
};
