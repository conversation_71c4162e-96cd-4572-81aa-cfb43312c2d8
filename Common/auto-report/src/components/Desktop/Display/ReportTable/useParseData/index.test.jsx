const React = require('react');
const { useDims, default: userParseData } = require('./index');
const renderer = require('react-test-renderer');
const { UserContext } = require('@mtfe/next-biz/src/contexts/user');
const {
  ReportContext,
} = require('../../../../../types');

describe('useParseData', () => {
  describe('useDims', () => {
    let roots, render, update;
    const reset = () => {
      roots = [{
        groupDims: {
          key1: null,
          key2: null,
          key3: null,
        },
        items: [{
          groupDims: {
            key1: 1,
            key2: 1,
            key3: 1,
          },
          items: [{
            groupDims: {
              key1: 2,
              key2: 2,
              key3: 2,
            },
            items: [{
              groupDims: {
                key1: 3,
                key2: 3,
                key3: 3,
              }
            }]
          }],
        }],
      }];
    };

    beforeEach(() => {
      let result, testInstance;
      const Component = () => {
        result = useDims(roots).dims;
        return null;
      };
    
      render = () => {
        renderer.act(() => {
          testInstance = renderer.create(<Component />);
        });
        return result;
      };
      update = () => {
        renderer.act(() => {
          testInstance.update(<Component />);
        });
        return result;
      };
    });
  
    reset();
    afterEach(reset); 

    it('useDims 应该将树中所有维度信息抽离出来', () => {
      const dims = render();
      expect(dims.get('key1')).toStrictEqual({1: true, 2: true, 3: true});
      expect(dims.get('key2')).toStrictEqual({1: true, 2: true, 3: true});
      expect(dims.get('key3')).toStrictEqual({1: true, 2: true, 3: true});
    });
  });
  
  describe('userParseData', () => {
    let requestManager,
        toolsManager,
        priceTaxManager,
        showIndex,
        showColumnSorter,
        isHeadOffice,
        columns,
        render,
        update;
    const reset = () => {
      columns = [];
      requestManager = { };
      toolsManager = undefined;
      priceTaxManager = undefined;
      showIndex = false;
      showColumnSorter = false;
      isHeadOffice = false;
    };
    reset();
    beforeEach(() => {
      let result, testInstance;
      const Component = () => {
        result = userParseData({ showIndex, showColumnSorter, columns });
        return null;
      };
      const wrapContext = () => (
        <UserContext.Provider value={{
          isHeadOffice: () => isHeadOffice,
        }}>
          <ReportContext.Provider value={{
            requestManager,
            toolsManager,
            priceTaxManager,
          }}>
            <Component />
          </ReportContext.Provider>
        </UserContext.Provider>
      );
      render = () => {
        renderer.act(() => {
          testInstance = renderer.create(wrapContext());
        });
        return result;
      };
      update = () => {
        renderer.act(() => {
          testInstance.update(wrapContext());
        });
        return result;
      };
    });

    afterEach(reset);

    it('登陆视角为总部，会根据登陆视角做显示/隐藏', () => {
      isHeadOffice = true;
      columns = [{
        loginType: 'chain',
        field: 'chainKey',
      }, {
        loginType: 'poi',
        field: 'poiKey',
      }];
      const result = render();
      expect(result.tableColumns).toHaveLength(1);
      expect(result.tableColumns[0].dataIndex).toBe('chainKey');
    });

    it('登陆视角为门店，会根据登陆视角做显示/隐藏', () => {
      isHeadOffice = false;
      columns = [{
        loginType: 'chain',
        field: 'chainKey',
      }, {
        loginType: 'poi',
        field: 'poiKey',
      }];
      const result = render();
      expect(result.tableColumns).toHaveLength(1);
      expect(result.tableColumns[0].dataIndex).toBe('poiKey');
    });

    it('列设置showOnPriceTaxEnable之后，将会根据价税分离配置做显示/隐藏', () => {
      columns = [{
        field: 'key1',
        showOnPriceTaxEnable: true,
      }, {
        field: 'key2',
        showOnPriceTaxEnable: false,
      }];
      priceTaxManager = {
        priceTaxEnabled: false,
        updateHavePriceTaxField: jest.fn(),
      };
      const result = render();
      expect(result.tableColumns.length).toBe(1);
      expect(result.tableColumns[0].field).toBe('key2');
      expect(priceTaxManager.updateHavePriceTaxField.mock.calls[0][0]).toBe(true);
    });

    it('设置isDim之后，列会根据rootNode 的值做隐藏/显示', () => {
      requestManager.rootNode = {
        groupDims: { key1: null, key2: null },
      };

      columns = [{
        field: 'key1',
        isDim: true,
      }, {
        field: 'key2',
      }, {
        filed: 'key3',
        isDim: true,
      }, {
        field: 'key4',
      }];

      const result = render();
      expect(result.tableColumns.length).toBe(3);
      expect(result.tableColumns[0].dataIndex).toBe('key1');
      expect(result.tableColumns[1].dataIndex).toBe('key2');
      expect(result.tableColumns[2].dataIndex).toBe('key4');
    });

    it('默认会在工具栏里设置「字段设置」', () => {
      let tools;
      toolsManager = {
        updateTools: (t) => tools = t,
      };
      showColumnSorter = true;
      render();
      expect(tools.columnSorter.label).toBe('字段设置');
      showColumnSorter = false;
      render();
      expect(tools.columnSorter).toBe(null);
    });
  });
});
