import React from 'react';
import {
  DatePicker,
} from 'antd';
import { RangePickerProps, RangePickerValue } from 'antd/es/date-picker/interface';
import { ValueControl } from '../types';
import {
  number2moment,
  moment2number,
} from '../../utils/time';

export type RangePickerMaterialProps = {
  name: 'date-range-picker',
  props?: RangePickerProps,
};

type Value = number | undefined;

type Props = RangePickerMaterialProps & ValueControl<Value[] | undefined>;

const { RangePicker } = DatePicker;

export const RangePickerMaterial = (p: Props) => {
  const {
    value = [], onChange, name, props, ...otherProps
  } = p;
  // eslint-disable-next-line
  const v: RangePickerValue = [number2moment(value[0]), number2moment(value[1])] as any;
  return (
    <RangePicker
      {...otherProps}
      {...props}
      value={v}
      onChange={(e) => {
        onChange?.((Array.from(e)).map(moment2number));
      }}
    />
  );
};
