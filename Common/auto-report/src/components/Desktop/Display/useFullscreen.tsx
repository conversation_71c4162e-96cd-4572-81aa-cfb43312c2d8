import React from 'react';
import {
  Icon,
} from 'antd';
import ReactDom from 'react-dom';
import {
  ReportContext,
} from '../../../types';
import { REPORT_FULLSCREEN_PREFIX } from '../../common/constant';
import './useFullscreen.less';

export default function useFullScreen(props: {
  id: string,
  bid?: string,
}) {
  const report = React.useContext(ReportContext);
  const { toolsManager, analysisManager } = report || {};
  const { updateTools } = toolsManager || {};
  const { moduleClick } = analysisManager || {};
  const fullscreenDom = React.useRef<HTMLDivElement | null>(null);
  const [isFullScreen, updateIsFullScreen] = React.useState(false);

  const enterFullscreen = React.useCallback(() => {
    const div = document.createElement('div');
    document.body.appendChild(div);
    fullscreenDom.current = div;
    updateIsFullScreen(true);
  }, [fullscreenDom, updateIsFullScreen]);

  const exitFullscreen = React.useCallback(() => {
    const { current } = fullscreenDom;
    if (current) {
      document.body.removeChild(current);
      fullscreenDom.current = null;
    }
    updateIsFullScreen(false);
    moduleClick && moduleClick(props.bid);
  }, [updateIsFullScreen, moduleClick, props.bid]);

  React.useEffect(() => {
    if (!updateTools) return;
    updateTools({
      fullscreen: {
        icon: 'fullscreen',
        label: '全屏展示',
        onClick: enterFullscreen,
      },
    });
  }, [enterFullscreen]);

  return (element: React.ReactElement) => {
    if (isFullScreen && fullscreenDom.current) {
      return ReactDom.createPortal((
        <div id={`${REPORT_FULLSCREEN_PREFIX}${props.id}`} className="auto-report-fullscreen">
          <div className="auto-report-fullscreen-title" onClick={exitFullscreen}>
            <Icon type="arrow-left" /> 返回
          </div>
          {element}
        </div>
      ), fullscreenDom.current);
    } else {
      return element;
    }
  };
}
