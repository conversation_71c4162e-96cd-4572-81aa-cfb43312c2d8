/* eslint-disable @typescript-eslint/no-explicit-any */
import { eq, isNil, startsWith, values } from 'lodash';
import mitt from 'mitt';
import React from 'react';
import hoistNonReactStatic from 'hoist-non-react-statics';
import { useStore, Schema, componentManager } from '@mtfe/auto-base';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';
import { BaseObjectRef } from './ObjectRef';
import {
  dataContext,
  DataControlType,
  storeContext,
  StoreControlType,
} from '../contexts';
import Analytics from '../helper/analytics';
import {
  BaseObjectRefType,
  ObjectRefType,
  ComponentProps,
  ComponentStatus,
  RenderManagerType,
  UpdateStatusValueType,
} from '../types';

export const adapterContext = React.createContext<{ path: string[] }>({
  path: [],
});

export type Options = {
  // 组件名称
  name: string;
  // 是否执行 renderManager.render，默认 true
  isComponentRender?: boolean;
};

type StateValueType<DataType> = {
  status: ComponentStatus;
  data?: DataType;
  dataStatus: 'default' | 'change'; // default 不会触发 onChange
};

export function useRefObject<T>(
  baseObjectRef?: BaseObjectRefType<T>
): ObjectRefType<T> | undefined {
  if (baseObjectRef?.version === 2) {
    return baseObjectRef as ObjectRefType<T>;
  }
  const { control } = React.useContext(storeContext) as {
    control: StoreControlType<T>;
  };
  return baseObjectRef?.getObjectRef(control);
}

/**
 * 组件适配器
 * 除了布局类组件都建议满足该适配器的接口，以方便组件的联动等操作
 * @param Component
 * @param options
 */
export function withAdapter<T extends ComponentProps<P>, P>(
  Component: React.FC<T | any>,
  options: Options
): React.FC<T | any> {
  const { name, isComponentRender = true } = options || {};

  const AdapterContext = adapterContext;
  AdapterContext.displayName = 'AT.Adapter.Context';

  const forwardRef: React.FC | any = React.forwardRef((props: T, ref) => {
    const { path: parentPath } = React.useContext(adapterContext);
    const { control: dataControl, analytics, renderManager, businessModuleId } = React.useContext(
      dataContext
    ) as {
      control: DataControlType<P>;
      analytics: Analytics;
      renderManager: RenderManagerType;
      businessModuleId?: BusinessModule;
    };

    const { control } = React.useContext(storeContext) as {
      control: StoreControlType<P>;
    };

    const {
      isTemporary,
      dataKey,
      defaultValue,
      onChange,
      onChangeTemporary,
      children,
      relations,
      value,
      ...otherProps
    } = props;

    // 历史如果存在对应的 dataKey，则直接使用
    const { current: oldRefObjectValue } = React.useRef(
      dataKey ? control.withDataKey(dataKey) : undefined
    );

    const refId = React.useRef(
      oldRefObjectValue?.id ||
        `${name}@${Math.round(Math.random() * 100000000)}`
    );

    const refValue = React.useRef(
      value || oldRefObjectValue?.value || defaultValue
    );

    const { current: oldTemporaryValue } = React.useRef(
      oldRefObjectValue?.temporaryValue
    );

    const { current: emitter$ } = React.useRef(
      oldRefObjectValue?.emitter$ || mitt()
    );

    const componentChildren = React.useMemo(
      () => (isComponentRender ? renderManager.render(children) : children),
      [children]
    );

    const currentPath = React.useMemo(
      () => parentPath.concat([refId.current]),
      [refId.current]
    );

    const currentPathValue = React.useMemo(() => ({ path: currentPath }), [currentPath])

    const [stateValue, setStateValue] = React.useState<StateValueType<P>>({
      status: 'pending',
      data: refValue.current,
      dataStatus: 'default',
    });

    const [temporaryValue, setTemporaryValue] = React.useState<P | undefined>(
      isTemporary ? refValue.current : oldTemporaryValue // isTemporary 时候将 value 同步到 temporaryValue
    );

    const [componentValue, setComponentValue] = React.useState<P | undefined>(refValue.current)

    // 更新组件状态
    const updateStatus = React.useCallback(
      (status?: ComponentStatus, _value?: UpdateStatusValueType<P>) => {
        setStateValue((oldStateValue) => {
          const data =
            typeof _value === 'function'
              ? (_value as (v?: P) => P)(oldStateValue.data)
              : _value;

          if (status === oldStateValue.status && eq(data, oldStateValue.data)) {
            return oldStateValue;
          }
          return {
            status: status || oldStateValue.status,
            data: data || oldStateValue.data,
            dataStatus: oldStateValue.dataStatus,
          };
        });
      },
      []
    );

    const refObjectValue = React.useMemo(
      () => ({
        id: refId.current,
        path: currentPath,
        name,
        dataKey,
        status: stateValue.status,
        value: stateValue.data,
        defaultValue,
        emitter$,
        temporaryValue,
        otherProps,
      }),
      [
        refId.current,
        currentPath,
        stateValue,
        defaultValue,
        emitter$,
        temporaryValue,
      ]
    );

    const baseRefObject = React.useMemo(
      () =>
        new BaseObjectRef(
          {
            ...refObjectValue,
          },
          analytics,
          renderManager
        ),
      [refObjectValue, analytics, renderManager]
    );

    const refObject = React.useMemo(() => baseRefObject.getObjectRef(control), [
      baseRefObject,
      control,
    ]);

    React.useImperativeHandle(ref, () => refObjectValue, [refObjectValue]);

    React.useEffect(() => {
      dataControl?.register(refObjectValue);
      return () => {
        dataControl?.unRegister(refObjectValue);
      };
    }, [refObjectValue.id]);

    React.useEffect(() => {
      dataControl?.update(refObjectValue);
    }, [
      refObjectValue.status,
      refObjectValue.value,
      refObjectValue.temporaryValue,
    ]);

    React.useEffect(() => {
      if (stateValue.dataStatus === 'change' && !isNil(refObject?.value)) {
        onChange?.(refObject?.value, refObject);
        if (relations?.length) {
          relations.forEach((relation) => {
            if (startsWith(relation, 'change->')) {
              setTimeout(() => {
                // 这里在主线程结束后的下一帧再触发
                refObject.emit(relation.replace('change->', ''));
              }, 60);
            }
          });
        }
      }
    }, [refObject.value]);

    React.useEffect(() => {
      if (!isNil(refObject?.temporaryValue)) {
        onChangeTemporary?.(refObject?.temporaryValue, refObject);
      }
    }, [refObject.temporaryValue]);

    React.useEffect(
      () => {
        setComponentValue((preComponentValue) => {
          const newComponentValue = isTemporary ? temporaryValue : stateValue.data;
          if (eq(preComponentValue, newComponentValue)) {
            return preComponentValue;
          }
          return newComponentValue;
        })
      },
      [isTemporary, temporaryValue, stateValue.data]
    );

    React.useEffect(() => {
      emitter$.on('updateValue', (data: P) => {
        setTemporaryValue(data);
        setStateValue((oldStateValue) => ({ ...oldStateValue, data }));
      });
      emitter$.on('updateTemporaryValue', (data: P) => {
        setTemporaryValue(data);
      });
      return () => {
        emitter$.all?.clear?.();
      };
    }, []);

    const _onChange = React.useCallback(
      (data: P) => {
        setTemporaryValue(data);
        if (!isTemporary) {
          setStateValue((oldStateValue) => ({
            ...oldStateValue,
            data,
            dataStatus: 'change',
          }));
        }
      },
      [isTemporary]
    );

    Component.displayName = `AdapterComponent.${name}`;
    const component = React.useMemo(
      () => (
        <Component
          businessModuleId={businessModuleId}
          {...props}
          baseRefObject={baseRefObject}
          updateStatus={updateStatus}
          value={componentValue}
          onChange={_onChange}
        >
          {componentChildren}
        </Component>
      ),
      [
        ...values(props),
        componentChildren,
        baseRefObject,
        updateStatus,
        componentValue,
        _onChange,
      ]
    );

    return (
      <AdapterContext.Provider value={currentPathValue}>
        {component}
      </AdapterContext.Provider>
    );
  });

  const forwardRefX: React.FC | any = React.forwardRef((props: T, ref) => {
    const { $$auto } = props;
    const { store } = useStore();

    const baseRefObject = React.useMemo(
      () =>  {
        const b = new BaseObjectRef<P>(
          {
            ...$$auto.$self,
          },
          store.getters.$global.state.analytics,
          {
            getComponent(name) {
              return componentManager.getComponent(name);
            },
            render(config, options) {
              return (Array.isArray(config) ? config : [config]).map((c, i) => {
                const key = options?.key ? `${options?.key}-${i}` : `${i}`;
                return c
                  ? React.createElement(Schema, {
                      key,
                      config: c as any,
                      options: {
                        key,
                      },
                    })
                  : '';
              });
            },
          } as RenderManagerType,
          2
        );
        b.emit = $$auto.emit.bind($$auto);
        return b;
      },
      [$$auto]
    );

    const [stateValue, setStateValue] = React.useState<StateValueType<P>>({
      status: 'pending',
      data: baseRefObject.value,
      dataStatus: 'default',
    });

    // 更新组件状态
    const _updateStatus = React.useCallback(
      (status?: ComponentStatus, _value?: UpdateStatusValueType<P>) => {
        setStateValue((oldStateValue) => {
          const data =
            typeof _value === 'function'
              ? (_value as (v?: P) => P)(oldStateValue.data)
              : _value;

          if (status === oldStateValue.status && eq(data, oldStateValue.data)) {
            return oldStateValue;
          }
          return {
            status: status || oldStateValue.status,
            data: data || oldStateValue.data,
            dataStatus: oldStateValue.dataStatus,
          };
        });
      },
      []
    );

    React.useEffect(() => {
      $$auto.updateStatus?.(stateValue.status, stateValue.data);
    }, [stateValue]);

    React.useImperativeHandle(ref, () => baseRefObject, [baseRefObject]);
    return (
      <Component
        {...props}
        baseRefObject={baseRefObject}
        updateStatus={_updateStatus}
      />
    );
  });

  forwardRef.displayName = name;
  forwardRefX.displayName = name;
  forwardRef.prototype = Component.prototype;
  forwardRefX.prototype = Component.prototype;
  hoistNonReactStatic(forwardRef, Component);
  hoistNonReactStatic(forwardRefX, Component);

  forwardRefX.auto = {
    ...(forwardRefX.auto || {}),
    mapProps: ['dataKey', 'relations'],
  };

  forwardRef.auto = {
    type: 'autoAdapter',
    version: 1,
    component: forwardRefX,
  };
  return forwardRef;
}

export default withAdapter;
