import React from 'react';
import Container from '../../Desktop/Layout/Container';
import {
  ScreenBlockProps, Props, ShowSvgDirectionType, hideBorderType,
} from '../../../types/config';
import ReportComponent from '../../ReportComponent';
import './Block7.less';

export const ShineLine = (
  <div
    style={{
      background: 'linear-gradient(90deg, transparent 0%, #A6DFFF 50%, transparent 100%)',
      position: 'absolute',
      top: 0,
      left: '25%',
      right: '25%',
      height: 2,
    }}
  />
);

export default function Block7(props: Props<ScreenBlockProps>) {
  const {
    cornerSize, title, type, className, hideLine, svgDirection, hideBorder, ...otherProps
  } = props;
  const style: React.CSSProperties = {
    background: 'rgba(255,255,255,0.06)',
    // boxShadow: 'rgba(255,255,255,0.06) 0px 0px 40px inset',
    border: '1px solid rgba(77, 118, 255, 0.3)',
    position: 'relative',
    padding: '20px 40px',
    ...otherProps,
  };
  const getStyle = React.useMemo(() => {
    switch (hideBorder) {
      case hideBorderType.left:
        return {
          ...style,
          borderLeft: 'none',
        };
      case hideBorderType.right:
        return {
          ...style,
          borderRight: 'none',
        };
      case hideBorderType.top:
        return {
          ...style,
          borderTop: 'none',
        };
      case hideBorderType.bottom:
        return {
          ...style,
          borderBottom: 'none',
        };
      case hideBorderType.leftAndRight:
        return {
          ...style,
          borderLeft: 'none',
          borderRight: 'none',
        };
      case hideBorderType.none:
        return style;
      default:
        return style;
    }
  }, [hideBorder]);
  const getSvgNode = React.useMemo(() => {
    const cornerStyle: React.CSSProperties = {
      position: 'absolute',
    };
    const size = cornerSize || 40;
    const svgProps = { width: size, height: size };
    const defs = (
      <>
        <defs>
          <g id="auto-report-border-7-element" fill="#52BFFD" fillOpacity="0.1">
            <path d="M2,2 L2,38 C2,39.1045695 1.1045695,40 -7.10542736e-15,40 L-3.55271368e-16,2 L-3.55271368e-15,0 L40,0 C40,1.1045695 39.1045695,2 38,2 L2,2 Z" />
            <path d="M4,4 L4,16 C4,18.209139 2.209139,20 8.8817842e-16,20 L3.55271368e-15,-3.6739404e-16 L4,3.6739404e-16 L20,0 C20,2.209139 18.209139,4 16,4 L4,4 Z" />
          </g>
        </defs>
        <use xlinkHref="#auto-report-border-7-element" />
      </>
    );

    const lt = <svg {...svgProps} style={{ ...cornerStyle, left: 0, top: 0 }}>{defs}</svg>;
    const rt = (
      <svg
        {...svgProps} style={{
          ...cornerStyle, right: 0, top: 0, transform: 'rotate(90deg)',
        }}
      >{defs}</svg>
    );
    const lb = (
      <svg
        {...svgProps} style={{
          ...cornerStyle, left: 0, bottom: 0, transform: 'rotate(-90deg)',
        }}
      >{defs}</svg>
    );
    const rb = (
      <svg
        {...svgProps} style={{
          ...cornerStyle, right: 0, bottom: 0, transform: 'rotate(-180deg)',
        }}
      >{defs}</svg>
    );
    if (svgDirection) {
      switch (svgDirection) {
        case ShowSvgDirectionType.none:
          return null;
        case ShowSvgDirectionType.left:
          return (<>{lt}{lb}</>);
        case ShowSvgDirectionType.right:
          return (<>{rt}{rb}</>);
        case ShowSvgDirectionType.top:
          return (<>{lt}{rt}</>);
        case ShowSvgDirectionType.bottom:
          return (<>{lb}{rb}</>);
        default:
          return (<>{lt}{rt}{lb}{rb}</>);
      }
    } else {
      return (<>{lt}{rt}{lb}{rb}</>);
    }
  }, [svgDirection]);

  const border = React.useMemo(() => (
    <>
      {!hideLine && ShineLine}
      {getSvgNode}
    </>
  ), [cornerSize]);
  const titleDom = title ? <ReportComponent>{title}</ReportComponent> : null;
  return (
    <Container {...getStyle} className={[className, 'auto-report-screen-block7'].join(' ')}>
      {border}
      {titleDom && <div className="auto-report-block-title">{titleDom}</div>}
      <ReportComponent>{props.children}</ReportComponent>
    </Container>
  );
}
