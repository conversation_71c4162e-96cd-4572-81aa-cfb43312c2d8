import _ from 'lodash';
import React from 'react';
import { REPORT_FULLSCREEN_PREFIX, REPORT_TABLE_PREFIX } from '../../../common/constant';
import { ScrollerContext } from '../../../../types';

export default function useFixedHeader(props: {
  tableId?: string,
}) {
  const scroller = React.useContext(ScrollerContext);
  React.useLayoutEffect(() => {
    if (typeof MutationObserver === 'undefined') return;
    const reportFullscreenDom = document.getElementById(`${REPORT_FULLSCREEN_PREFIX}${props.tableId}`);
    const isUseScrollerFixed = scroller?.isFixed && scroller?.domRef?.current && !reportFullscreenDom;
    const reportTableDom = document.getElementById(`${REPORT_TABLE_PREFIX}${props.tableId}`);
    const container = reportTableDom?.querySelector('.ant-table') as HTMLDivElement | undefined;
    const containerY = (isUseScrollerFixed ? scroller?.domRef?.current : container) as HTMLDivElement | undefined;
    if (!container || !containerY || !reportTableDom) return;
    const topButton = scroller?.domRef?.current?.querySelector('.goto-top-button');
    const table = reportTableDom.querySelector('.ant-table-content');
    const headers = reportTableDom.querySelectorAll('thead');
    const footers = reportTableDom.querySelectorAll('tbody>tr.total-row:last-child') as NodeListOf<HTMLTableRowElement> | undefined;
    const lefts = reportTableDom.querySelector('.ant-table-fixed-left') as HTMLDivElement | undefined;
    let offsetTop = 0;
    let offsetBottom = 0;
    let detached = false;
    const updateHeaders = (scrollTop: number) => {
      if (!table || !containerY) {
        _.forEach(headers || [], (head) => {
          head.style.transform = '';
          container?.classList.remove('report-header-fixed');
        });
      } else {
        let y = Math.round(offsetTop + scrollTop);
        if (y < 0) y = 0;
        _.forEach(headers || [], (head) => {
          if (y === 0) {
            head.style.transform = '';
            container?.classList.remove('report-header-fixed');
          } else {
            head.style.transform = `translateY(${y - 1}px)`;
            if (!container?.classList.contains('report-header-fixed')) {
              container?.classList.add('report-header-fixed');
            }
          }
        });
      }
    };

    const updateFoorers = (scrollTop: number) => {
      if (!table || !containerY) {
        _.forEach(footers || [], (footer) => {
          footer.style.transform = '';
          container?.classList.remove('report-footer-fixed');
        });
      } else {
        _.forEach(footers || [], (footer, index) => {
          let y = Math.round(scrollTop - offsetBottom);
          if (y > 0) y = 0;
          if (y === 0) {
            footer.style.transform = '';
            container?.classList.remove('report-footer-fixed');
          } else {
            const scrollBarHeight = (containerY?.offsetHeight || 0) - (containerY?.clientHeight || 0);
            y -= scrollBarHeight;
            footer.style.transform = `translateY(${y}px)`;
            if (!container?.classList.contains('report-footer-fixed')) {
              container?.classList.add('report-footer-fixed');
            }
          }
        });
      }
    };

    const updateLefts = () => {
      if (!lefts) return;
      if (!container) {
        lefts.style.transform = '';
      } else {
        lefts.style.transform = `translateX(${container.scrollLeft}px)`;
      }
    };

    const updateScrollButton = (scrollTop: number) => {
      if (containerY?.offsetHeight && scrollTop > containerY?.offsetHeight) {
        if (!topButton?.classList.contains('show')) {
          topButton?.classList.add('show');
        }
      } else {
        topButton?.classList.remove('show');
      }
    };

    const updateOffset = (cb: () => void) => {
      if (detached) return;
      if (!table || !containerY) {
        offsetTop = 0;
        offsetBottom = 0;
        return;
      }
      const tableRect = table.getBoundingClientRect();
      const containerRect = containerY.getBoundingClientRect();
      offsetTop = containerRect.top - tableRect.top - containerY.scrollTop;
      offsetBottom = tableRect.bottom - containerRect.bottom + containerY.scrollTop;
      cb();
    };

    const update = () => {
      const scrollTop = containerY.scrollTop;
      updateHeaders(scrollTop);
      updateFoorers(scrollTop);
      updateLefts();
      updateScrollButton(scrollTop);
    };

    const updateSize = () => {
      updateOffset(() => update());
    };

    container?.addEventListener('scroll', update);
    if (isUseScrollerFixed) {
      containerY?.addEventListener('scroll', update);
    }
    window.addEventListener('resize', updateSize);
    updateSize();

    let lastContainerWidth = container?.scrollWidth || 0;
    const observerX = new MutationObserver(() => {
      if (container?.scrollWidth === lastContainerWidth) {
        return;
      }
      lastContainerWidth = container?.scrollWidth || 0;
      updateSize();
    });

    if (container) {
      observerX.observe(container, { attributes: true, childList: true, subtree: true });
    }

    let lastContainerHeight = containerY?.scrollHeight || 0;
    const observerY = new MutationObserver(() => {
      if (containerY?.scrollHeight === lastContainerHeight) {
        return;
      }
      lastContainerHeight = containerY?.scrollHeight || 0;
      updateSize();
    });

    if (containerY) {
      observerY.observe(containerY, { attributes: true, childList: true, subtree: true });
    }

    return () => {
      detached = true;
      container?.removeEventListener('scroll', update);
      if (isUseScrollerFixed) {
        containerY?.removeEventListener('scroll', update);
      }
      window.removeEventListener('resize', updateSize);
      observerX.disconnect();
      observerY.disconnect();
      // 这里需要把之前的样式还原一下
      _.forEach(headers || [], (head) => {
        head.style.transform = '';
        head.classList.remove('fixed');
      });
      _.forEach(footers || [], (footer) => {
        footer.style.transform = '';
        footer.classList.remove('fixed');
      });
    };
  });
}
