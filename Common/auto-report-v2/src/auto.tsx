/* eslint-disable @typescript-eslint/no-explicit-any */
import { isEqual, throttle } from 'lodash';
import React from 'react';
import loglevel from 'loglevel';
import { MonitorReport } from '@mtfe/next-biz/es/utils/rmsMonitor';
import { renderSwitchStatus } from '@mtfe/next-biz/es/services/commonSwitch';
import {
  dataContext, EnvType, stateContext, storeContext,
} from './contexts';
import componentManager from './core/ComponentManager';
import serviceManager from './core/ServiceManager';
import Analytics from './helper/analytics';
import Loading from './components/Core/Loading';
import { BusinessModule } from '@mtfe/next-biz/es/services/org';
import {
  BaseComponentProps,
  ObjectRefValueType,
  RenderManagerComponentType,
} from './types';

import './auto.less';

const logger = loglevel.getLogger('Auto');

type StoreDispatchType =
  | {
    type: 'register';
    payload: ObjectRefValueType<any>;
  }
  | {
    type: 'unRegister';
    payload: ObjectRefValueType<any>;
  }
  | {
    type: 'update';
    payload: ObjectRefValueType<any>;
  }
  | {
    type: 'recycle';
  };

const storeReducer = (
  state: ObjectRefValueType<any>[],
  action: StoreDispatchType,
) => {
  switch (action.type) {
    case 'register':
      const registerIndex = state.findIndex(v => v.id === action.payload?.id);
      if (registerIndex !== -1) {
        state[registerIndex] = action.payload;
      } else {
        state.push(action.payload);
      }
      return state.filter(Boolean);
    case 'unRegister':
      const unRegisterIndex = state.findIndex(
        v => v.id === action.payload?.id,
      );
      if (unRegisterIndex !== -1) {
        state[unRegisterIndex].isDelete = true;
      }
      return state.filter(Boolean);
    case 'update':
      const updateIndex = state.findIndex(
        v => v.id === action.payload?.id,
      );
      if (updateIndex !== -1) {
        state[updateIndex].status = action.payload.status;
        state[updateIndex].value = action.payload.value;
        state[updateIndex].temporaryValue = action.payload.temporaryValue;
      }
      return state.filter(Boolean);
    case 'recycle':
      return state.filter(v => !v.isDelete);
    default:
      return state;
  }
};

export interface AutoReportProps<StateType = {}>
  extends BaseComponentProps<StateType> {
  // 加载状态
  loading?: Boolean;
  // 环境信息
  env?: EnvType;
  // 税价开关
  queryTaxSwitch?: boolean;
  // 埋点信息
  cid?: string;
  /**
   * 额外 state 数据，用于外部数据在 auto-report 中控制组件联动或内容展示等逻辑
   */
  state?: StateType;
  /**
   * 额外 components 组件，只对当前 auto-report 生效
   */
  components?: RenderManagerComponentType[];
  // 业务开关key
  switchKeys?: string[];
  switchCallBack?: (status: {[key: string]: boolean}) => void;
  // 集团开关
  chainSwitchKeys?: string[];
  businessModuleId?: BusinessModule;
  preventPV?: boolean; // 不需要执行pageView
}

export function AutoReport<StateType = {}>(props: AutoReportProps<StateType>) {
  const {
    env,
    queryTaxSwitch,
    cid,
    state: stateData,
    components,
    children,
    switchKeys,
    chainSwitchKeys,
    switchCallBack,
    businessModuleId,
    preventPV
  } = props;

  const DataContext = dataContext;
  const StateContext = stateContext;
  const StoreContext = storeContext;
  const [envState, setEnvState] = React.useState<EnvType>();
  const [state, dispatch] = React.useReducer(storeReducer, []);

  const { current: control } = React.useRef({
    register: (objectRef: ObjectRefValueType<any>) => {
      dispatch({ type: 'register', payload: objectRef });
    },
    unRegister: (objectRef: ObjectRefValueType<any>) => {
      dispatch({ type: 'unRegister', payload: objectRef });
    },
    update: (objectRef: ObjectRefValueType<any>) => {
      dispatch({ type: 'update', payload: objectRef });
    },
    recycle: throttle(dispatch, 0, { leading: false }),
  });

  const { current: analytics } = React.useRef(new Analytics({ cid }));

  const renderManager = React.useMemo(
    () => componentManager.getRenderManager(
        components?.filter(v => v.os === env?.os),
    ),
    [components, env],
  );

  const _data = React.useMemo(
    () => ({
      env: envState,
      control,
      analytics,
      renderManager,
      businessModuleId,
    }),
    [envState, control, analytics, renderManager, businessModuleId,],
  );

  const _state = React.useMemo(() => stateData || {}, [stateData]);

  const _store = React.useMemo(
    () => ({
      state,
      control: {
        getRefList: () => state,
        withId: (id: string) => state.find(v => v.id === id),
        withDataKey: (dataKey: string) => state.find(v => v.dataKey === dataKey),
        withByParentPath: (parendPath: string[]) => state.filter(v => isEqual(v.path.slice(0, -1), parendPath)),
        withByChildrenName: (currentPath: string[], childrenName: string) => state.filter(
          v => v.path.join('/').startsWith(currentPath.join('/'))
              && v.name === childrenName,
        ),
      },
    }),
    [state],
  );

  const _loading = props.loading || !_data.env;

  const component = React.useMemo(() => _loading ? null : renderManager.render(children), [
    _loading, children,
  ]);

  // 回收多余的内存
  React.useEffect(() => {
    // logger.debug('State 发生变更', state);
    if (state.filter(v => v.isDelete).length) {
      control.recycle({ type: 'recycle' });
    }
  }, [state]);

  // 初始化处理，这里是获取用户等基本的信息
  React.useEffect(() => {
    (async () => {
      analytics.startTime('auto-report-get-userinfo');
      if (serviceManager.prerenderData) {
        const { user, isTaxOpen } = serviceManager.prerenderData;
        setEnvState({
          os: env?.os,
          user,
          isTaxOpen: isTaxOpen || false,
          isChain: user.isChain,
        });
      } else {
        const [user, isTaxOpen] = await Promise.all([
          serviceManager.getUserService(),
          queryTaxSwitch ? serviceManager.queryTaxSwitch() : false,
        ]);
        setEnvState({
          os: env?.os,
          user,
          isTaxOpen: isTaxOpen || false,
          isChain: user.isChain,
        });
      }
      await getSwitchStatus();
      const duration = analytics.endTime('auto-report-get-userinfo');
      logger.debug(`获取基础数据完毕，耗时：${duration}`);

      if (env?.os) {
        // 全局加上 os
        window.document.body.classList.add(env.os);
      }
    })();
  }, []);

  // 埋点
  React.useEffect(() => {
    const { __$log: log, __$pageStartTime: pageStartTime } = window as any;
    if (log && pageStartTime) {
      log.log(`【AutoReport2】页面开始加载时间 ----> ${Date.now() - Number(pageStartTime)}`);
    }
    if (env?.os === 'pc') {
      serviceManager.monitor = new MonitorReport({ module: 6001003 });
      serviceManager.monitor.start('PAGE_LOAD_TIME');
    }
    logger.debug('auto 页面开始加载');
    if (!preventPV) {
      analytics.pageView();
    }
    analytics.startTime('auto-report-start');
  }, []);

  const getSwitchStatus = React.useCallback(async() => {
    if (switchKeys || chainSwitchKeys) {
      const switchStatus = await renderSwitchStatus(switchKeys || [], chainSwitchKeys);
      switchCallBack?.(switchStatus);
    }
  }, []);

  if (_loading) {
    return <Loading />;
  }

  return (
    <DataContext.Provider value={_data}>
      <StateContext.Provider value={_state}>
        <StoreContext.Provider value={_store}>
          {component}
        </StoreContext.Provider>
      </StateContext.Provider>
    </DataContext.Provider>
  );
}

export default AutoReport;
