/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { find, isNil } from 'lodash';
import classNames from 'classnames';
import { Input, Select } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { getQuery } from './helper';
import { SelectorAndTextInputProps, SelectorAndTextInputValue } from './types';

import './pc.less';

const SelectorAndTextInput = (props: SelectorAndTextInputProps) => {
  const {
    className,
    options,
    placeholder,
    updateStatus,
    value,
    onChange,
  } = props;

  const [onSelected, onValueChange] = React.useMemo(() => [
    (v: string) => onChange?.({ ...value, selected: v }),
    (v: any) => onChange?.({ ...value, value: v.target.value }),
  ], [value, onChange]);

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return React.useMemo(() => {
    return (
      <Input.Group
        className={classNames('selector-and-text-input-group', className)}
        compact
      >
        <Select value={value?.selected} onChange={onSelected}>
          {options?.map(k => (
            <Select.Option key={k.value} value={k.value}>
              {k.title}
            </Select.Option>
          ))}
        </Select>
        <Input
          value={value?.value}
          placeholder={
            options.find(v => v.value === value?.selected)?.placeholder
            || placeholder
          }
          onChange={onValueChange}
        />
      </Input.Group>
    );
  }, [value, onSelected, onValueChange]);
};

SelectorAndTextInput.prototype.getQuery = getQuery;

SelectorAndTextInput.prototype.getDisplayValue = (
  value: SelectorAndTextInputValue,
  props?: SelectorAndTextInputProps,
) => {
  const selectedLabel = find(props?.options, o => o.value === value.selected)?.title || '';
  if (!isNil(value.value)) {
    return `${selectedLabel}：${value.value}`;
  }
  return undefined;
};

export default withAdapter(SelectorAndTextInput, {
  name: 'SelectorAndTextInput',
});
