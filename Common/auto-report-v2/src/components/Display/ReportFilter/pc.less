@auto-v2-theme-color: #FFD100;
@auto-v2-link-color: #FF6000;

:root .auto-report-page .auto2-query-manage-tags {
  .ant-tag {
    background: #F7F7F7;
    border: 1px solid #E5E5E5;
    border-radius: 4px;
    font-size: 12px;
    color: #222333;
    line-height: 24px;
    padding: 0 16px;
    cursor: pointer;

    &.active {
      background: #fff;
    }

    .anticon-close {
      font-size: 12px;
    }
  }
}

:root .auto-report-page .auto2-query-container {
  display: flex;
  flex-wrap: wrap;
  margin: 8px 0;

  .tree-selector {
    width: 100%;
    > .ant-select {
      display: block;
    }
  }

  .ant-select {
    display: block;
    flex: 1;
  }

  .ant-input-group-compact .ant-select {
    display: inline-block;
  }

  // tag 布局调整
  .ant-tags {
    display: flex;
    padding-top: 3px;

    .ant-btn {
      height: 24px;
      line-height: 16px;
      padding: 0 5px;
      flex: 1;
      &.isSelected {
        color: @auto-v2-link-color !important;
        border-color: @auto-v2-link-color !important;
      }
    }
  }
  
  &.expand .auto2-query-item.hide {
    display: flex;
  }

  .auto2-query-item {
    display: flex;
    align-items: center;
    height: 37px;

    &.first {
      flex-grow: 0;
      flex-shrink: 0;
    }

    &.more {
      display: flex;
    }

    &.hide {
      display: none;
    }
  }

  .auto2-query-item-label {
    margin-right: 8px;
    white-space: nowrap;
  }

  .auto2-query-item-control {
    display: flex;
    position: relative;
    padding-right: 16px;
    zoom: 1;
    flex: 1;
  }
  .auto2-query-toggle {
    margin-right: 0;
    padding-left: 0;
    padding-right: 10px;
    color: @auto-v2-link-color;
    font-weight: 500;
    span {
      margin: 0;
    }
  }

  // 以下是样式重设
  .auto2-query-item {
    .ant-input-number {
      line-height: 24px;
      height: 24px;
      width: auto;
    }

    .ant-input,
    .ant-input-number-input {
      height: 24px;
    }

    // 时间选择器布局调整
    .ant-RangePickerX,
    .datepicker {
      line-height: 16px;
      height: 24px;

      .ant-calendar-picker-input {
        height: 16px;
        line-height: 16px;
        padding: 0 4px;
      }
    }

    .ant-select-selection {
      min-height: 24px;
      height: 24px;
    }

    .ant-select-selector,.saas-select-selector {
      min-height: 24px;
      height: 24px;
      flex-wrap: nowrap;
      .ant-select-selection-item,.saas-select-selection-item {
        height: 18px;
        line-height: 16px;
        max-width: 160px;
      }
    }
    &.more {
      .ant-select-selector,.saas-select-selector {
        max-width: 100% !important;
        .ant-select-selection-search {
          width: calc(100% - 20px) !important;
        }
      }
    }

    .ant-select-selection--multiple .ant-select-arrow,
    .ant-select-selection--multiple .ant-select-selection__clear {
      top: 14px;
    }

    .ant-select-selection--multiple .ant-select-selection__rendered > ul > li,
    .ant-select-selection--multiple > ul > li {
      height: 18px;
      line-height: 16px;
    }

    .ant-select-selection__rendered {
      line-height: 24px;
    }

    .ant-select-search__field__placeholder,
    .ant-select-selection__placeholder {
      line-height: 20px;
    }

    // buttons 布局
    .ant-btn {
      height: 24px;
    }

    .ant-select-selection--multiple .ant-select-selection__choice__content {
      max-width: 160px;
    }
  }

  .auto2-query-item.action {
    .ant-btn {
      height: 24px;
      margin-right: 8px;
      padding: 2.4px 7px;
      font-size: 12px;
      &.auto2-query-toggle {
        padding-left: 0;
        margin-right: 0;
        padding-right: 8px;
      }
    }
  }
}
