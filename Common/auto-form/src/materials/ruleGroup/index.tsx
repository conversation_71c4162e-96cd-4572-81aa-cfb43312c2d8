/**
 * 条件组组件，可内嵌 material 或普通组件
 */
 import * as React from 'react';
 import { message } from 'antd';
 import type { ValueControl } from '../types';
 import type { BaseMaterials } from '..';
 import { renderMaterialForRuleGroup } from './material';
 
 type ChildNode = JSX.Element | BaseMaterials;
 
 export type RuleGroupType<ValueType, ChilNodeProps> = ValueControl<ValueType[]> & {
   min?: number;
   max?: number;
   defaultValue?: ValueType[];
   /** 新增数据时默认回填的值 */
   emptyValue: ValueType;
   value?: ValueType[];
   onChange?: (value: ValueType[]) => void;
   childNode: ChildNode;
   childNodeProps?: ChilNodeProps;
   renderAdd: (dom: JSX.Element, onClick: () => void) => React.ReactNode;
   renderDelete: (dom: JSX.Element, index: number, onClick: () => void) => React.ReactNode;
 };
 
 const getDom = (element: ChildNode) => {
   let tempDom: JSX.Element = <></>;
   if (React.isValidElement(element)) {
     tempDom = element;
   } else if (typeof element === 'object' && !React.isValidElement(element)) {
     tempDom = renderMaterialForRuleGroup(element as BaseMaterials);
   }
   return tempDom;
 };
 
 export class RuleGroup<V, P> extends React.Component<RuleGroupType<V, P>> {
   constructor(props: RuleGroupType<V, P>) {
     super(props);
   }
 
   get list() {
     return this.props.value || this.props.defaultValue || []
   }
 
   domItem = getDom(this.props.childNode);
 
   onChangeValue = (list: V[]) => {
     this.props.onChange?.(list);
   };
 
   onItemChange = (v: V, key: number) => {
     const list = this.list.slice();
     list[key] = v;
     this.onChangeValue(list);
   };
 
   onItemDelete = (key: number) => {
     const list = this.list.slice();
     const { min } = this.props;
      if (min && list.length <= min) {
       message.error('不可删除');
     } else {
       list.splice(key, 1);
       this.onChangeValue(list);
     }
   };
 
   onItemAdd = () => {
     const list = this.list.slice();
      const { emptyValue } = this.props;
     const _list = [...list, emptyValue];
     this.onChangeValue(_list);
   };
 
   render() {
     const list  = this.list;
     const { min = 0, max, renderDelete, renderAdd, childNodeProps } = this.props;
     const domItem = this.domItem;
     const doms = (
       <>
         {list.map((value, index) => {
           const dom = React.cloneElement(domItem, {
             ...childNodeProps,
             value,
             key: index,
             itemKey: index,
             onChange: (v: V) => {
               this.onItemChange(v, index);
             },
           });
           return (
             <div>
               {list.length >= min
                 ? renderDelete(dom, index, () => {
                     this.onItemDelete(index);
                   })
                 : null}
             </div>
           );
         })}
       </>
     );
     return <div>{!max || list.length < max ? renderAdd(doms, this.onItemAdd) : doms}</div>;
   }
 }