:root .auto-report-page-table {
  .ant-tablex {
    .ant-table-bordered .ant-table-content table {
      border-top: none;
    }

    .ant-table-has-fix-left {
      .ant-table-fixed-left {
        table {
          width: auto !important;
          min-width: 70px;
        }
      }
    }

    thead tr:first-child th {
      border-top: 1px solid #e8e8e8;
    }

    .ant-table-cell, .column-cell-inner {
      white-space: normal;
    }

    .ant-table-sticky-scroll {
      opacity: 1;
    }

    .ant-table-thead, .ant-table-summary, .ant-table-sticky-scroll {
      transition: none;
    }

    .ant-table-row-expand-icon {
      padding: 0;

      &::after {
        position: initial;
        display: inline-block;
      }
    }

    .ant-table-cell-with-append .column-cell-inner {
      display: inline-block;
    }

    .index-number {
      text-align: center;
    }

    .ant-table-row-expand-icon-spaced {
      display: none;
    }

    .index-number-cell-inner, .ant-table-fixed-right {
      .ant-table-row-expand-icon, .ant-table-row-indent {
        display: none;
      } 
    }

    .index-number-cell-inner {
      display: inline-block;
    }
  }

  &-alert {
    margin: 8px 0;
  }

  .ant-table-small.ant-table-bordered {
    border: 0;
  }

  .ant-table-small.ant-table-bordered,
  .ant-table-small.ant-table-bordered .ant-table-content {
    border-right: 0;
  }

  .ant-table-bordered .ant-table-placeholder {
    border-right: 1px solid #e8e8e8;
    border-left: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-table-bordered {
    .ant-table-tbody tr > td,
    .ant-table-summary tr > td,
    .ant-table-thead tr > th {
      padding: 8px;

      &:last-child {
        border-right: 1px solid #e8e8e8;
      }

      &.ant-table-fixed-columns-in-body {
        border-right: 1px solid transparent;
      }
    }
  }

  .ant-table-small.ant-table-bordered .ant-table-thead > tr > th:last-child {
    border-right: 1px solid #e8e8e8;
  }

  .ant-table-summary {
    background-color: #fafafa;
    font-weight: 500;

    tr > td {
      border-bottom: 1px solid #e8e8e8;
    }

    tr > td.ant-table-cell {
      border-right: 1px solid #e8e8e8;
    }
  }

  .ant-table-thead tr > th.ant-table-column-has-sorters {
    cursor: pointer;

    &:hover {
      background: #f2f2f2;
    }
  }

  .ant-table-thead tr > th .ant-table-column-sorters {
    position: relative;
    display: inline-block;
    padding-right: 24px;

    .ant-table-column-sorter {
      position: absolute;
      display: block;
      height: 21px;
      right: 0;
      top: 50%;
      transform: translate(-50%, -50%);

      .ant-table-column-sorter-inner {
        height: 14px;
        line-height: 14px;
        margin-top: 0;
        margin-left: 0;

        span {
          &.active {
            color: #ffbd00;
          }
        }
      }
    }
  }
  .ant-table-thead tr > th .ant-table-filter-column {
    position: relative;
    display: inline-block;
    padding-right: 24px;
    display: flex;
    justify-content: space-between;

    .ant-table-filter-trigger-container {
      position: absolute;
      display: block;
      height: 21px;
      right: -24px;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 28px;
      height: 40px;

      span {
        &.active svg {
          color: #ffbd00;
        }
      }

      .ant-dropdown-trigger {
        height: 21px;
      }

      .ant-dropdown-open{
        background: #e5e5e5;
        display: inline-block;
        width: 28px;
        height: 39px;
        svg{
          color: #666666;
        }
      }
     
      .anticon-filter:hover, .anticon-filter:link{
        background: #e5e5e5;
        svg{
          color: #666666;
        }
      }
      // 跳转按钮的位置以为显示颜色
      .ant-table-filter-dropdown-btns{
        display: flex;
        flex-flow: row-reverse;
        justify-content: space-between;
        button{
          border: 0;
          background: transparent;
          color: #fe8c00;
          padding: 0;
        }
        button:hover{
          color: #ffbd00;
        }
        .ant-btn-primary{
          letter-spacing: -1.5px;
        }
      }
    }
  }
}
