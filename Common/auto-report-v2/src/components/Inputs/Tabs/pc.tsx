import React from 'react';
import { Menu } from '@mtfe/sjst-antdx-next';
import withAdapter from '../../../core/withAdapter';
import { TabsProps } from './types';

type ItemType = {
  name: string;
  value: string | number;
  disabled?: boolean;
};

const Tabs = (props: TabsProps) => {
  const { options, updateStatus, value, onChange } = props;

  const items: ItemType[] = React.useMemo(() => {
    if (Array.isArray(options)) {
      return options.map((tab, i) => {
        if (typeof tab === 'string') {
          return { name: tab, value: i };
        }
        return { name: tab.title, value: i, disabled: tab.disabled };
      });
    }
    return Object.keys(options).map((key) => ({
      name: options[key],
      value: key,
    }));
  }, [options]);

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return (
    <Menu
      mode="horizontal"
      onSelect={React.useCallback(
        (x) => {
          const item = items.find((v) => `${v.value}` === x.key);
          if (item) {
            onChange?.(item.value);
          }
        },
        [items, onChange]
      )}
      selectedKeys={[`${value}`]}
    >
      {items.map((v) => (
        <Menu.Item key={v.value}>{v.name}</Menu.Item>
      ))}
    </Menu>
  );
};

export default withAdapter(Tabs, { name: 'Tabs' });
