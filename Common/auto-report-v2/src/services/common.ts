import { getService } from '@mtfe/next-biz/src/services/user';
import SandboxService from '@mtfe/next-biz/es/services/sandbox';
import Net from '@mtfe/next-biz/es/lib/Net';
import { isPC } from '@mtfe/next-biz/es/lib/customDimension/util';
import { buildPostApiCustom } from './buildApi';
import { isNil, isString } from 'lodash';

export interface AbMap {
  [key: string]: boolean;
}

export interface whiteListType {
  configKey: string;
  whiteSingleMerchantNos: string;
  numType: number; // 1: 商户编号->merchantNo  2：租户id->tenantId
}
/** 获取emis上的配置的白名单 */
// const protocol = process.env.AWP_BUILD_ENV === 'production' ? 'https://canyin-openapi.vip.sankuai.com' : '';
export const getEmisWhiteList = buildPostApiCustom<{configKeys: string[]}, whiteListType[]>(`/api/v1/support/operation-configs/query/pc`);
// 门店视角下判断用户是否在emis白名单中
export const poiUserIsEmisWhite = buildPostApiCustom<{configKeys: string[]}, IV>(`/api/v1/supportoperation-configs/query-pos`);

/** 批量获取是否在emis上的配置的白名单 **/
const emisWhiteListCaches = new Map<string, boolean>();
export const batchQueryEmisWhiteList = async (emisKeys: string[]): Promise<AbMap> => {
  let cachesData: AbMap = {};
  const othersKeys: string[] = [];
  emisKeys?.forEach(item => {
    const cache = emisWhiteListCaches.get(item);
    if (!isNil(cache)) {
      cachesData[item] = cache;
    } else {
      othersKeys.push(item);
    }
  })
  const user = await getService();
  const isChain = user.isHeadOffice();
  const merchantNo = user.org.merchantNo;
  const tenantId = String(user.org.tenantId);
  const queryParams = {
    configKeys: othersKeys,
  };
  const emisAbMap: AbMap = {};
  try {
    if (othersKeys?.length) {
      if (isChain) {
        const whiteList = await getEmisWhiteList(queryParams);
        othersKeys.forEach(item => {
          const itemConfig = whiteList.find(i => i.configKey === item);
          const numType = itemConfig?.numType || 1;
          const configValue = numType === 1 ? merchantNo : tenantId;
          const inEmis = !!itemConfig?.whiteSingleMerchantNos?.includes(configValue);
          emisAbMap[item] = inEmis;
        });
      } else {
        const whiteData = await poiUserIsEmisWhite(queryParams);
        othersKeys.forEach(item => {
          emisAbMap[item] = whiteData?.[item] === '1';
        })
      }
      Object.keys(emisAbMap).forEach(item => {
        emisWhiteListCaches.set(item, emisAbMap[item]);
      })
    }
    return {
      ...cachesData,
      ...emisAbMap,
    };
  } catch (error) {
    return {
      ...cachesData,
      ...emisAbMap,
    };
  }
};
export interface ConfigParams {
  /**
   * emis对应配置的key, 可参考https://km.sankuai.com/collabpage/1407847174
   */
  emisKey: string;
  /** emis白名单的配置维度 */
  configKey?: 'tenantId' | 'merchantNo' // 不用传该值了
}
/** 是否在emis上的配置的白名单中（通用 */
export const inEmisWhiteList = async (config: ConfigParams): Promise<boolean> => {
  const { emisKey } = config;
  const emisResult = await batchQueryEmisWhiteList([emisKey]);

  return !!emisResult[emisKey];
  
};

// 是否在取消默认查询的白名单中
export const chainCancelDefaultQuery = async (): Promise<boolean> => {
  return await inEmisWhiteList({emisKey: 'chain_pc_report_cancel_default_query'});
}

// 是否在abTest中
type SandboxAbType<T> = T extends Array<string> ? AbMap : boolean;
const sandboxAbCaches = new Map<string, boolean | undefined>();
export const isInSandboxAb = async <T = (string | Array<string>)>(abKey: T, defaultVal?: boolean): Promise<SandboxAbType<T>> => {
  const abKeyIsString = isString(abKey);
  const abKeyList = abKeyIsString ? [abKey] : abKey;
  let cachesData: AbMap = {};
  const othersKeys: string[] = [];
  // @ts-ignore
  (abKeyList as Array<string>)?.forEach(item => {
    const cache = sandboxAbCaches.get(item);
    if (!isNil(cache)) {
      cachesData[item] = cache;
    } else {
      othersKeys.push(item);
    }
  })
  const otherAbMap: AbMap = {};
  if (othersKeys?.length) {
    const userService = await getService();
    const testKeys = othersKeys.map(testKey => ({cookieName: '', path: '', testKey}));
    const sandbox = new SandboxService({
      testKeys,
      contextMap: {
        tenantId: String(userService?.org?.tenantId),
      },
    });
    const res = await sandbox.fetchAbService(true);
    othersKeys.forEach(key => {
      const abConfig = res?.[key];
      if (abConfig?.code === 0 && abConfig?.testkeyNotFound !== true) {
        otherAbMap[key] = !!abConfig?.strategy;
        sandboxAbCaches.set(key, !!abConfig?.strategy);
      } else {
        otherAbMap[key] = !isNil(defaultVal) ? defaultVal : false;
      }
    })
  }

  const abDataMap = {
    ...cachesData,
    ...otherAbMap,
  }
  if (abKeyIsString) {
    // @ts-ignore
    return abDataMap[abKey] as SandboxAbType<T>;
  }
  return abDataMap as SandboxAbType<T>;
}

// 是否在批量查询结算时间的ab中
export const isInBatchGetClearingTimeAb = async () => {
  const abKey = 'tll_batch_get_clearing_times';
  return await isInSandboxAb(abKey);
}

// 是否限制近两年查询的名单中
export const isInLimitTimeListAb = async () => {
  return await isInSandboxAb('ab_report_limit_time_list');
}

// 是否在袁记接口错误文案统一的名单中
export const isInYJResetApiErrorAbAndChain = async () => {
  const user = await getService();
  const isYJ = await isInSandboxAb('ab_report_yj_reset_api_err_message');
  return user?.isHeadOffice() && isYJ;
}

export const getMenuReportPC = async () => {
  return await Net.get('/api/v1/permissions/menu/pc/tree', {
    timeout: 30000,
    params: {parentMenuCode: 2}
  });
}

export const pushOperationsLog = buildPostApiCustom<IV, IV>('/api/v1/operations/batch/log/operations');

// 报表免密登录场景记查看日志--批量上报操作日志
// 接口文档： https://km.sankuai.com/collabpage/**********
export const pushReportOperationsLog = () => {
  (async () => {
    try {
      const user = await getService();
      const internalStaff = user.account?.internalStaff; // 是否为emis登录
      // const defaultGrandfatherMenuName = '报表中心';
      // const defaultMenuName = window.location.href;
      if (isPC() && internalStaff) {
        let [menuName, grandfatherMenuName] = ['', ''];
        let menuList = (window as IV)._reportPCMenuList;
        if (!menuList) {
          const resp = await getMenuReportPC();
          menuList = resp.menuList || [];
          (window as IV)._reportPCMenuList = menuList;
        }
        const hash = location.hash?.split('?')?.[0];
        if (hash) {
          const menu = menuList.find((i: IV) => {
            const menuHash = `#${i.redirectUrl?.split('#')?.[1]?.split('?')?.[0] || ''}`;
            return menuHash === hash;
          });
          const grandfatherMenuCode = menu?.path?.split('/')?.[1];
          const grandfatherMenu = menuList.find((i: IV) => (grandfatherMenuCode && String(i.menuCode) === grandfatherMenuCode));
          menuName = menu?.name;
          grandfatherMenuName = grandfatherMenu?.name;
        }
        if (!!grandfatherMenuName && !!menuName) {
          const actionData = `查看了【${grandfatherMenuName}】-【${menuName}】`;
          const params = {
            operationTime: Date.now(),
            operatorInfoTO: {
              operatorType: 1,
            },
            simpleOperationTOList: [{
              logType: 2,
              operationModuleType: 101,
              operationType: 4,
              sensitive: 0,
              actionTOs: [
                {
                  actionType: 4,
                  actionData,
                  templateId: 3586,
                }
              ]
            }]
          }
          await pushOperationsLog(params);
        }
      }
    } catch (error) {
      console.log('上报失败', error?.message || '');
    }
  })();
}