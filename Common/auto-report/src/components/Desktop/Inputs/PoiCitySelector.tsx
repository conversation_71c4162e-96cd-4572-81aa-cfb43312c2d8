/**
 * 门店城市选择器。当前集团下的门店所在城市，不是通用的省市区选择。
 *
 */
import React from 'react';
import { City } from '@mtfe/next-biz/src/components/Org/Filter';
import { City as ICity } from '@mtfe/next-biz/src/components/Org/DataSource';
import {
  Filter,
  PoiCitySelectorFilter,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

function toArray<T>(d?: T | T[]): (undefined | T[]) {
  if (!d) return;
  if (Array.isArray(d)) return d;
  return [d];
}

export default class implements InputType {
  config: PoiCitySelectorFilter;

  constructor(filter: Filter, public context: Context) {
    this.config = filter as PoiCitySelectorFilter;
  }

  getMultiple() {
    if ('multiple' in this.config) {
      return this.config.multiple;
    } else {
      return true;
    }
  }

  render(props: FilterProps) {
    const { otherProps } = props;
    const multiple = this.getMultiple();
    return (
      <City
        size={props.size}
        multiple={!!multiple}
        {...otherProps}
      />
    );
  }

  getDisplayValue(cities?: ICity[] | ICity) {
    if (!cities) {
      return '全部';
    }
    if (Array.isArray(cities) && !cities.length) {
      return '全部';
    }
    return toArray(cities)?.map(o => o.name).join('，');
  }

  getValue(dicts: ICity | ICity[] | undefined) {
    return toArray(dicts)?.map(o => o.id);
  }
}
