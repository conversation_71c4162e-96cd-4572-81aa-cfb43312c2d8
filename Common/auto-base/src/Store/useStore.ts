import React from 'react';
import { storeContext } from './Provider';
import { Store, Getter } from './types';
import { deepEqual } from '../helper';

export function useStore<S = any, G = any, Selected = any>(
  selector?: Getter<S, G, Selected>,
  deps?: React.DependencyList,
  context?: React.Context<Store | null>
) {
  const store = React.useContext(context || storeContext) as Store<S, G>;
  const [selectedValue, setSelectedValue] = React.useState<Selected>();

  const update = React.useCallback(() => {
    if (selector) {
      setSelectedValue((oldSelectedValue) => {
        const newSelectedValue = selector(store);
        if (deepEqual(newSelectedValue, oldSelectedValue)) {
          return oldSelectedValue;
        }
        return newSelectedValue;
      });
    }
  }, deps || []);

  React.useEffect(() => {
    update();
    return store.subscribe(update);
  }, [update]);

  return {
    value: selectedValue || selector?.(store),
    store,
  };
}

export default useStore;