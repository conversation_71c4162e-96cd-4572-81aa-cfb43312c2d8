import React from 'react';
import {
  Select,
  Radio,
} from 'antd';
import { deepEqual } from '@mtfe/next-biz/src/utils/deepEqual';
import AsyncTreeSelect from '@mtfe/next-biz/es/components/TreeSelect/AsyncTreeSelect';
import { isVoid } from '@mtfe/next-biz/es/utils/types';
import {
  EnumFilter,
  Filter,
  InputType,
  FilterProps,
  Option,
} from '../../../types/filter';

export default class EnumSelector implements InputType {
  config: EnumFilter;

  options: Array<Option & { key: string }> = [];

  constructor(filter: Filter) {
    this.config = filter as EnumFilter;

    this.options = this.config.options.map(option => ({ ...option, key: [option.label, option.value].join('$%$') }));
  }

  getDefaultValue = () => {
    const { defaultValue, multiple } = this.config;
    if (!defaultValue) return;

    if (multiple && Array.isArray(defaultValue)) {
      return this.options.filter(o => defaultValue.includes(o.value)).map(o => o.key);
    } else {
      const option = this.options.find(o => deepEqual(o.value, defaultValue));
      if (!option) return;
      return option.key;
    }
  }

  render(props: FilterProps) {
    const { config } = this;
    switch (this.config.selectType) {
      case 'radio':
        const radioChildren = this.options.map(o => (
          <Radio.Button key={o.key} value={o.key}>{o.label}</Radio.Button>
        ));
        const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          props.otherProps.onChange(e.target.value);
        };
        return <Radio.Group size={props.size} {...props.otherProps} onChange={onChange}>{radioChildren}</Radio.Group>;
      case 'select':
      case 'treeSelect':
        const treeSelectOptions = {
          multiple: config.multiple,
          placeholder: config.placeholder,
        };
        const options = this.options.reduce((s: IV, v: Option & { key: string }) => {
          s[v.key] = v.label;
          return s;
        }, {});
        // 组件 bug，传 undefined 不会生效，所以替换为空数组
        const value = props.otherProps.value === undefined ? [] : props.otherProps.value;
        return <AsyncTreeSelect size={props.size} {...props.otherProps} {...treeSelectOptions} value={value} options={options} />;
      default:
        const selectorOptions = {
          mode: config.multiple ? 'tags' : 'default',
          placeholder: config.placeholder,
          allowClear: true,
          dropdownMatchSelectWidth: false,
        };
        const maxTagCount = 1;
        let valLen = 0;
        if (Array.isArray(props.otherProps.value)) {
          valLen = props.otherProps.value.length;
        } else if (!isVoid(props.otherProps.value)) {
          valLen = 1;
        }
        const maxTagPlaceholder = `已选${valLen}个`;
        const maxCount = valLen > maxTagCount ? 0 : maxTagCount;
        const selectProps = {
          maxTagCount: maxCount,
          maxTagPlaceholder,
        };
        const children = this.options.map(o => (
          <Select.Option key={o.key} value={o.key}>{o.label}</Select.Option>
        ));

        return <Select size={props.size} {...props.otherProps} {...selectorOptions} {...selectProps}>{children}</Select>;
    }
  }

  getDisplayValue(value: Array<string | number> | string | number | undefined) {
    if (!Array.isArray(value)) {
      if (value !== undefined) {
        value = [value];
      } else {
        value = [];
      }
    }
    return value.map((v) => {
      const option = this.options.find(o => o.key === v);
      if (!option) return null;
      return option.label;
    }).filter(Boolean).join('、') || '全部';
  }

  getValue(value?: Array<string> | string) {
    if (!value) return;
    if (Array.isArray(value)) {
      return value.map((v) => {
        const option = this.options.find(o => o.key === v);
        if (!option) return null;
        return option.value;
      });
    } else {
      const option = this.options.find(o => o.key === value);
      if (!option) return null;
      return option.value;
    }
  }
}
