/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
} from '@antv/g2';
import { ColumnChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart, { Data } from './useBasicChart';
import { Color } from '../../../common/chartConst';

export default function ColumnChart(props: Props<ColumnChartProps>) {
  const {
    y,
    x,
    legend,
    width,
    height,
    y1,
  } = props;
  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    // 添加主线
    const column = chart
      .interval()
      .position(`${x.field}*${y.field}`)
      .adjust({
        type: 'dodge',
        marginRatio: 0.05,
      });

    if (legend) {
      chart.legend(legend.field, {
        position: 'bottom',
      });
      column.color(legend.field, Color);
    } else {
      column.color({
        fields: [x.field],
        values: Color,
      });
    }
    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    if (y1) {
      chart.line()
        .position(`${x.field}*${y1.field}`)
        .color(Color[0]);
      chart.axis(y1.field, {
        grid: null,
      });
      chart.scale({
        [y1.field]: {
          alias: y1.label,
        },
      });
    }

    return chart;
  }, [x, y, legend]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    chart.data(data);
  }, [x]);

  const dims = React.useMemo(() => [x.field, legend?.field], [x, legend]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
  });

  return dom;
}

registorComponent('ColumnChart', ColumnChart);
