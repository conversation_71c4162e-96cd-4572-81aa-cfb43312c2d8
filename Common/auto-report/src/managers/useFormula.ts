/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-new-func */
import React from 'react';
import { UserContext } from '@mtfe/next-biz/src/contexts/user';
import {
  ReportContext,
} from '../types';

export default function useFormula() {
  const report = React.useContext(ReportContext);
  const userService = React.useContext(UserContext);
  const query = report?.requestManager?.query;

  const runFormula = React.useCallback((formula?: string) => {
    let result: any;
    if (!formula) return;
    try {
      const fn = new Function('query', 'userService', `return ${formula}`);
      result = fn(query, userService);
    } catch (e) {
      console.error('表达式运行异常', formula, e);
      result = false;
    }
    return result;
  }, [query, userService]);

  return runFormula;
}
