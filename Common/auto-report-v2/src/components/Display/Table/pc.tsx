import { findIndex, findLastIndex, get, isNil } from 'lodash';
import React, { useMemo } from 'react';
import classNames from 'classnames';
import { Alert, TableY, ITableYProps, Empty } from '@mtfe/sjst-antdx-next';
import { IV } from '@mtfe/sjst-report/es/ReportCore/utils';
import { useStore } from '@mtfe/auto-base';
import { deepEqual } from '@mtfe/auto-base/helper';
import zhCN from 'antd/es/locale-provider/zh_CN';
import { dataContext } from '../../../contexts';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import serviceManager from '../../../core/ServiceManager';
import formatManager from '../../../core/FormatManager';
import PipelineManager from '../../../core/PipelineManager';
import { defaultFormat } from '../../../helper/format';
import Loading from '../../../components/Core/Loading';
import {
  ObjectRefType,
  RenderManagerType,
  ReportPage,
  ReportValueType,
} from '../../../types';
import { pipelineManager } from './pipelines';
import { recursiveColumns, columnSort } from './helper';
import { TableProps, TablePopelineValueType, TableColumnProps } from './types';
import { FullScreenValueType } from '../FullScreen/types';
import { isControlError } from '../../../../../auto-report/src/components/ControlTip/index';

import './pc.less';

export { pipelineManager } from './pipelines';

const TABLE_COLUMN_PLACEHOLDER_KEY = 'table-column-placeholder';

const equalDataIndex = (a: string | string[], b: string | string[]) => {
  const _a = Array.isArray(a) ? a.join('.') : a;
  const _b = Array.isArray(b) ? b.join('.') : b;
  return _a === _b;
};

// 解析 column
const processColumn = (renderManager?: RenderManagerType, currentSort?: IV) => (
  column: TableColumnProps<IV>,
  directlyTotal?: boolean,
  isTotal?: boolean
) => {
  // render 处理
  // eslint-disable-next-line
  const render = (_: any, record: any, index: number) => {
    const value = get(record, column.dataIndex || '');

    // eslint-disable-next-line
    const _format = (_value: any, _record: any, _index: number) => {
      const { format } = column;
      if (typeof format === 'object') {
        return renderManager?.render(format, {
          data: {
            value: _value,
            record: _record,
            index: _index,
          },
        });
      }
      if (typeof format === 'function') {
        return format ? format(_value, _record, index, isTotal) : _value;
      }
      if (isNil(_value) && typeof _value !== 'number') {
        return _value;
      }
      return format ? formatManager.run(format, _value) : _value;
    };

    const formatValue = defaultFormat(
      directlyTotal ? value : _format(value, record, index)
    );
    const props =
      typeof column.props === 'function'
        ? column.props?.(value, record, index)
        : column.props;

    const { className, style, ...otherProps } = props || {};

    return {
      props: otherProps,
      children: (
        <div
          className={classNames('column-cell-inner', className)}
          style={{
            ...style,
            maxWidth: column.width ? 'max-content' : undefined,
          }}
        >
          {formatValue}
        </div>
      ),
    };
  };

  if (column.sorter) {
    if (
      equalDataIndex(column.dataIndex as string, currentSort?.column?.dataIndex)
    ) {
      column.sortOrder = currentSort?.order;
    } else {
      column.sortOrder = null;
    }
  }

  // 浮动列必须要设置宽度，所以这里默认给 100
  if (column.fixed === 'left' || column.fixed === 'right') {
    column.resizeable =
      column.resizeable === undefined
        ? Boolean(column.width) || false
        : column.resizeable;
    column.width = column.width || 120;
  }

  return {
    showSorterTooltip: false,
    resizeable: true,
    ...column,
    render: column.customRender ? column.customRender : render,
  };
};

function _Table<ListRowData>(props: TableProps<ListRowData>) {
  const {
    directlyTotal,
    rowKey,
    showIndexNoColumn,
    defaultExpandAllRows,
    indexNoColumnProps,
    indexNoColumnFormat,
    expandIconColumnDataIndex,
    columns,
    pipelines,
    updateStatus,
    value,
    baseRefObject,
    onChange,
    forceUseCurColumns,
    filterDataSourceByColumnSetting,
    $$auto,
    sticky = true,
    ...otherProps
  } = props;
  const refObject = useRefObject(baseRefObject);

  // const tableRef = React.useRef<TableY<ListRowData>>();
  // eslint-disable-next-line
  const sortRef = React.useRef<any>();
  const rowKeyRef = React.useRef(
    rowKey
      ? (record: ListRowData, index: number) => {
          if (typeof rowKey === 'string') {
            return `${index}_${get(record, rowKey, '')}`;
          }
          if (Array.isArray(rowKey)) {
            return `${index}_${rowKey
              .map((v) => get(record, v, ''))
              .join('-')}`;
          }
          return `${index}_${rowKey(record)}`;
        }
      : undefined
  );

  const runKey = React.useRef<string | null>('');
  const hasUpdate = React.useRef(false);
  const initReport = React.useRef(true);

  const valueColumns = value?.columns;
  const reportRefObject =
    refObject?.version === 2
      ? useStore<any>((store) => {
          return store.getters.withParentName(refObject?.path, 'Report');
        }).value
      : ((refObject as ObjectRefType<any>)?.withParentName(
          'Report'
        ) as ObjectRefType<ReportValueType<ListRowData[]>>);

  // 全屏展示
  const fullScreenRefObject =
    refObject?.version === 2
      ? useStore<any>((store) => {
          return store.getters.withChildrenName(
            reportRefObject?.path || [],
            'FullScreen'
          )?.[0];
        }).value
      : ((reportRefObject as ObjectRefType<any>)?.withChildrenName(
          'FullScreen'
        )?.[0] as ObjectRefType<FullScreenValueType> | undefined);

  // 字段配置
  const columnSettingRefObject =
    refObject?.version === 2
      ? useStore<any>((store) => {
          return store.getters.withChildrenName(
            reportRefObject?.path || [],
            'ColumnSetting'
          )?.[0];
        }).value
      : ((reportRefObject as ObjectRefType<any>)?.withChildrenName(
          'ColumnSetting'
        )?.[0] as
          | ObjectRefType<Array<TableColumnProps<ListRowData>>>
          | undefined);

  const { value: reportValue, status: reportStatus } = reportRefObject || {};

  const { env = $$auto?.$global?.env } = React.useContext(dataContext);

  const [ updateCount, setUpdateCount ] = React.useState(0);
  const [state, setState] = React.useState<{
    dataSource: ListRowData[];
    // eslint-disable-next-line
    sumData?: any;
    reportPage: ReportPage;
  }>({
    dataSource: [],
    reportPage: {},
  });

  const [originalTableColumns, setOriginalTableColumns] = React.useState<
    Array<TableColumnProps<ListRowData>>
  >([]);

  const tableColumns = React.useMemo(() => {
    const _columns = recursiveColumns(
      originalTableColumns,
      processColumn(refObject?.renderManager, sortRef.current)
    );
    const indexColumns: TableColumnProps<ListRowData>[] = showIndexNoColumn
      ? [
          {
            key: 'index-number',
            className: 'index-number',
            title: '序号',
            fixed: 'left',
            width: 70,
            resizeable: false,
            align: 'center',
            render: (_, record: ListRowData, i: number) => ({
              props: indexNoColumnProps ? indexNoColumnProps(record, i) : {},
              children: indexNoColumnFormat ? (
                indexNoColumnFormat(record, i)
              ) : (
                <div className="index-number-cell-inner">{i + 1}</div>
              ),
            }),
          },
        ]
      : [];

    const autoIndex = findIndex(
      _columns,
      (v) => v.fixed !== 'left' && v.fixed !== 'right' && !v.hide && !v.width
    );
    if (autoIndex === -1) {
      const lastIndex = findLastIndex(
        _columns,
        (v) => v.fixed !== 'left' && v.fixed !== 'right' && !v.hide
      );
      if (lastIndex !== -1) {
        _columns[lastIndex].width = undefined;
      }
    }
    return _columns.length ? indexColumns.concat(_columns) : [];
  }, [showIndexNoColumn, indexNoColumnProps, originalTableColumns]);

  // 合计行
  const summary = React.useCallback(
    (_, Tr, Td, { fixed } = {}) => {
      if (!state.dataSource?.length || !state.sumData) {
        return null;
      }
      const summaryColumns: React.ReactNode[] = [];
      recursiveColumns(
        tableColumns,
        (column) => {
          if (!column.children?.length) {
            const { render } = processColumn(refObject?.renderManager)(
              column,
              directlyTotal,
              true
            );
            const v = get(state.sumData, column.dataIndex || []);
            const renderValue = render(v, state.sumData, -1);
            if (column.key === 'index-number') {
              if (fixed !== 'right') {
                summaryColumns.push(
                  <Td key={column.key}>
                    <div className="column-cell">合计</div>
                  </Td>
                );
              }
            } else {
              if (!fixed || column.fixed === fixed) {
                summaryColumns.push(
                  <Td
                    key={
                      column.dataIndex || Math.random().toString(36).slice(2)
                    }
                    className="ant-table-cell"
                    style={{ textAlign: column?.align }}
                  >
                    {column.key === TABLE_COLUMN_PLACEHOLDER_KEY
                      ? ''
                      // @ts-ignore
                      : renderValue?.children}
                  </Td>
                );
              }
            }
          }
          return column;
        },
        (column) => !column.hide
      );
      const key = `sum-data-${fixed || 'content'}`;
      return (
        <Tr key={key} rowKey={key}>
          {summaryColumns}
        </Tr>
      );
    },
    [directlyTotal, tableColumns, updateCount]
  );

  const pageChange = React.useCallback(
    (pagination, filters, sort) => {
      // 如果分页发生变化，则排序不变
      if (
        pagination.current &&
        state.reportPage.pageNo !== pagination.current
      ) {
        sort = sortRef.current;
      }

      // 如果 PageSize 发生变化，则排序不变
      if (
        pagination.pageSize &&
        state.reportPage.pageSize !== pagination.pageSize
      ) {
        sort = sortRef.current;
      }

      const sortQuery = sort.column?.dataIndex
        ? {
            orderBy: sort.column?.dataIndex,
            orderByType: sort.order === 'descend' ? 'desc' : 'asc',
          }
        : {};

      // 如果排序发生变化，重新从第一页开始
      if (sortRef.current && !deepEqual(sortRef.current, sort)) {
        pagination.current = 1;
      }
      sortRef.current = sort || {};
      refObject?.emit('submit@Report', {
        filters,
        query: {
          ...sortQuery,
          pageNo: pagination.current || state.reportPage.pageNo,
          pageSize: pagination.pageSize || state.reportPage.pageSize,
        },
      });
    },
    [state]
  );

  React.useEffect(() => {
    if (!rowKey) {
      refObject?.logger.error('Table 未设置 rowKey！');
    }
  }, [rowKey]);

  React.useEffect(() => {
    const duration = refObject?.analytics.endTime('auto-report-start', {
      clear: false,
    });
    refObject?.logger.debug(
      `auto 页面开始渲染表格，auto-report 耗时：${duration}`
    );
    updateStatus?.('done');
    return () => {
      runKey.current = null;
    }
  }, []);

  React.useEffect(() => {
    const newColumns = (columnSettingRefObject && !forceUseCurColumns)
      ? columnSettingRefObject.value || []
      : valueColumns || [];
    setOriginalTableColumns((preColumns) => {
      if (deepEqual(preColumns, newColumns)) {
        return preColumns;
      }
      return newColumns;
    });
  }, [columnSettingRefObject?.value, valueColumns, forceUseCurColumns]);

  React.useEffect(() => {
    (async () => {
      const key = Math.random().toString(36).slice(2);
      runKey.current = key;
      let _columns: any[] = [];
      try {
        _columns = typeof columns === 'function'
          ? columns(reportValue || {}, env)
          : columns;
      } catch (error) {
      }

      if (!reportValue?.originalQuery?.orderBy) {
        sortRef.current = {};
      }

      // 管道用以处理原始数据
      const viewData = await (pipelineManager as PipelineManager<
        TablePopelineValueType<ListRowData> & ReportValueType<ListRowData[]>
      >).run(
        pipelines || [],
        {
          ...reportValue,
          columns: _columns,
        },
        { env }
      );
      if (runKey.current === key) {
        const newState = {
          dataSource: viewData.data || [],
          sumData: viewData.others?.sumData,
          reportPage: {
            total: viewData.others?.totalCount,
            pageNo: viewData.query?.pageNo,
            pageSize: viewData.query?.pageSize,
          },
        };
        setState((preState) => {
          if (deepEqual(preState, newState)) {
            return preState;
          }
          return newState;
        });
  
        const dataColumns = recursiveColumns(
          viewData.columns || [],
          (v) => ({ ...v, selectableInChildren: v.expandSelectable }),
          (v) => !v.hide
        );
        if (dataColumns.length) {
          updateStatus?.(undefined, {
            columns: dataColumns,
            data: viewData.data || [],
          });
        } else {
          // 有些表格所有的列都是others中返回的，导致接口报错后进不了上面的if
          updateStatus?.(undefined, {
            // 默认给一个占位的，不然下面一直loading
            columns: [{
              dataIndex: 'default-data-index',
              key: 'default-data-index',
              title: '',
              width: 70,
              resizeable: false,
              align: 'center',
            }],
            data: [],
          });
        }
      }
    })();
  }, [columns, pipelines, reportValue]);

  React.useEffect(() => {
    hasUpdate.current = false;
    setTimeout(() => {
      if (!hasUpdate.current && runKey.current !== null) {
        setUpdateCount(p => p + 1);
      }
    }, 200);
  }, [state]);

  React.useEffect(() => {
    if (reportStatus !== 'pending') {
      if (initReport.current) {
        initReport.current = false;
      }
      const duration = refObject?.analytics.endTime('auto-report-start');
      refObject?.logger.debug(
        `auto 页面全部渲染完毕，auto-report 耗时：${duration}`
      );
    }
  }, [reportStatus]);

  const _dataSource = useMemo(() => {
    const { dataSource } = state;
    const list = tableColumns?.length ? dataSource : [];
    if (forceUseCurColumns && filterDataSourceByColumnSetting) {
      return filterDataSourceByColumnSetting(list, columnSettingRefObject?.value);
    }
    return list;
  }, [tableColumns, state?.dataSource, forceUseCurColumns, filterDataSourceByColumnSetting, columnSettingRefObject?.value]);

  const table = React.useMemo(() => {
    hasUpdate.current = true;
    const { reportPage } = state;
    const expandIconColumnIndex = expandIconColumnDataIndex
      // @ts-ignore
      ? tableColumns.filter(v => !!v?.enabled).findIndex(
          (v) =>
            v.dataIndex &&
            equalDataIndex(v.dataIndex as string, expandIconColumnDataIndex)
        ) + (showIndexNoColumn ? 1 : 0)
      : 0;

    if (initReport.current) {
      return <Loading />;
    }
    return (
      <div className="auto-report-page-table">
        <TableY
          // refTableY={(tableY) => (tableRef.current = tableY)}
          rowKey={rowKeyRef.current}
          size="small"
          scroll={{ x: 'max-content' }}
          loading={reportStatus === 'pending' || !tableColumns?.length}
          expandIconColumnIndex={expandIconColumnIndex}
          {...(otherProps as ITableYProps<ListRowData>)}
          getPopupContainer={(triggerNode) => {
            return triggerNode;
          }}
          defaultExpandAllRows={defaultExpandAllRows || false}
          locale={{...zhCN.Table, emptyText: !reportValue?.firstRequestFinished ? <Empty description="请选择查询条件并查询" image={Empty.PRESENTED_IMAGE_SIMPLE}/> : undefined}}
          bordered
          sticky={sticky}
          headerOffsetY={1}
          columns={
            sortRef.current?.field
              ? columnSort(tableColumns, sortRef.current)
              : tableColumns
          }
          dataSource={_dataSource || []}
          summary={summary}
          pagination={
            reportPage.total
              ? {
                  current: reportPage.pageNo,
                  pageSize: reportPage.pageSize,
                  total: reportPage.total,
                  pageSizeOptions: serviceManager.pageSizeOptions || [
                    '10',
                    '20',
                    '50',
                    '100',
                  ],
                }
              : false
          }
          onChange={pageChange}
        />
      </div>
    );
  }, [expandIconColumnDataIndex, reportStatus, tableColumns, updateCount, initReport?.current, reportValue?.firstRequestFinished, _dataSource]);

  return React.useMemo(() => {
    // 这个展示在前端重复了，并且由于牵涉改动的后端业务方比较多，所以先前端判断去掉
    if (isControlError(reportValue?.alert?.message)) {
      reportValue.alert = undefined;
    }

    return (
      <>
        {reportValue?.alert ? (
          <Alert
            className="auto-report-page-table-alert"
            message={reportValue.alert.message}
            type={reportValue.alert.type || 'warning'}
            showIcon
            closable
          />
        ) : null}
        {fullScreenRefObject?.value?.renderComponent
          ? fullScreenRefObject?.value?.renderComponent(table)
          : table}
      </>
    );
  }, [reportValue?.alert, fullScreenRefObject?.value, table]);
}

export default withAdapter(_Table, { name: 'Table' });
