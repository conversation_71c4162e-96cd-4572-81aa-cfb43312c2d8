import React from 'react';
// import { Alert } from '@mtfe/sjst-antdx-next';
import './Display';
import './Layout';
import './ReportFilter';
import './ReportHeader';
import './Widget';
import Inputs from './Inputs';
import Report from '../Report';
import { registorComponent, components } from './registor';
import {
  envsContext, componentsContext, inputSetContext, Envs,
} from '../../types/context';
import { Config } from '../../types/config';
import ReportComponent from '../ReportComponent';

registorComponent('Report', Report);

export default function DesktopComponentsPreset(props: {
  envs?: Envs,
  children: Config[] | Config | React.ReactNode | React.ReactNode[],
}) {
  const EnvsContext = envsContext;
  const InputSetContext = inputSetContext;
  const ComponentContext = componentsContext;
  return (
    <EnvsContext.Provider value={{ ...props.envs }}>
      <InputSetContext.Provider value={Inputs}>
        <ComponentContext.Provider value={components}>
          {/* {process.env.AWP_BUILD_ENV !== 'production' && <Alert message="AutoReport1页面" type="info" closable className="report-test-env-alert-info"/>} */}
          <ReportComponent>{props.children}</ReportComponent>
        </ComponentContext.Provider>
      </InputSetContext.Provider>
    </EnvsContext.Provider>
  );
}
