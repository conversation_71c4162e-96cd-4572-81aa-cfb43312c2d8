const React = require('react');
const { default: useCell, defaultFormat } = require('./useCell');
const renderer = require('react-test-renderer');
const { default: format } = require('../../../../common/format');

describe('useCell', () => {
  describe('defaultFormat', () => {
    it('数据为空时，返回--', () => {
      expect(defaultFormat(null)).toBe('-');
      expect(defaultFormat(undefined)).toBe('-');
    });

    it('传入其他数据时返回原数据', () => {
      expect(defaultFormat(123)).toBe(123);
      expect(defaultFormat({})).toStrictEqual({});
      expect(defaultFormat([])).toStrictEqual([]);
    });
  });

  describe('useCell', () => {
    let getResult, column, cell, row;
    beforeEach((cb) => {
      const Component = () => {
        const cellRender = useCell().cellRender;
        getResult = () => {
          return cellRender(column || {})(cell, row || {});      
        };
        cb();
        return null;
      };
      renderer.act(() => {
        renderer.create(<Component />);
      })
    });

    afterEach(() => {
      column = null;
      getResult = null;
      cell = null;
      row = null;
    });

    describe('单元格渲染', () => {
      it('默认会按照 format 进行渲染 children', () => {
        column = { format: 'number' };
        cell = { data: 123456 };
        let result = getResult();
        expect(result.children).toBe(format(column.format, cell.data));

        column = { format: 'currency' };
        cell = { data: 121343578 };
        result = getResult();
        expect(result.children).toBe(format(column.format, cell.data));
      });

      it('数据会使用defaultFormat做格式化兜底', () => {
        cell = { data: null };
        const result = getResult();
        expect(result.children).toBe(defaultFormat());

        column = { format: () => null };
        cell = { data: 1234 };
        result = getResult();
        expect(result.children).toBe(defaultFormat());
      });

      it('可以设置rowSpan', () => {
        cell = { rowSpan: 12 };
        const result = getResult();
        expect(result.props.rowSpan === cell.rowSpan).toBeTruthy();
      });

      it('column设置alternate后，没有数据时将会从alternate中取数', () => {
        column = { alternate: 'key1' };
        cell = { data: 435 };
        row = { key1: { data: 2346 }};

        let result = getResult();
        expect(result.children).toBe(cell.data);
        cell = null;
        result = getResult();
        expect(result.children).toBe(row.key1.data);
      });
    });

    describe('设置宽度', () => {
      it('宽度应该额外增加 16px', () => {
        column = { width: 100 };
        const result = getResult();
        expect(result.props.width).toEqual(116);
      });

      it('应该增加内部div防止单元格被压缩', () => {
        column = { width: 100 };
        cell = { data: 345 };
        const result = getResult();
        const dom = renderer.create(result.children);
        expect(dom.root.type).toBe('div');
        expect(result.props.className.split(' ').includes('fixed-width')).toBeTruthy();
        expect(dom.root.props.className.split(' ').includes('td-inner')).toBeTruthy();
        expect(dom.root.props.style.maxWidth).toBe(column.width);
        expect(dom.root.props.children).toBe(cell.data);
      });
    });

    describe('加粗', () => {
      it('className 应该包含 bold', () => {
        cell = { bold: true };
        const result = getResult();
        expect(result.props.className.split(' ').includes('bold')).toBeTruthy();
      });
    });

    describe('合计行', () => {
      it('列上设置showTotal=false，就不显示合计数据', () => {
        column = { showTotal: false };
        cell = { data: 123 };
        row = { isTotalRow: true };
        const result = getResult();
        expect(result.children).toBe(defaultFormat());
      });
    });
  });
});