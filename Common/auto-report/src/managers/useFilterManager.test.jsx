const React = require('react');
const { UserContext } = require('@mtfe/next-biz/src/contexts/user');
const { renderHook, act } = require('@testing-library/react-hooks');
const { default: useFilterManager } = require('./useFilterManager');

class Input {
  constructor(options) { this.options = options; }

  getDefaultValue() {
    return this.options.defaultValue;
  }
}

describe('useFilterManager', () => {
  it('在有parentManager的情况下，isSubFilter 返回true', () => {
    const parent = {
      getQueryInfo() { return {}; },
      prepareQuery() { return {}; },
    };
    const { result } = renderHook(() => useFilterManager(parent));
    expect(result.current.isSubFilter).toBe(true);
  });

  it('originQuery 的值由 filter.defaultValue 和 updateOriginQuery 组成', async () => {
    const { result, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true));
    act(() => {
      result.current.updateFilters([{
        type: Input,
        field: 'a',
        defaultValue: 123,
      }, {
        type: Input,
        field: 'aa',
        defaultValue: 900,
      }]);
    });
    await waitForValueToChange(() => result.current.inited);
    act(() => {
      result.current.updateOriginQuery(d => ({
        ...d,
        c: 456,
        aa: 488,
      }));
    });

    expect(result.current.originQuery).toStrictEqual({
      a: 123,
      aa: 488,
      c: 456,
    });
  });

  it('queryInfo 的值能和 filters 一一对应上, 且可以通过 getDisplayValue 控制展示的文本', async () => {
    const { result, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true));
    class InputWithDisplay extends Input {
      getDisplayValue(v) {
        return v.toString(16);
      }
    }

    act(() => {
      result.current.updateFilters([{
        type: InputWithDisplay,
        field: 'a',
        label: '数字',
      }, {
        type: InputWithDisplay,
        field: 'aa',
      }]);
    });

    await waitForValueToChange(() => result.current.inited);
    act(() => {
      result.current.updateQuery({
        a: 123,
        aa: 900,
      });
    });

    expect(result.current.getQueryInfo()).toBe('数字：【7b】；384');
  });

  it('filter.validator 较验失败后会设置 errors', async () => {
    const { result, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true));
    act(() => {
      result.current.updateFilters([{
        type: Input,
        field: 'a',
        label: '数字',
        validator: v => (v > 10 ? '不能大于10' : null),
      }, {
        type: Input,
        field: 'aa',
      }]);
    });
    await waitForValueToChange(() => result.current.inited);
    act(() => {
      result.current.updateOriginQuery({
        a: 123,
        aa: 900,
      });
    });
    expect(result.current.errors.a).toBe('不能大于10');
  });

  it('filter.loginType 会影响组件在总部/门店下展示', async () => {
    let userService = {
      isHeadOffice: () => false,
    };
    const { result, rerender, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true), {
      wrapper: ({ children }) => <UserContext.Provider value={userService}>{children}</UserContext.Provider>,
    });
    act(() => {
      result.current.updateFilters([{
        type: Input,
        field: 'a',
        loginType: 'poi',
      }, {
        type: Input,
        field: 'aa',
        loginType: 'chain',
      }]);
    });
    await waitForValueToChange(() => result.current.inited);
    expect(result.current.filters.length).toBe(1);
    expect(result.current.filters[0].field).toBe('a');
    act(() => {
      userService = {
        isHeadOffice() { return true; },
      };
      rerender();
    });
    expect(result.current.filters.length).toBe(1);
    expect(result.current.filters[0].field).toBe('aa');
  });

  it('updateQueryBySerialized 会调用 input.deserialize 并更新 prepareQuery 的结果', async () => {
    class InputWithDeserialize extends Input {
      deserialize(v) {
        return Number(v);
      }
    }
    const { result, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true));
    act(() => {
      result.current.updateFilters([{
        type: InputWithDeserialize,
        field: 'a',
      }, {
        type: InputWithDeserialize,
        field: 'aa',
      }]);
    });
    await waitForValueToChange(() => result.current.inited);
    act(() => {
      result.current.updateQueryBySerialized({
        a: '123',
        aa: '900',
      });
    });
    expect(result.current.originQuery.a).toBe(123);
    expect(result.current.originQuery.aa).toBe(900);
  });

  it('prepareQuery 会调用 input.getValue 和 input.getQuery，返回值包括displayQuery', async () => {
    class InputWithGetValue extends Input {
      getValue(v) {
        return v * 10;
      }

      getQuery(v, query) {
        query[`${this.options.field}_`] = v;
        return query;
      }
    }
    const { result, waitForValueToChange } = renderHook(() => useFilterManager(undefined, true));
    act(() => {
      result.current.updateFilters([{
        type: InputWithGetValue,
        field: 'a',
      }, {
        type: InputWithGetValue,
        field: 'b',
      }, {
        type: Input,
        field: 'c',
        defaultValue: 1234,
      }]);
    });
    await waitForValueToChange(() => result.current.inited);
    act(() => {
      result.current.updateDisplayQuery({
        dd: 435,
        ff: 435,
      });
      result.current.updateQuery({
        a: 10,
        b: 20,
      });
    });

    expect(result.current.prepareQuery()).toStrictEqual({
      a_: 100,
      b_: 200,
      c: undefined,
      dd: 435,
      ff: 435,
      pageNo: 1,
      pageSize: 20,
      orderBy: undefined,
      orderByType: undefined,
      loginName: undefined,
      login: undefined,
    });
  });
});
