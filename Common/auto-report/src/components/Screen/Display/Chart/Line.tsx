/* eslint-disable react/no-children-prop */
import React from 'react';
import {
  Chart,
} from '@antv/g2';
import { LineChartProps, Props } from '../../../../types/config';
import { registorComponent } from '../../registor';
import useBasic<PERSON>hart, { Data } from './useBasicChart';
import { LineGradient, AreaGradient } from './const';
import Empty from './Empty';
import formatter from '../../../common/format';

export default function LineChart(props: Props<LineChartProps>) {
  const {
    y,
    x,
    y1,
    width,
    height,
  } = props;
  const lineColors = props.lineColors || LineGradient;
  const areaColors = props.areaColors || AreaGradient;

  /** 初始化图表 */
  const renderer = React.useCallback((chart: Chart) => {
    // 添加主线
    chart
      .line()
      .position(`${x.field}*${y.field}`)
      .color(lineColors[0]);

    chart
      .area()
      .position(`${x.field}*${y.field}`)
      .tooltip(false)
      .color(areaColors[0]);

    if (y1) {
      // 添加辅线
      chart
        .line()
        .position(`${x.field}*${y1.field}`)
        .color(lineColors[1]);
      chart
        .area()
        .position(`${x.field}*${y1.field}`)
        .tooltip(false)
        .color(areaColors[1]);

      chart.axis(y1.field, { grid: null });
    }

    chart.axis(y.field, {
      tickLine: { style: { fill: '#FFFFFF', fillOpacity: 0.35 } },
      label: { style: { fill: '#D8D8D8', fillOpacity: 1 } },
    });

    chart.axis(x.field, {
      tickLine: { style: { fill: '#FFFFFF', fillOpacity: 0.35 } },
      label: {
        style: { fill: '#A0A4AA', fillOpacity: 1 },
        autoHide: true,
        rotate: 0,
        autoRotate: false,
      },
    });

    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    });

    chart.removeInteraction('element-highlight');
    chart.scale({
      [x.field]: {
        formatter: v => formatter(x.format, v),
        alias: x.label,
        tickMethod: cfg => cfg.values,
      },
      [y.field]: {
        formatter: v => formatter(y.format, v),
        alias: y.label,
        nice: true,
      },
    });

    if (y1) {
      chart.scale(y1.field, {
        formatter: v => formatter(y1.format, v),
        alias: y1.label,
        nice: true,
      });
      chart.legend({
        custom: true,
        position: 'bottom',
        itemName: {
          style: {
            fill: '#D8D8D8',
          },
        },
        items: [{
          id: '1',
          value: y.field,
          name: y.label,
          marker: {
            symbol: 'circle',
            style: {
              fill: lineColors[0],
            },
          },
        }, {
          id: '2',
          value: y1.field,
          name: y1.label,
          marker: {
            symbol: 'circle',
            style: {
              fill: lineColors[1],
            },
          },
        }],
      });
    }
    chart.removeInteraction('legend-filter');
    chart.removeInteraction('legend-active');
    return chart;
  }, [x, y, y1]);

  const loadData = React.useCallback((chart: Chart, data: Data) => {
    chart.data(data);
  }, [x, y1]);

  const dims = React.useMemo(() => [x.field], [x]);

  const dom = useBasicChart({
    renderer,
    dims,
    width,
    height,
    loadData,
    empty: <Empty />,
  });

  return dom;
}

registorComponent('LineChart', LineChart);
