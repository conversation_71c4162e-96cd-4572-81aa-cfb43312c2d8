import React, { ReactNode } from 'react';
// import { Alert } from '@mtfe/sjst-antdx-next';
import { withAdapter } from '../../../core/withAdapter';
import { ComponentProps } from '../../../types';
import './pc.less';

interface Props extends ComponentProps{
  header: ReactNode;
  notice: ReactNode;
  tab: ReactNode;
  tooltip: ReactNode;
  filter: ReactNode;
  body: ReactNode;
}

const PageComponents: (keyof Props)[] = [
  'header',
  'tab',
  'notice',
  'tooltip',
  'filter',
  'body',
];

/**
 * 页面顶层容器
 * @param props
 * @returns
 */
function PageContainer(props: Props) {
  return (
    <div className="auto2-root-container">
      {/* {process.env.AWP_BUILD_ENV !== 'production' && <Alert message="AutoReport2页面" type="info" closable className="report-test-env-alert-info"/>} */}
      {PageComponents.map((name) => (
        <div key={name} className={`auto2-page-slot_${name}`}>
          {props.baseRefObject?.renderManager?.render?.(props[name])}
        </div>
      ))}
    </div>
  );
}

// @ts-ignore
PageContainer.auto = {
  childrenIsConfig: false,
};

export default withAdapter(PageContainer, {
  name: 'PageContainer',
  isComponentRender: false,
});
