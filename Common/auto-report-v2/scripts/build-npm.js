const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const root = path.join(__dirname, '../');
const srcPath = path.join(__dirname, '../src');
const esPath = path.join(__dirname, '../es');

function syncAssets(_path, filename) {
  const state = fs.statSync(_path);
  if (state.isDirectory()) {
    fs.readdirSync(_path).forEach(file => {
      syncAssets(path.join(_path, file), file);
    });
    return;
  }
  if (!filename) return;
  if (!filename.match(/\.(ts|tsx)$/)) {
    const relative = path.relative(srcPath, _path);
    const target = path.join(esPath, relative);
    const targetDir = path.dirname(target);
    try {
      execSync(`mkdir -p ${targetDir}`, {stdio: 'inherit'}); 
    } catch(e) {
      process.exit(1);
    }
    fs.copyFileSync(_path, target);
    console.log('Copy File', _path);
  }
}

function build () {
  try {
    console.log('清除 ES 文件夹');
    execSync('rm -rf es', {stdio: 'inherit'});
  } catch(e) {
    process.exit(1);
  }
  try {
    console.log('构建项目');
    execSync('npm run build:tsc', {stdio: 'inherit'});
  } catch(e) {
    // 忽略 tsc 报错
  }

  console.log('同步静态资源文件');
  syncAssets(srcPath);
  console.log('Done');
}

build();