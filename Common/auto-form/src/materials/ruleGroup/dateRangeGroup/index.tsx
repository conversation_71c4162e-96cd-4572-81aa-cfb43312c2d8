import * as React from 'react';
import { Button } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker/interface';
import { ValueControl } from '../../types';
import { RuleGroup, RuleGroupType } from '..';
import './index.less';

type MomentType = number | undefined;
type RangesType = Array<MomentType>;

export type DatePickerGroupMaterialProps = {
  name: 'date-range-picker-group';
  props?: Omit<RuleGroupType<RangesType, RangePickerProps>, 'renderAdd' | 'renderDelete' | 'childNode' | 'emptyValue'>;
};

type Props = DatePickerGroupMaterialProps & ValueControl<RangesType[]>;

export const DatePickerGroupMaterial = (p: Props) => {
  const { props, value, onChange } = p;
  const renderAdd = React.useCallback((dom, onClick: () => void) => {
    return (
      <div>
        {dom}
        <div className="add-btn">
          <a onClick={onClick}>添加一个日期段</a>
        </div>
      </div>
    );
  }, []);
  const renderDelete = React.useCallback((dom, _index, onClick) => {
    return (
      <div className="time-range-picker-rule-item" >
        {dom}
        <Button type="primary" shape="circle" icon="minus" size="small" className="delete-btn" onClick={onClick} />
      </div>
    );
  }, []);
  return (
    <RuleGroup
      emptyValue={[]}
      {...props}
      value={value}
      onChange={onChange}
      childNode={{ name: 'date-range-picker' }}
      renderAdd={renderAdd}
      renderDelete={renderDelete}
    />
  );
};
