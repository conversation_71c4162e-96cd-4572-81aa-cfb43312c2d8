/* eslint-disable */

declare module '@mtfe/react-prosemirror' {
  const HtmlEditor: any;
  const MenuBar: any;
  export {
    HtmlEditor,
    MenuBar,
  };
}
declare module '@cs/csp';
declare const BASE_PAGEMAP_URL: string;
declare const AWP_BUILD_ENV: string;
declare const SWIMLANE: string;

declare interface Window {
  RMS_GLOBAL: any;
  __nestExport: boolean;
  csp: { initChat: (p: IV) => void };
}

declare module '@mtfe/react-prosemirror-config-default' {
  const options: any;
  const menu: any;
  export {
    options,
    menu,
  };
}

declare module 'qrcodejs2' {
  class QRCode {
    constructor(dom: HTMLElement, url: string | {});
  }
  export default QRCode;
}

declare module 'rc-menu';
declare module 'css-animation';
declare module '@cs/csp';


declare module "@mtfe/next-biz/es/utils/json" {
  const parse: () => (data: any) => any;
  export default parse;
}

declare module "*.png";

type FunctionFirstArgType<T> = T extends (f: infer First, ...args: any[]) => any ? First : never;

/**
 * @deprecated
 */
interface IV {
  [id: string]: any;
}

type PromiseValue<T> = T extends Promise<infer U> ? U : unknown;
type PromiseReturnType<T extends (...args: any) => Promise<any>> = PromiseValue<ReturnType<T>>

interface StringMap {
  [index: string]: string;
}

interface NumberMap {
  [index: string]: number;
}

interface StringArrayMap {
  [index: string]: string[];
}

interface StringArrayNullMap {
  [index: string]: string[] | null;
}

interface NumberArrayMap {
  [index: string]: number[];
}

interface Window {
  Owl: IV;
  OwlForRequestRecord: IV;
  OwlForCustomPoint: IV;
  ifFst: boolean;
  handleFrozen?: (options: any, err: Error, code: number) => boolean;
  /**
   * FixCDNState 存储域名错误状态 FixCDNState = { [域名]: 错误数 }
   */
  FixCDNState?: {
    [domain: string] : number
  }
  /**
   * 不死鸟配置信息
   */
  PHOENIX_LIST?: {
    match: string[],
    replace: { host: string, try_count: number }[],
  }[]
}

declare module "gwm" {
  const creation: (ops: {
    txt: string,
    width: number,
    height: number,
    fontSize: number,
    color: string,
    alpha: number,
    angle: number,
    mode: string,
  }) => void;
}