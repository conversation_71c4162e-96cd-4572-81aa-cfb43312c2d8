import React from 'react';
import { Tabs } from '@mtfe/sjst-ui-next';
import withAdapter from '../../../core/withAdapter';
import { TabsProps } from './types';

const _Tabs = (props: TabsProps) => {
  const { options, updateStatus, value, onChange } = props;

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return (
    <Tabs tabs={options} value={value} onChange={onChange} average />
  );
};

export default withAdapter(_Tabs, { name: 'Tabs' });
