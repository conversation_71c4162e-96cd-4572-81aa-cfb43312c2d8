/* eslint-disable react/no-children-prop */
import React from 'react';
import type { Chart, Data } from '@antv/f2';
import { ReportContext } from '../../../../types';
import useF2 from './useF2';
import useParseData from '../../../common/useChartData';

type Props = {
  renderer: ((Chart: Chart) => void),
  width?: number | string,
  height?: number | string,
  dims: Array<string | undefined>,
  loadData?: (chart: Chart, data: Data) => void,
}
export default function useBasicChart(props: Props) {
  const F2 = useF2();
  const {
    dims,
    width,
    height,
    loadData,
    renderer,
  } = props;
  const report = React.useContext(ReportContext);
  const { requestManager } = report || {};
  const { rootNode } = requestManager || {};
  const { data } = useParseData(rootNode || null, dims);
  const [chart, updateChart] = React.useState<Chart | null>(null);

  const id = React.useMemo(() => `auto-report-line-chart-${Math.round(Math.random() * 100000000)}`, []);

  /** 初始化图表 */
  const initChart = React.useCallback(() => {
    if (!F2 || chart) return;
    const _chart = new F2.Chart({
      id,
      pixelRatio: window.devicePixelRatio,
    });
    renderer(_chart);
    updateChart(_chart);
  }, [F2, renderer, chart, updateChart, id]);

  React.useEffect(initChart, [F2, initChart]);

  React.useEffect(() => {
    if (chart && data) {
      if (loadData) {
        loadData(chart, data);
      } else {
        chart.source(data);
      }
      chart.render();
    } else if (chart) {
      chart.source([]);
      chart.render();
    }
  }, [chart, data, loadData]);
  return <canvas id={id} style={{ width, height }} />;
}
