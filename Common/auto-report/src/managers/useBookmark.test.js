let getAll = jest.fn(() => ([]));
let getBookmarks = jest.fn(() => ([]));
let addBookmark = jest.fn(() => true);
let removeBookmark = jest.fn(() => true);
let useCurrentLocation = jest.fn(() => ({}));
class ReportService {
  getAll = getAll;
  getBookmarks = getBookmarks;
  addBookmark = addBookmark;
  removeBookmark = removeBookmark;
}
jest.mock('@mtfe/next-router-v2', () => ({
  useCurrentLocation,
}));
jest.mock('@mtfe/next-biz/src/services/reports', () => ReportService);
const { default: useBookmark } = require('./useBookmark');
const { renderHook } = require('@testing-library/react-hooks');

describe('useBookmark', () => {
  afterEach(() => {
    getAll.mockClear();
    getBookmarks.mockClear();
    useCurrentLocation.mockClear();
  });

  it('初始化阶段会调用getALL和getBookmarks', async () => {
    const { waitForNextUpdate, result } = renderHook(() => useBookmark(false));
    await waitForNextUpdate();
    expect(getAll.mock.calls.length).toBe(1);
    expect(getBookmarks.mock.calls.length).toBe(1);
  });

  it('会根据getBookmarks和useCurrentLocation的值来判断当前页面是否为书签', async () => {
    getBookmarks.mockReturnValue([{
      url: '#/rms-report/a',
    }]);
    useCurrentLocation.mockReturnValue({
      pathname: '/rms-report/a',
    });
    const { result, waitForNextUpdate, rerender } = renderHook(() => useBookmark(false));
    expect(result.error).toBe(undefined);
    await waitForNextUpdate();
    expect(result.current.bookmarked).toBe(true);
    useCurrentLocation.mockReturnValue({
      pathname: '/rms-report/b',
    });
    rerender();
    expect(result.error).toBe(undefined);
    expect(result.current.bookmarked).toBe(false);
  })

  it('更新书签状态之后，会调用 addBookmark 或 removeBookmark', async () => {
    getAll.mockReturnValue([{
      url: '#/rms-report/a',
    }]);
    useCurrentLocation.mockReturnValue({
      pathname: '/rms-report/a',
    });
    const { result, waitForNextUpdate } = renderHook(() => useBookmark(false));
    await waitForNextUpdate();
    result.current.updateBookmarks(true);
    await waitForNextUpdate();
    expect(addBookmark.mock.calls.length).toBe(1);
    result.current.updateBookmarks(false);
    await waitForNextUpdate();
    expect(removeBookmark.mock.calls.length).toBe(1);
  });
});
