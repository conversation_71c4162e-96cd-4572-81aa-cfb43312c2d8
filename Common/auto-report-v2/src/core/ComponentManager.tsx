/* eslint-disable @typescript-eslint/no-explicit-any */
import { omit, set, get, flatten } from 'lodash';
import React from 'react';
import loglevel from 'loglevel';
import { componentManager as baseComponentManager } from '@mtfe/auto-base';
import { deepEqual, isBasicType, isReactNode } from '../helper/utils';
import {
  ChildrenComponent,
  ComponentMapType,
  ComponentProps,
  Config,
  ConfigProps,
  RenderManagerComponentType,
  RenderManagerType,
  RuleConfig,
} from '../types';

const logger = loglevel.getLogger('Auto-ComponentManager');

export function ComponentChild(props: {
  config: Config;
  renderManager: RenderManager;
  options?: ComponentProps;
}) {
  const { config, renderManager } = props;
  const { type, props: configProps } = config;
  const otherConfigProps = React.useMemo(
    () => omit(configProps, ['baseRefObject', 'children']),
    [configProps],
  );

  const [otherProps, setOtherProps] = React.useState<ConfigProps & RuleConfig>({});
  const [component, setComponent] = React.useState<React.ElementType>();

  // 这里每次都是函数 render 出来的所以每次 otherConfigProps 都是最新的，所以使用 state 来保存一下
  React.useEffect(() => {
    setOtherProps((preOtherProps) => {
      if (deepEqual(preOtherProps, otherConfigProps)) {
        return preOtherProps;
      }
      return otherConfigProps;
    });
  }, [otherConfigProps]);

  React.useEffect(() => {
    (async () => {
      if (!type) {
        logger.warn('组件 type 未设置！');
        return;
      }
      let _component: React.ElementType | string | undefined;
      if (['div', 'p', 'span', 'ul', 'li', 'ol'].indexOf(type) !== -1) {
        _component = type;
      }

      if (!_component) {
        _component = renderManager.getComponent(type);
      }

      if (!_component) {
        _component = await renderManager.getLazyComponent(type);
      }
      if (!_component) {
        logger.warn(`配置化报表未找到组件：${type}，组件配置：`, props);
        return;
      }

      if (typeof _component === 'object' && '$$typeof' in _component) {
        setComponent(_component);
      } else {
        setComponent(
          React.forwardRef((_props: any, _) => React.createElement(
            _component as string,
            _props,
            renderManager.render(_props.children || ''),
          )),
        );
      }
    })();
  }, [type, renderManager]);

  // 这里只根据 otherProps 的变动来跟新，props.options 变更不更新componentProps
  const componentProps = React.useMemo(
    () => ({ ...otherProps, ...(props.options || {}) }),
    [otherProps],
  );

  return React.useMemo(
    () => {
      if (!component) {
        return null;
      }
      const Component = component;
      return <Component {...componentProps}>{configProps?.children}</Component>;
    },
    [component, componentProps, configProps?.children],
  );
}

export type ComponentOptions = {
  name: string;
};
export class RenderManager implements RenderManagerType {
  private components: ComponentMapType;

  private lazyComponents: RenderManagerComponentType[];

  constructor(
    components?: ComponentMapType,
    lazyComponents?: RenderManagerComponentType[],
  ) {
    this.components = components || {};
    this.lazyComponents = lazyComponents || [];
  }

  getComponent(name: string) {
    const componentList = flatten([
      this.lazyComponents.filter(v => Boolean(v.components)).map(({ components }) => components),
      this.components
    ]);
    for (let i = 0; i < componentList.length; i++) {
      const components = componentList[i];
      const component = get(components, name);
      if (component) {
        return component;
      }
    }
    return undefined;
  }

  async getLazyComponent(name: string) {
    const componentList = await Promise.all(
      this.lazyComponents.filter(v => Boolean(v.lazy)).map(({ lazy }) => lazy?.().then(v => v.default)),
    );
    for (let i = 0; i < componentList.length; i++) {
      const components = componentList[i];
      const component = get(components, name);
      if (component) {
        return component;
      }
    }
    return undefined;
  }

  render(
    config?: ChildrenComponent | ChildrenComponent[],
    options?: ComponentProps,
    otherProps?: { key: string },
  ): React.ReactNode | React.ReactNode[] {
    if (!config) return null;

    if (Array.isArray(config)) {
      return (config as ChildrenComponent[]).map((c, i) => this.render(c, options, {
        key: otherProps?.key ? `${otherProps?.key}-${i}` : `${i}`,
      }));
    }

    if (isBasicType(config) || isReactNode(config)) {
      return config || null;
    }
    return (
      <ComponentChild
        key={otherProps?.key}
        config={config as Config}
        options={options}
        renderManager={this}
      />
    );
  }
}

export class ComponentManager {
  private components: ComponentMapType = {};

  getRenderManager(lazyComponents?: RenderManagerComponentType[]) {
    return new RenderManager(this.components, lazyComponents);
  }

  add(component: React.ComponentType, options?: ComponentOptions | string) {
    let name = component?.displayName;
    if (options) {
      if (typeof options === 'string') {
        name = options;
      } else if (options.name) {
        name = options.name;
      }
    }
    if (name) {
      set(this.components, name, component);

      // 兼容 auto-base
      // @ts-ignore
      const { type, version, component: sourceComponent } = component?.auto || {};
      if (type === 'autoAdapter' && version === 1) {
        baseComponentManager.registor(sourceComponent, { name });
      } else {
        baseComponentManager.registor(component, { name });
      }
    } else {
      throw new Error('Component not set name');
    }
  }
}

export const manager = new ComponentManager();
export default manager;
