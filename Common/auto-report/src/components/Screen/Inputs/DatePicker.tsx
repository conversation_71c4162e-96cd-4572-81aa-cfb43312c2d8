/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-fallthrough */
import {
  Filter,
  DateFilter,
  InputType,
} from '../../../types/filter';

type Value = {
  start: number,
  end: number,
  queryType?: any,
}

export default class implements InputType {
  config: DateFilter;

  constructor(filter: Filter) {
    this.config = filter as DateFilter;
  }

  getDefaultValue(): Value {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const date = now.getDate();
    let day = now.getDay();
    day = day === 0 ? 7 : day;
    switch (this.config.mode) {
      case 'year':
        return {
          start: new Date(year, 0, 1, 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
      case 'month':
        return {
          start: new Date(year, month, 1, 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
      case '30days':
        return {
          start: new Date(year, month, date - 30, 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
      case 'week':
        return {
          start: new Date(year, month, date - (day - 1), 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
      case '4weeks':
        return {
          start: new Date(year, month, date - (20 + day), 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
      case 'date':
      default:
        return {
          start: new Date(year, month, date, 0, 0, 0, 0).getTime(),
          end: new Date(year, month, date, 23, 59, 59, 999).getTime(),
        };
    }
  }

  render() {
    return null;
  }

  getValue() {
    return this.getDefaultValue();
  }

  getQuery(timeRange: Value) {
    timeRange = timeRange || this.getDefaultValue();
    if (!timeRange) return;
    const result = {
      startDate: timeRange.start,
      endDate: timeRange.end,
    } as any;
    return result;
  }
}
