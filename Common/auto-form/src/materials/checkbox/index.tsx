import React, { useContext } from 'react';
import { Checkbox } from 'antd';
import { CheckboxProps, CheckboxGroupProps } from 'antd/es/checkbox';
import { DataSource, ValueControl, DataSourceItem } from '../types';
import { DataContext } from '../../cores/dataContext';

export type CheckboxMaterialProps = {
  name: 'checkbox';
  props?: CheckboxGroupProps;
  itemProps?: CheckboxProps;
  dataSource: DataSource<CheckboxProps, CheckboxProps['value']> | string;
};

type Props = CheckboxMaterialProps & ValueControl<CheckboxGroupProps['value']>;

export const CheckboxMaterial = (p: Props) => {
  const { dataSource, props = {}, itemProps = {}, name, value, onChange, ...otherPorps } = p;
  const dataContext = useContext(DataContext);
  const options = typeof dataSource === 'string' ? dataContext.dataPool[dataSource] : dataSource;

  return (
    <Checkbox.Group {...otherPorps} {...props} value={p.value} onChange={p.onChange}>
      {options?.map?.((_: DataSourceItem<CheckboxProps, CheckboxProps['value']>) => {
        const { name: text, value: v, props: currentProps } = _;
        const newProps = { ...itemProps, ...currentProps };
        return (
          <Checkbox value={v} {...newProps} key={v}>
            {text}
          </Checkbox>
        );
      })}
    </Checkbox.Group>
  );
};
