import NP from 'number-precision';
import loglevel from 'loglevel';
import {
  Analytics as _Analytics,
  ILXAnalyticsOptions,
} from '@mtfe/next-biz/es/utils/analytics';
import { PerfPoint } from '@mtfe/next-biz/es/utils/addPoints';

const logger = loglevel.getLogger('Analytics');

export interface AnalyticsProps {
  cid?: string;
}

/**
 * 继承基本 Analytics，额外扩展一些其他的分析能力
 */
export default class Analytics {
  private cache: Map<string, number> = new Map();

  private analytics?: _Analytics;

  points = PerfPoint();

  constructor(options: AnalyticsProps) {
    this.analytics = options.cid
      ? new _Analytics({ cid: options.cid })
      : undefined;
  }

  startTime(key: string) {
    this.cache.set(key, Date.now());
  }

  endTime(key: string, options?: { startTime?: number; clear?: boolean }) {
    const duration = NP.minus(
      Date.now(),
      options?.startTime || this.cache.get(key) || Date.now(),
    );
    if (options?.clear !== false) {
      this.cache.delete(key);
    }
    return duration;
  }

  pageView() {
    logger.debug('pageView：', this.analytics?.getCid());
    setTimeout(() => {
      this.analytics?.pageView?.()?.();
    }, 1000);
  }

  pageDisappear(valLab: Object = {}, options: ILXAnalyticsOptions = {}) {
    logger.debug('pageDisappear', this.analytics?.getCid(), valLab, options);
    return this.analytics?.pageDisappear(valLab, options);
  }

  moduleView(
    mid?: string,
    valLab: Object = {},
    options: ILXAnalyticsOptions = {},
  ) {
    if (mid) {
      logger.debug(
        'moduleView',
        this.analytics?.getCid(),
        mid,
        valLab,
        options,
      );
      return this.analytics?.moduleView(mid, valLab, options);
    }
  }

  async moduleClick(
    bid?: string,
    valLab: Object = {},
    options: ILXAnalyticsOptions = {},
  ) {
    if (bid) {
      logger.debug(
        'moduleClick',
        this.analytics?.getCid(),
        bid,
        valLab,
        options,
      );
      return this.analytics?.moduleClick(bid, valLab, options);
    }
  }
}
