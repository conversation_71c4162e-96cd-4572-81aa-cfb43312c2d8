import React from 'react';
import { InputProps } from 'antd/es/input';
import { SelectProps } from 'antd/es/select';
import { CheckboxMaterial, CheckboxMaterialProps } from './checkbox';
import { InputMaterial, InputMaterialProps } from './input';
import { RadioMaterial, RadioMaterialProps } from './radio';
import { SelectMaterial, SelectMaterialProps } from './select';
import { SwitchMaterial, SwitchMaterialProps } from './switch';
import { CascaderMaterial, CascaderMaterialProps } from './cascader';
import { TimePickerMaterialProps, TimePickerMaterial } from './timepicker';
import { TimeRangePickerMaterial, TimeRangePickerMaterialProps } from './timepicker/range';
import { DatePickerMaterial, DatePickerMaterialProps } from './datepicker';
import { RangePickerMaterial, RangePickerMaterialProps } from './datepicker/range';
import { WeekPickerMaterial, WeekPickerMaterialProps } from './datepicker/week';
import { MonthPickerMaterial, MonthPickerMaterialProps } from './datepicker/month';
import { PoiTreeMaterialProps, PoiTreeMaterial } from './poiTree';
import { TimePickerGroupMaterialProps, TimePickerGroupMaterial } from './ruleGroup/timePickerGroup';
import { DatePickerGroupMaterialProps, DatePickerGroupMaterial } from './ruleGroup/dateRangeGroup'

export type BasicMaterialsProps = {
  input: InputProps;
  select: SelectProps;
};

export type BaseMaterials = | CheckboxMaterialProps
  | InputMaterialProps
  | RadioMaterialProps
  | SelectMaterialProps
  | SwitchMaterialProps
  | CascaderMaterialProps
  | TimePickerMaterialProps
  | TimeRangePickerMaterialProps
  | DatePickerMaterialProps
  | RangePickerMaterialProps
  | MonthPickerMaterialProps
  | WeekPickerMaterialProps
  | PoiTreeMaterialProps;

export type Materials = BaseMaterials | TimePickerGroupMaterialProps
  | DatePickerGroupMaterialProps;


export const renderBasicMaterial = (props: Materials) => {
  const materialMap = {
    checkbox: CheckboxMaterial,
    input: InputMaterial,
    radio: RadioMaterial,
    select: SelectMaterial,
    switch: SwitchMaterial,
    cascader: CascaderMaterial,
    'time-picker': TimePickerMaterial,
    'time-range-picker': TimeRangePickerMaterial,
    'date-picker': DatePickerMaterial,
    'date-range-picker': RangePickerMaterial,
    'week-picker': WeekPickerMaterial,
    'month-picker': MonthPickerMaterial,
    'poi-tree-selector': PoiTreeMaterial,
    'time-range-picker-group': TimePickerGroupMaterial,
    'date-range-picker-group': DatePickerGroupMaterial
  };
  // eslint-disable-next-line
  const Material: any = materialMap[props.name];
  return <Material {...props} />;
};
