/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Select,
} from 'antd';
import { isVoid } from '@mtfe/next-biz/src/utils/types';
import {
  GroupBySelectorFilter,
  Filter,
  Option,
  OptionSet,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';
import './GroupBySelector.less';

type Value = Array<number | string>;

type OnChangeAndValue = {
  value: Value,
  onChange: (v: Value) => void,
}

function getSelectedOption(value: Value, options: Option<string | number>[]) {
  let option = options.find(o => value.includes(o.value));
  // 销售员业绩统计中，按集团和自定义时间的 value 都是 null
  // 而 value 里不会保存 null
  // 不加下面的判断，导出和渲染时页面展示不出来统计方式
  if (!option) {
    option = options.find(o => o.value === null);
  }
  return option;
}

const SelectRender = (props: {
  value: Value,
  set: OptionSet<number | string>,
  index: number,
  size: 'small' | 'default' | 'large'
  onChange: (v: Value) => void,
  moduleClick: (bid?: string) => void,
}) => {
  const {
    onChange,
    value,
    moduleClick,
  } = props;
  const renderOption = React.useCallback((o: Option<number | string>) => <Select.Option value={o.value} key={o.value}>{o.label}</Select.Option>, []);

  const _onChange = React.useCallback((v: number | string | undefined) => {
    const newValue = value.slice();
    props.set.options.forEach((o) => {
      const _index = newValue.findIndex(_v => _v === o.value);
      if (_index >= 0) {
        newValue.splice(_index, 1);
      }
    });
    if (typeof v !== 'undefined') {
      newValue.push(v);
    }
    onChange(newValue);
    const option = props.set.options?.find(o => o.value === v);
    moduleClick && moduleClick(option?.bid);
  }, [value, onChange, props.set, moduleClick]);

  let dom: React.ReactNode = props.set.options.map(renderOption);

  if (props.set.label) {
    dom = (
      <Select.OptGroup label={props.set.label}>
        {dom}
      </Select.OptGroup>
    );
  }
  return (
    <Select size={props.size} value={getSelectedOption(value, props.set.options)?.value} dropdownMatchSelectWidth={false} onChange={_onChange}>{dom}</Select>
  );
};

function SelectorMatrix(props: OnChangeAndValue & { optionSet: Array<OptionSet>, size: 'small' | 'default' | 'large', moduleClick: (bid?: string) => void}) {
  const selectors = props.optionSet.map((set, index) => {
    if (set.hide) return null;
    return (
      <SelectRender
        size={props.size}
        key={index}
        set={set}
        index={index}
        onChange={props.onChange}
        value={props.value}
        moduleClick={props.moduleClick}
      />
    );
  });
  return <div className="auto-report-group-by-selector">{selectors}</div>;
}

export default class GroupBySelector implements InputType {
  config: GroupBySelectorFilter;

  optionSet: Array<OptionSet>;

  constructor(filter: Filter, context: Context) {
    this.config = filter as GroupBySelectorFilter;
    this.optionSet = this.config.optionSet.filter((set) => {
      if (set.loginType === 'poi' && context.userService?.isHeadOffice()) return false;
      if (set.loginType === 'chain' && !context.userService?.isHeadOffice()) return false;
      return true;
    });
  }

  getDefaultValue() {
    return this.optionSet.reduce((r: Value, set, index) => {
      if (!set) return r;
      const v = set.options[0] && set.options[0].value;
      if (typeof v !== 'undefined') {
        r.push(v);
      }
      return r;
    }, []);
  }

  render(props: FilterProps) {
    const { otherProps, moduleClick } = props;
    const { value } = otherProps;
    return <SelectorMatrix size={props.size} {...otherProps} optionSet={this.optionSet} value={value || this.getDefaultValue()} moduleClick={moduleClick} />;
  }

  getDisplayValue(value: Value) {
    return this.optionSet.map((set) => {
      if (set.hide) return false;
      const option = getSelectedOption(value, set.options);
      return option?.label;
    }).filter(Boolean).join('，');
  }

  getQuery(value: Value[], result: { groupBy?: Value[] }) {
    let groupBy = result?.groupBy || [];
    groupBy = groupBy.slice();
    value.forEach((v) => {
      if (!groupBy.includes(v) && !isVoid(v)) {
        groupBy.push(v);
      }
    });
    return {
      groupBy,
    };
  }
}
