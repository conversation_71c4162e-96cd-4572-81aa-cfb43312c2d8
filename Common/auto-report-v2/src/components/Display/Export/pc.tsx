/* eslint-disable @typescript-eslint/no-explicit-any */
import { cloneDeep, debounce } from 'lodash';
import React from 'react';
import {
  Button, Icon, Modal, message,
} from '@mtfe/sjst-antdx-next';
import { DownloadListIntro } from '@mtfe/next-biz/es/components/DownloadListIntro';
import { StateObject, useStore } from '@mtfe/auto-base';
import withAdapter, { useRefObject } from '../../../core/withAdapter';
import serviceManager from '../../../core/ServiceManager';
import { dataContext } from '../../../contexts';
import { autoExport, excelGetExport, excelPostExport } from './helper';
import { Config, ObjectRefType, ReportValueType } from '../../../types';
import { TableColumnProps, TableValueType } from '../Table/types';
import { exportCallback, ExportProps } from './types';
import { getService } from '@mtfe/next-biz/src/services/user';

export const exportAuthErrorMsg = '您没有当前页面导出权限，无法操作导出';

export const exportTaskAuthErrorMsg = () => {
  Modal.warning({
    title: '操作失败',
    content: (
      <div>
        <span>当前免密登录角色暂无导出/下载权限，申请权限请查看</span>
        <a target='_blank' href='https://km.sankuai.com/collabpage/1849901095#b-aa65141c21364c7c90600428131c2bd6'>餐饮SaaS-免密登录角色权限申请流程</a>
        <span>，或联系商家自行处理。</span>
      </div>
    ),
    okText: '知道了'
  });
}

const exportCallbackMap: { [name: string]: exportCallback } = {
  'auto-export': autoExport,
  'excel-get-export': excelGetExport,
  'excel-post-export': excelPostExport,
};

function downloadURI(uri: string, name: string = '') {
  const link = document.createElement('a');
  link.download = name;
  link.href = uri;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const Export = (props: ExportProps) => {
  const {
    baseRefObject,
    updateStatus,
    permissionCode,
    passWhileSome,
    bid,
    type,
    notTotalLimit,
    limitTotal = 20,
    beforeCallback,
    callback,
    options,
    $$auto,
    isJudgeDatalength = true
  } = props;

  const refObject = useRefObject(baseRefObject);

  const { env } = React.useContext(dataContext);

  const [loading, setLoading] = React.useState(false);

  const reportOriginObject
  = refObject?.version === 2
    ? useStore<any>((store) => {
        return store.getters.withParentName(refObject?.path, 'Report');
      }).value
    : ((refObject as ObjectRefType<any>)?.withParentName('Report'));

  React.useEffect(() => {
    if (!type) {
      console.error('Export 未设置 type！');
    }
  }, [type]);

  const onDownload = React.useCallback(debounce(async () => {
    if (!refObject) {
      return;
    }
    refObject.analytics.moduleClick(bid);

    let originalQuery: any = {};
    let query: any = {};
    let totalCount = 0;
    let title = '';
    let queryItems: any[] = [];
    let columns: any[] = [];
    let data: any;
    let reportValue: any;

    if (refObject?.version !== 2) {
      const reportRefObject = refObject?.withParentName('Report') as ObjectRefType<
      ReportValueType<any[]>
      >;
      const pageHeaderRefObject = reportRefObject?.withChildrenName(
        'PageHeader'
      )?.[0] as ObjectRefType<string> | undefined;
      const reportFilterRefObject = reportRefObject?.withChildrenName(
        'ReportFilter'
      )?.[0] as ObjectRefType<Config[]> | undefined;
      const columnSettingRefObject = reportRefObject?.withChildrenName(
        'ColumnSetting'
      )?.[0] as ObjectRefType<Array<TableColumnProps<any>>> | undefined;
      const tableRefObject = reportRefObject?.withChildrenName('Table')?.[0] as
        | ObjectRefType<TableValueType<any>>
        | undefined;
      
      reportValue = reportRefObject?.value;
      originalQuery = reportRefObject?.value?.originalQuery;
      query = reportRefObject?.value?.query;
      totalCount = reportRefObject?.value?.others?.totalCount || 0;
      title = pageHeaderRefObject?.value || '';
      queryItems = reportFilterRefObject?.value || [];
      columns = columnSettingRefObject?.value || tableRefObject?.value?.columns || [];
      data = tableRefObject?.value?.data;
    } else {
      const reportObject = $$auto?.withParentName('Report')as StateObject<any>;
      const pageHeaderObject = reportObject?.withChildrenName(
        'PageHeader'
      )?.[0];
      const reportFilterObject = reportObject?.withChildrenName(
        'ReportFilter'
      )?.[0];
      const columnSettingObject = reportObject?.withChildrenName<TableValueType<any>>(
        'ColumnSetting'
      )?.[0];
      const tableObject = reportObject?.withChildrenName('Table')?.[0];
      reportValue = reportObject?.$self?.value;
      originalQuery = reportObject?.$self?.value?.originalQuery;
      query = reportObject?.$self?.value?.query;
      totalCount = reportObject?.$self?.value?.others?.totalCount || 0;
      title = pageHeaderObject?.$self?.value || '';
      queryItems = reportFilterObject?.$self?.value || [];
      columns = columnSettingObject?.$self?.value || tableObject?.$self?.value?.columns || [];
      data = tableObject?.$self?.value?.data;
    }

    if (
      permissionCode
      && !(await serviceManager.checkhasPermission(permissionCode, passWhileSome))
    ) {
      Modal.confirm({
        title: '您的账号没有【导出】权限，请联系管理员分配权限再操作',
        okText: '我知道了',
        okCancel: false,
      });
    } else if (!data?.length && isJudgeDatalength) {
      Modal.confirm({
        title: '查询结果为空，没有可导出的数据',
        okText: '我知道了',
        okCancel: false,
      });
    } else if (!notTotalLimit && totalCount > limitTotal * 10000) {
      Modal.warning({
        title: '不支持导出',
        content: `数据超过${limitTotal}万条，不支持导出，建议缩短查询时间跨度再导出`,
        okText: '我知道了',
      });
    } else {
      setLoading(true);
      try {
        const _exportCallback = typeof type === 'string' ? exportCallbackMap[type] : type;

        const baseData = cloneDeep({
          user: env?.user,
          title,
          originalQuery,
          query,
          queryItems,
          columns,
          options,
          totalCount,
          reportValue,
          exportPermissionCode: permissionCode
        });

        const data = beforeCallback ? await beforeCallback(baseData, refObject) : baseData;
        if (!data) {
          setLoading(false);
          return;
        }

        const result = await _exportCallback(refObject, data);

        setLoading(false);
        if (result?.isNotAuth) {
          exportTaskAuthErrorMsg();
        }
        else if (result.link) {
          message.success('导出成功');
          downloadURI(result.link, result.reportName || '');
        } else if (result.totalCount === -1) {
          let maxTotal = limitTotal;
          if (notTotalLimit && result.resTotalCount) {
            maxTotal = 100;
          }
          Modal.warning({
            title: '不支持导出',
            content: `数据超过${maxTotal}万条，不支持导出，建议缩短查询时间跨度再导出`,
            okText: '我知道了',
          });
        } else if (!result.code) {
          Modal.confirm({
            title: (
              <div>
                {title}
                <br />
                本次导出数据共{result.totalCount || 0}
                条，请前往通用顶栏「下载清单」查看下载状态
              </div>
            ),
            okText: '前往下载清单',
            cancelText: '我知道了',
            onOk: () => {
              window.open(
                '/web/fe.rms-portal/rms-report.html#/rms-report/promiseDownload',
              );
            },
            onCancel: () => {
              const startDom = document.querySelector(
                '.ant-modal-confirm-btns .ant-btn',
              );
              const cancelBtnPos = startDom?.getBoundingClientRect();
              DownloadListIntro({ startDomRect: cancelBtnPos });
            },
          });
        }
        callback?.();
      } catch (e) {
        setLoading(false);
        callback?.(e);
        const _user = await getService();
        // _user?.account?.internalStaff && e?.message === exportAuthErrorMsg 为了兼容三端切换的表dpaas导出校验
        if (e?.code === 98015 || (_user?.account?.internalStaff && e?.message === exportAuthErrorMsg)) {
          exportTaskAuthErrorMsg();
        } else {
          message.error('导出失败');
        }
        refObject.logger.error(e);
      }
    }
  }, 200), [
    env,
    refObject,
    permissionCode,
    passWhileSome,
    bid,
    type,
    options,
    limitTotal,
    notTotalLimit,
  ]);

  const debounceDownload = React.useCallback(() => {
    if (!refObject) {
      return;
    }
    const { status: reportStatus } = reportOriginObject || {};
    if (reportStatus === 'pending') {
      return;
    }
    onDownload();
  }, [refObject, onDownload]);

  const { onClick, icon } = React.useMemo(
    () => (loading
      ? {}
      : {
        onClick: debounceDownload,
        icon: <Icon type="download" />,
      }),
    [loading, debounceDownload],
  );

  React.useEffect(() => {
    updateStatus?.('done');
  }, []);

  return React.useMemo(() => (
    <Button
      type="link"
      key="ColumnSetting"
      onClick={onClick}
      loading={loading}
    >
      {props.children || (
        <>
          {icon}
          <span>导出</span>
        </>
      )}
    </Button>
  ), [onClick, loading, props.children]);
};

export default withAdapter(Export, { name: 'Export' });
