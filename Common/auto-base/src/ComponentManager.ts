/* eslint-disable @typescript-eslint/no-explicit-any */
import { set, get } from 'lodash';

export type ComponentMapType = { [name: string]: React.ComponentType & any };

export type ComponentOptions = {
  name: string;
};

export type RenderManagerComponentType = {
  lazy(): Promise<{ default: ComponentMapType }>;
};

export class ComponentManager {
  private suspenseFallbackComponent: React.ComponentType & any;
  private errorBoundaryComponent: React.ComponentType & any;
  private components: ComponentMapType = {};
  private lazyComponents: RenderManagerComponentType[] = [];

  // 设置加载中组件
  setSuspenseFallbackComponent(component: React.ComponentType) {
    this.suspenseFallbackComponent = component;
  }

  getSuspenseFallbackComponent() {
    return this.suspenseFallbackComponent;
  }

  // 设置异常组件
  setErrorBoundaryComponent(component: React.ComponentType) {
    this.errorBoundaryComponent = component;
  }

  getErrorBoundaryComponent() {
    return this.errorBoundaryComponent;
  }

  // 注册组件
  registor(
    component: React.ComponentType,
    options?: ComponentOptions | string
  ) {
    let name = component?.displayName;
    if (options) {
      if (typeof options === 'string') {
        name = options;
      } else if (options.name) {
        name = options.name;
      }
    }
    if (name) {
      set(this.components, name, component);
    } else {
      throw new Error('Component not set name');
    }
  }

  // 同步获取组件
  getComponent(name: string) {
    return get(this.components, name);
  }

  // 添加异步组件
  addLazyComponent(lazyComponent: RenderManagerComponentType) {
    this.lazyComponents.push(lazyComponent);
  }

  // 异步获取组件
  async getLazyComponent(name: string) {
    const componentList = await Promise.all(
      this.lazyComponents.map(({ lazy }) => lazy().then((v) => v.default))
    );
    for (let i = 0; i < componentList.length; i++) {
      const components = componentList[i];
      const component = get(components, name);
      if (component) {
        return component;
      }
    }
    return undefined;
  }

  // 异步的组件一般是页面单独引用的，所以需要有清除机制
  clearLazyComponents() {
    this.lazyComponents = [];
  }
}

export const manager = new ComponentManager();
export default manager;
