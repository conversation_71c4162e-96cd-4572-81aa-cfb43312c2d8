/* eslint-disable */

import { IV } from '../type';

/**
 * 生成uuid
 */
export const uuid = () => {
  const s = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  const lefthand: any = s[19];
  s[19] = hexDigits.substr((lefthand & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  const uuidStr = s.join('');
  return uuidStr;
};

export const parseFormValue = (valueMap?: IV) => {
  if (!valueMap) {
    return;
  }
  let result: IV = {};
  Object.keys(valueMap).forEach(key => {
    const value = valueMap[key];
    if (key.includes('.')) {
      const keys = key.split('.');
      let len = keys.length;
      let temp = value;
      while (len--) {
        const _key = keys[len];
        temp = { [_key]: temp };
        // 遍历到第一项的时候，需要做下浅层合并，注意！这里只能合并第一级数据
        if (len === 0) {
          const itemValue = result[_key];
          result[_key] = { ...itemValue, ...temp[_key] };
        }
      }
    } else {
      result[key] = value;
    }

    //  如果value 是对象，递归查询一次
    if (value && !Array.isArray(value) && typeof value === 'object') {
      result[key] = parseFormValue(value);
    }
  });
  return result;
};
