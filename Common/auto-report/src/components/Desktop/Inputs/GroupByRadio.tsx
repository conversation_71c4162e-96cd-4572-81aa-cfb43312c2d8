/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Radio,
} from 'antd';
import { isVoid } from '@mtfe/next-biz/src/utils/types';
import {
  GroupByRadioFilter,
  Filter,
  Option,
  InputType,
  FilterProps,
  Context,
} from '../../../types/filter';

type Value = number | string | null;

export default class GroupByRadio implements InputType {
  config: GroupByRadioFilter;

  options?: Option<Value[] | Value>[];

  constructor(filter: Filter, context: Context) {
    this.config = filter as GroupByRadioFilter;
    this.options = this.config.options.filter((o) => {
      if (o.loginType === 'chain') {
        return context.userService?.isHeadOffice();
      } else if (o.loginType === 'poi') {
        return !context.userService?.isHeadOffice();
      } else {
        return true;
      }
    });
  }

  getSelectedOption(options: Option<Value[] | Value>[], value?: Value[] | Value) {
    return options.find((o) => {
      if (Array.isArray(o.value) && Array.isArray(value) && o.value.length === value.length) {
        return o.value.every(v => value.includes(v));
      } else if (Array.isArray(value) && !Array.isArray(o.value) && value.length === 1) {
        return value.includes(o.value);
      }
      return value === o.value;
    });
  }

  render(props: FilterProps) {
    const { otherProps, moduleClick } = props;

    const { onChange } = otherProps;

    const newOnChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
      const v = e.target.value;
      if (!onChange) return;
      if (Array.isArray(v)) {
        onChange(v);
      } else {
        onChange([v]);
      }
      if (!moduleClick) return;
      const option = this.options?.find(o => o.value === v);
      if (!option || !option.bid) return;
      moduleClick(option.bid);
    }, [onChange, this.options, moduleClick]);

    const children = this.options?.map(option => (
      <Radio.Button
        key={option.label}
        value={option.value}
      >
        {option.label}
      </Radio.Button>
    ));

    const selectedOption = React.useMemo(() => this.getSelectedOption(this.options || [], otherProps.value), [otherProps.value, this.options]);
    const value = selectedOption ? selectedOption.value : this.getDefaultValue();

    return (
      <Radio.Group size={props.size} {...otherProps} onChange={newOnChange} value={value}>
        {children}
      </Radio.Group>
    );
  }

  getDefaultValue() {
    if ('defaultValue' in this.config) {
      return this.config.defaultValue;
    }

    const option = this.options ? this.options[0] : undefined;
    const { value } = option || {};

    return value;
  }

  getValue(v?: Value[]) {
    const result = v || this.getDefaultValue();
    return result;
  }

  getDisplayValue(value: Value[]) {
    const options = this.config.options;
    if (!options) return null;
    const selectedOption = this.getSelectedOption(options, value);
    return selectedOption?.label;
  }

  getQuery(value: Value[] | Value, result: { groupBy?: Value[] }) {
    let groupBy = result?.groupBy || [];
    groupBy = groupBy.slice();
    if (!Array.isArray(value)) {
      value = [value];
    }
    value.forEach((v) => {
      if (!groupBy.includes(v) && !isVoid(v)) {
        groupBy.push(v);
      }
    });
    return {
      groupBy,
    };
  }
}
