/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { doAction } from '@mtfe/next-biz/src/lib/actions';
import { throttleFn } from '@mtfe/next-biz/src/utils/throttle';
import { Row } from './types';
import {
  TableColumnConfig, DataNode,
} from '../../../../../types';
import { RequestManager } from '../../../../../types/context';
import { transformNodeForCrossTable } from './useRows';

// function shallowCompare(a: any, b: any) {
//   if (a === b) return true;
//   if (!a && !b) return true;
//   if (!a || !b) return false;
//   const aKeys = Object.keys(a);
//   const bKeys = Object.keys(b);
//   if (aKeys.length !== bKeys.length) return false;
//   return aKeys.every((k: string) => {
//     if (k in b) {
//       return a[k] === b[k];
//     } else {
//       return false;
//     }
//   });
// }

export default function useTotalRow(
  rows: Row[] | null,
  columns: TableColumnConfig[],
  isCrossTable: boolean,
  requestManager: RequestManager | undefined,
  showIndex: boolean | undefined,
): [Row[] | null, DataNode | null] {
  const [totalRow, updateTotalRow] = React.useState<Row | null>(null);
  const [totalRoot, updateTotalRoot] = React.useState<DataNode | null>(null);
  const {
    query, doRequest, rootNode,
  } = requestManager || {};

  const submitKey = React.useRef('');

  /**
   * groupBy 合计行的 groupBy 值
   * totalColumn 合计行所在的列，一般都为指标列
   */
  const { groupBy, totalColumn } = React.useMemo(() => {
    if (!isCrossTable) return { groupBy: null, totalColumn: null };
    let _groupBy: string[] = [];
    let _totalColumn: TableColumnConfig | undefined;
    const groupDims = rootNode?.groupDims;
    const iter = (_columns: TableColumnConfig[]) => {
      _columns.forEach((column) => {
        if (column.children && column.children.length) {
          if (column.field) {
            if (!column.isDim || !groupDims || column.field in groupDims) {
              if (!totalColumn) _totalColumn = column;
              _groupBy.push(column.field);
            }
          }
          iter(column.children);
        }
      });
    };
    _groupBy = _groupBy.filter(Boolean);
    iter(columns);
    return {
      groupBy: _groupBy.length ? _groupBy : null,
      totalColumn: _totalColumn,
    };
  }, [columns, isCrossTable, rootNode]);

  const transformTotalRow = React.useCallback((values: any) => {
    const row: Row = {};
    row.isTotalRow = true;
    const firstColumn = columns[0];
    let key: string | undefined;
    if (showIndex) {
      key = 'rootIndex';
    } else if (firstColumn) {
      key = firstColumn.field;
    }
    if (key) {
      row[key] = {
        data: '合计',
        rowSpan: 1,
        colSpan: 1,
        bold: true,
      };
    }
    Object.entries(values).forEach(([k, v]) => {
      row[k] = {
        data: v,
        rowSpan: 1,
        colSpan: 1,
        bold: true,
      };
    });
    columns.forEach((column) => {
      if (!row[column.field || '']) {
        row[column.field || ''] = {
          data: undefined,
          rowSpan: 1,
          colSpan: 1,
          bold: true,
        };
      }
    });
    return row;
  }, [showIndex, columns]);

  const refreshTotalRow = React.useMemo(() => {
    const fn = async (_query: any, key?: string) => {
      updateTotalRoot(null);
      updateTotalRow(null);
      if (!groupBy || !totalColumn) return;
      if (!doRequest) return;
      const root = await doAction(doRequest({
        ..._query,
        orderBy: undefined,
        orderByType: undefined,
        groupBy,
        isTotalRequest: true, // 区分是否是合计的请求
      }));
      if (!root) return;
      if (submitKey.current === key) {
        const [node] = transformNodeForCrossTable(root, [totalColumn], true);
        updateTotalRow(transformTotalRow({ ...node.aggr, ...node.groupDims, ...node.values }));
        updateTotalRoot(root);
      }
    };
    return throttleFn(fn, 10);
  }, [groupBy, totalColumn, transformTotalRow, updateTotalRow, doRequest]);

  React.useEffect(() => {
    if (!isCrossTable) return;
    const key = Math.random().toString(36).slice(2);
    submitKey.current = key;
    if (!query || !rootNode || !rootNode.items?.length) {
      updateTotalRow(null);
      updateTotalRoot(null);
      return;
    }
    const q = {
      ...query, groupBy: undefined, pageSize: 100, pageNo: 1,
    };
    refreshTotalRow(q, key);
  }, [query, updateTotalRow, refreshTotalRow, rootNode, isCrossTable, updateTotalRoot]);

  return React.useMemo(() => {
    if (!totalRow) return [rows, totalRoot];
    return [[...(rows as []), totalRow], totalRoot];
  }, [rows, totalRow, totalRoot]);
}
