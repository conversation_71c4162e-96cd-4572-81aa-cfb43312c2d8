import React from 'react';
import {
  Tooltip,
  Icon,
} from 'antd';
import QueryContainerContext from './context';
import './QueryItem.less';
import { registorComponent } from '../registor';
import { QueryItemProps } from './types';
import {
  Props,
} from '../../../types';
import ReportComponent from '../../ReportComponent';

export default function QueryItem(props: Props<QueryItemProps>) {
  const { bid, moduleClick, error } = props;
  const containerContext = React.useContext(QueryContainerContext);

  const visiable = containerContext?.visiable(props.index);

  const style: React.CSSProperties = {};

  if (!visiable) {
    style.display = 'none';
  }

  let label: React.ReactNode = null;

  if (props.label) {
    const className = ['auto-report-query-item-label', props.size].filter(Boolean).join(' ');
    if (props.help) {
      label = (
        <Tooltip
          /* eslint-disable-next-line react/no-danger */
          title={<div dangerouslySetInnerHTML={{ __html: props.help }} />} className={className}
        >
          {props.label} <Icon type="question-circle" />
        </Tooltip>
      );
    } else {
      label = (
        <span className={className}>{props.label}</span>
      );
    }
  }

  const onClick = React.useCallback(() => {
    moduleClick && moduleClick(bid);
  }, [moduleClick, bid]);

  let errrorDom: React.ReactNode = null;
  if (error) {
    errrorDom = (
      <div className="auto-report-query-item-error">
        {error}
      </div>
    );
  }

  return (
    <div className="auto-report-query-item" style={style} onClick={onClick}>
      {label}
      <div className="auto-report-query-item-content">
        <ReportComponent>{props.children}</ReportComponent>
        {errrorDom}
      </div>
    </div>
  );
}

registorComponent('QueryItem', QueryItem);
