/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Checkbox,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import {
  CheckboxFilter,
  Filter,
  InputType,
  FilterProps,
} from '../../../types/filter';

export default class implements InputType {
  config: CheckboxFilter;

  constructor(filter: Filter) {
    this.config = filter as CheckboxFilter;
  }

  showLabel() {
    return false;
  }

  getDefaultValue() {
    return this.config.defaultValue || false;
  }

  render(props: FilterProps) {
    const { otherProps } = props;

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const onChange = React.useCallback((e: CheckboxChangeEvent) => {
      const _onChange = props.otherProps?.onChange;
      if (!_onChange) return;
      if (e.target.checked) {
        _onChange(true);
      } else {
        _onChange(false);
      }
    }, [props.otherProps.onChange]);

    const checked = otherProps.value;

    return (
      <Checkbox size={props.size} {...otherProps} checked={checked} onChange={onChange}>
        {this.config.label}
      </Checkbox>
    );
  }

  getDisplayValue(value: boolean) {
    if (value) {
      return '是';
    } else {
      return '否';
    }
  }
}
